#image: atlassian/default-image:3
options:
  docker: true
# aliases:
#   - &version-stages |
#       export desired_version=bumPatch

# aliases:
#   - &initialize-env |
#       export environment=demo

definitions:
  steps:
    - step: &build-push-demo
        name: build and push
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.6.3-jdk11-gcloud441 #this image has kubectl,gcloud helm command and maven3.6.3
        caches:
          - maven
        script:
          - mvn clean install #build the maven project
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - echo $VERSION
          - export IMAGE_NAME=asia-south1-docker.pkg.dev/$GCP_PROJECT_ID_DEMO/$IMAGE_REPO_DEMO/backend:$VERSION  #Declaring backend image variable
          - echo $GCLOUD_KEYFILE_DEMO > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json #gcloud sdk authentication using sa key
          - gcloud config set project $GCP_PROJECT_ID_DEMO
          - gcloud auth configure-docker asia-south1-docker.pkg.dev #login gcp artifact registry
          # BUILD IMAGE
          - docker build -t $IMAGE_NAME .
          # PUBLISH IMAGE
          - docker push $IMAGE_NAME

    - step: &deploy-demo
        name: deploy
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.6.3-jdk11-gcloud441
        script:
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - sed -i "s/image_tag/$VERSION/g" "./pem-deployment-gcp/values-demo.yaml"
          - sed -i "s/aws_access_key/$AWS_ACCESS_KEY/g" "./pem-deployment-gcp/values-demo.yaml"
          - sed -i "s/aws_secret_key/$AWS_SECRET_KEY/g" "./pem-deployment-gcp/values-demo.yaml"
          - sed -i "s/bitbucket-db-password/$bitbucket_db_password_demo/g" "./pem-deployment-gcp/values-demo.yaml"
          - echo $GCLOUD_KEYFILE_DEMO > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json
          - gcloud config set project $GCP_PROJECT_ID_DEMO
          - gcloud auth configure-docker asia-south1-docker.pkg.dev
          - gcloud container clusters get-credentials $GCP_CLUSTER_DEMO --zone=$GCP_ZONE_DEMO --project $GCP_PROJECT_ID_DEMO #authentication with gke cluster
          - helm upgrade -i pembackend -f ./pem-deployment-gcp/values-demo.yaml  ./pem-deployment-gcp --namespace demo

    - step: &build-push-uat
        name: build and push
        image: asia-south1-docker.pkg.dev/master-projects-399408/base-image/taxgenie-maven-gcloud:1.0.0 #this image has kubectl,gcloud helm command and maven3.6.3
        caches:
          - maven
        script:
          - mvn clean install #build the maven project
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - echo $VERSION
          - export IMAGE_NAME=asia-south1-docker.pkg.dev/$GCP_PROJECT_ID_DEMO/$IMAGE_REPO_DEMO/backend:$VERSION  #Declaring backend image variable
          - echo $GCLOUD_KEYFILE_DEMO > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json #gcloud sdk authentication using sa key
          - gcloud config set project $GCP_PROJECT_ID_DEMO
          - gcloud auth configure-docker asia-south1-docker.pkg.dev #login gcp artifact registry
          # BUILD IMAGE
          - docker build -t $IMAGE_NAME .
          # PUBLISH IMAGE
          - docker push $IMAGE_NAME

    -  step: &deploy-uat
        name: deploy
        image: asia-south1-docker.pkg.dev/master-projects-399408/base-image/taxgenie-maven-gcloud:1.0.0
        script:
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - sed -i "s/image_tag/$VERSION/g" "./pem-deployment-gcp/values-uat.yaml"
          - sed -i "s/aws_access_key/$AWS_ACCESS_KEY/g" "./pem-deployment-gcp/values-uat.yaml"
          - sed -i "s/aws_secret_key/$AWS_SECRET_KEY/g" "./pem-deployment-gcp/values-uat.yaml"
          - sed -i "s/bitbucket-db-password/$bitbucket_db_password/g" "./pem-deployment-gcp/values-uat.yaml"
          - echo $GCLOUD_KEYFILE_DEMO > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json
          - gcloud config set project $GCP_PROJECT_ID_DEMO
          - gcloud auth configure-docker asia-south1-docker.pkg.dev
          - gcloud container clusters get-credentials $GCP_CLUSTER_DEMO --zone=$GCP_ZONE_DEMO --project $GCP_PROJECT_ID_DEMO #authentication with gke cluster
          - helm upgrade -i pembackend -f ./pem-deployment-gcp/values-uat.yaml  ./pem-deployment-gcp

    - step: &build-push-prod
        name: build and push
        image: asia-south1-docker.pkg.dev/master-projects-399408/base-image/taxgenie-maven-gcloud:1.0.0 #this image has kubectl,gcloud helm command and maven3.6.3
        caches:
          - maven
        script:
          - mvn clean install #build the maven project
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - echo $VERSION
          - export IMAGE_NAME=asia-south1-docker.pkg.dev/$GCP_PROJECT_ID_MASTER/$IMAGE_REPO_MASTER/backend:$VERSION  #Declaring backend image variable
          - echo $GCLOUD_KEYFILE_MASTER > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json #gcloud sdk authentication using sa key
          - gcloud config set project $GCP_PROJECT_ID_MASTER
          - gcloud auth configure-docker asia-south1-docker.pkg.dev #login gcp artifact registry
          # BUILD IMAGE
          - docker build -t $IMAGE_NAME .
          # PUBLISH IMAGE
          - docker push $IMAGE_NAME

    -  step: &deploy-prod
         name: deploy
         image: asia-south1-docker.pkg.dev/master-projects-399408/base-image/taxgenie-maven-gcloud:1.0.0
         script:
           - chmod +x ./get_version.sh
           - /bin/bash get_version.sh     #Geting version of maven project
           - VERSION=$(cat ver.txt)
           - sed -i "s/image_tag/$VERSION/g" "./pem-deployment-gcp/values-prod.yaml"
           - sed -i "s/aws_access_key/$AWS_ACCESS_KEY/g" "./pem-deployment-gcp/values-prod.yaml"
           - sed -i "s/aws_secret_key/$AWS_SECRET_KEY/g" "./pem-deployment-gcp/values-prod.yaml"
           - sed -i "s/bitbucket-db-password-prod/$bitbucket_db_password_prod/g" "./pem-deployment-gcp/values-prod.yaml"
           - echo $GCLOUD_KEYFILE_MASTER > ~/.gcloud-key.json
           - gcloud auth activate-service-account --key-file ~/.gcloud-key.json
           - gcloud config set project $GCP_PROJECT_ID_MASTER
           - gcloud auth configure-docker asia-south1-docker.pkg.dev
           - gcloud container clusters get-credentials $GCP_CLUSTER_MASTER --zone=$GCP_ZONE_MASTER --project $GCP_PROJECT_ID_MASTER #authentication with gke cluster
           - helm upgrade -i pembackend -f ./pem-deployment-gcp/values-prod.yaml  ./pem-deployment-gcp

pipelines:
  tags:
    demo-*:
      - step:
          caches:
            - maven
          <<: *build-push-demo
          name: Build and Push [Demo]
      - step:
          <<: *deploy-demo
          name: Deploy apps [Demo]
  branches:
#    uat-gcp:
#      - step:
#          <<: *build-push-uat    #calling the chunks of code which is defined above in &build-push-demo
#          name: Build and Push [Uat]
#      - step:
#          <<: *deploy-uat
#          name: Deploy apps [Uat]

    master-gcp:
      - step:
          <<: *build-push-prod    #calling the chunks of code which is defined above in &build-push-demo
          name: Build and Push [Prod]
      - step:
          <<: *deploy-prod
          name: Deploy apps [Prod]

