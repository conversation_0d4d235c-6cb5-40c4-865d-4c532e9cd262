CREATE DEFINER=`root`@`%` TRIGGER `expense_rule_BEFORE_DELETE` BEFORE DELETE ON `expense_rule` FOR EACH ROW BEGIN
INSERT INTO expense_rule_history (
	  operation,
      timestamp,
      updating_user,
      expense_rule_id,
      old_branch_code,
      old_can_exceed_limit,
      old_company_code,
      old_cost_center_code,
      old_created_timestamp,
      old_creating_user_id,
      old_department_code,
      old_employee_grade,
      old_employee_type,
      old_end_date,
      old_expense_subgroup_id,
      old_invoice_required_threshold,
      old_is_frozen,
      old_is_invoice_required,
      old_is_per_diem_allowed,
      old_is_unit_rate_applicable,
      old_limit_amount,
      old_limit_days,
      old_location_category,
      old_maximum_amount,
      old_per_diem_amount,
      old_standard_deduction_rate,
      old_start_date,
      old_unit_of_measure,
      old_unit_rate,
      old_unit_rate_type,
      new_branch_code,
      new_can_exceed_limit,
      new_company_code,
      new_cost_center_code,
      new_created_timestamp,
      new_creating_user_id,
      new_department_code,
      new_employee_grade,
      new_employee_type,
      new_end_date,
      new_expense_subgroup_id,
      new_invoice_required_threshold,
      new_is_frozen,
      new_is_invoice_required,
      new_is_per_diem_allowed,
      new_is_unit_rate_applicable,
      new_limit_amount,
      new_limit_days,
      new_location_category,
      new_maximum_amount,
      new_per_diem_amount,
      new_standard_deduction_rate,
      new_start_date,
      new_unit_of_measure,
      new_unit_rate,
      new_unit_rate_type
	) VALUES (
    	"DELETE",
      CURRENT_TIMESTAMP,
      OLD.updating_user_id,
      OLD.id,
      OLD.branch_code,
      OLD.can_exceed_limit,
      OLD.company_code,
      OLD.cost_center_code,
      OLD.created_timestamp,
      OLD.creating_user_id,
      OLD.department_code,
      OLD.employee_grade,
      OLD.employee_type,
      OLD.end_date,
      OLD.expense_subgroup_id,
      OLD.invoice_required_threshold,
      OLD.is_frozen,
      OLD.is_invoice_required,
      OLD.is_per_diem_allowed,
      OLD.is_unit_rate_applicable,
      OLD.limit_amount,
      OLD.limit_days,
      OLD.location_category,
      OLD.maximum_amount,
      OLD.per_diem_amount,
      OLD.standard_deduction_rate,
      OLD.start_date,
      OLD.unit_of_measure,
      OLD.unit_rate,
      OLD.unit_rate_type,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL,
      NULL
    );
END