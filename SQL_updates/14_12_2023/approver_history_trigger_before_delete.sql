CREATE DEFINER=`root`@`%` T<PERSON><PERSON><PERSON><PERSON> `approver_B<PERSON><PERSON><PERSON>_DELETE` BEFORE DELETE ON `approver` FOR EACH ROW BEGIN
INSERT INTO approver_history (
		operation,
        timestamp,
        updating_user,
        approver_id,
        old_approval_match_value,
        old_approval_matcher,
        old_approval_title,
        old_approver,
        old_channel,
        old_company_code,
        old_created_timestamp,
        old_creating_user_id,
        old_is_frozen,
		new_approval_match_value,
        new_approval_matcher,
        new_approval_title,
        new_approver,
        new_channel,
        new_company_code,
        new_created_timestamp,
        new_creating_user_id,
        new_is_frozen
    ) VALUES (
		"DELETE",
        CURRENT_TIMESTAMP,
		SESSION_USER(),
        OLD.id,
		OLD.approval_match_value,
        OLD.approval_matcher,
        OLD.approval_title,
        OLD.approver,
        OLD.channel,
        OLD.company_code,
        OLD.created_timestamp,
        OLD.creating_user_id,
        OLD.is_frozen,
		NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
    );

END