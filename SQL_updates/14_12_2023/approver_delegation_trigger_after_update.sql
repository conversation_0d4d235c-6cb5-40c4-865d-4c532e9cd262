CREATE DEFINER=`root`@`%` TRI<PERSON><PERSON><PERSON> `approval_delegation_AFTER_UPDATE` AFTER UPDATE ON `approval_delegation` FOR EACH ROW BEGIN
	INSERT INTO approval_delegation_history (
		operation,
        timestamp,
        updating_user,
        approval_delegation_id,
        old_assign_unapproved,
        old_assigned_to,
        old_company_code,
        old_created_timestamp,
        old_creating_user_id,
        old_end_date,
        old_is_frozen,
        old_remarks,
        old_start_date,
        old_originator,
	    new_assign_unapproved,
        new_assigned_to,
        new_company_code,
        new_created_timestamp,
        new_creating_user_id,
        new_end_date,
        new_is_frozen,
        new_remarks,
        new_start_date,
        new_originator
    ) VALUES (
		"UPDATE",
        CURRENT_TIMESTAMP,
		NEW.updating_user_id,
        OLD.id,
        OLD.assign_unapproved,
        OLD.assigned_to,
        OLD.company_code,
        OLD.created_timestamp,
        OLD.creating_user_id,
        OLD.end_date,
        OLD.is_frozen,
        OLD.remarks,
        OLD.start_date,
        OLD.originator,
	    NEW.assign_unapproved,
        NEW.assigned_to,
        NEW.company_code,
        NEW.created_timestamp,
        NEW.creating_user_id,
        NEW.end_date,
        NEW.is_frozen,
        NEW.remarks,
        NEW.start_date,
        NEW.originator
    );
END