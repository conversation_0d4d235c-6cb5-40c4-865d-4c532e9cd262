CREATE DEFINER=`root`@`%` TRIGGER `location_BEFORE_DELETE` BEFORE DELETE ON `location` FOR EACH ROW BEGIN
	INSERT INTO location_history (
		operation,
        timestamp,
        updating_user,
        location_id,
        old_category,
        old_company_code,
        old_country_code,
        old_created_timestamp,
        old_creating_user_id,
        old_description,
        old_is_frozen,
        old_location,
		new_category,
        new_company_code,
        new_country_code,
	    new_created_timestamp,
        new_creating_user_id,
        new_description,
        new_is_frozen,
        new_location
    ) VALUES (
		"DELETE",
        CURRENT_TIMESTAMP,
		OLD.updating_user_id,
        OLD.id,
        OLD.category,
        OLD.company_code,
        OLD.country_code,
        OLD.created_timestamp,
        OLD.creating_user_id,
        OLD.description,
        OLD.is_frozen,
        OLD.location,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
    );
END