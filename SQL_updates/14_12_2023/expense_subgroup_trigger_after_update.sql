CREATE DEFINER=`root`@`%` TRIGGER `expense_subgroup_AFTER_UPDATE` AFTER UPDATE ON `expense_subgroup` FOR EACH ROW BEGIN
INSERT INTO expense_subgroup_history (
		operation,
        timestamp,
        updating_user,
        expense_subgroup_id,
        old_company_code,
        old_created_timestamp,
        old_creating_user_id,
        old_description,
        old_expense_code,
        old_expense_genre,
        old_expense_genre_prefix,
        old_expense_metadata_id,
        old_expense_subgroup,
        old_expense_subgroup_prefix,
        old_frequency,
        old_gl_account_code,
        old_is_date_range_applicable,
        old_is_destination_location_applicable,
        old_is_expense_identifier_applicable,
        old_is_frozen,
        old_is_gst_entry_allowed,
        old_is_location_required,
        old_is_source_location_applicable,
        old_is_standard_deduction_applicable,
        old_merchant_required,
        old_applicable_gender,
        old_is_mobility_descriptor_applicable,
        old_is_transport_descriptor_applicable,
        old_is_travel_descriptor_applicable,
        old_rules_count,
		new_company_code,
        new_created_timestamp,
        new_creating_user_id,
        new_description,
        new_expense_code,
        new_expense_genre,
        new_expense_genre_prefix,
        new_expense_metadata_id,
        new_expense_subgroup,
        new_expense_subgroup_prefix,
        new_frequency,
        new_gl_account_code,
        new_is_date_range_applicable,
        new_is_destination_location_applicable,
        new_is_expense_identifier_applicable,
        new_is_frozen,
        new_is_gst_entry_allowed,
        new_is_location_required,
        new_is_source_location_applicable,
        new_is_standard_deduction_applicable,
        new_merchant_required,
        new_applicable_gender,
        new_is_mobility_descriptor_applicable,
        new_is_transport_descriptor_applicable,
        new_is_travel_descriptor_applicable,
        new_rules_count
	) VALUES (
		"UPDATE",
        CURRENT_TIMESTAMP,
		NEW.updating_user_id,
        OLD.id,
        OLD.company_code,
        OLD.created_timestamp,
        OLD.creating_user_id,
        OLD.description,
        OLD.expense_code,
        OLD.expense_genre,
        OLD.expense_genre_prefix,
        OLD.expense_metadata_id,
        OLD.expense_subgroup,
        OLD.expense_subgroup_prefix,
        OLD.frequency,
        OLD.gl_account_code,
        OLD.is_date_range_applicable,
        OLD.is_destination_location_applicable,
        OLD.is_expense_identifier_applicable,
        OLD.is_frozen,
        OLD.is_gst_entry_allowed,
        OLD.is_location_required,
        OLD.is_source_location_applicable,
        OLD.is_standard_deduction_applicable,
        OLD.merchant_required,
        OLD.applicable_gender,
        OLD.is_mobility_descriptor_applicable,
        OLD.is_transport_descriptor_applicable,
        OLD.is_travel_descriptor_applicable,
        OLD.rules_count,
        NEW.company_code,
        NEW.created_timestamp,
        NEW.creating_user_id,
        NEW.description,
        NEW.expense_code,
        NEW.expense_genre,
        NEW.expense_genre_prefix,
        NEW.expense_metadata_id,
        NEW.expense_subgroup,
        NEW.expense_subgroup_prefix,
        NEW.frequency,
        NEW.gl_account_code,
        NEW.is_date_range_applicable,
        NEW.is_destination_location_applicable,
        NEW.is_expense_identifier_applicable,
        NEW.is_frozen,
        NEW.is_gst_entry_allowed,
        NEW.is_location_required,
        NEW.is_source_location_applicable,
        NEW.is_standard_deduction_applicable,
        NEW.merchant_required,
        NEW.applicable_gender,
        NEW.is_mobility_descriptor_applicable,
        NEW.is_transport_descriptor_applicable,
        NEW.is_travel_descriptor_applicable,
        NEW.rules_count
    );
END