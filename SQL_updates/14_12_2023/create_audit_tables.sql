
CREATE TABLE `location_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `operation` varchar(10),
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updating_user` varchar(25) DEFAULT NULL,
  `location_id` bigint,
  `old_category` varchar(255) DEFAULT NULL,
  `old_company_code` bigint NOT NULL,
  `old_country_code` varchar(255) DEFAULT NULL,
  `old_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `old_creating_user_id` bigint,
  `old_description` varchar(255) DEFAULT NULL,
  `old_is_frozen` bit(1) NOT NULL,
  `old_location` varchar(255) DEFAULT NULL,
  `new_category` varchar(255) DEFAULT NULL,
  `new_company_code` bigint DEFAULT NULL,
  `new_country_code` varchar(255) DEFAULT NULL,
  `new_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `new_creating_user_id` bigint,
  `new_description` varchar(255) DEFAULT NULL,
  `new_is_frozen` bit(1) DEFAULT NULL,
  `new_location` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT fk_location_id FOREIGN KEY (`location_id`) REFERENCES location(id) ON DELETE SET NULL
);

CREATE TABLE `approver_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `operation` varchar(10),
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updating_user` varchar(25) DEFAULT NULL,
  `approver_id` bigint,
  `old_approval_match_value` varchar(255) DEFAULT NULL,
  `old_approval_matcher` varchar(255) DEFAULT NULL,
  `old_approval_title` varchar(255) DEFAULT NULL,
  `old_approver` varchar(255) DEFAULT NULL,
  `old_channel` int DEFAULT NULL,
  `old_company_code` bigint DEFAULT NULL,
  `old_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `old_creating_user_id` bigint DEFAULT NULL,
  `old_is_frozen` bit(1) DEFAULT NULL,
  `new_approval_match_value` varchar(255) DEFAULT NULL,
  `new_approval_matcher` varchar(255) DEFAULT NULL,
  `new_approval_title` varchar(255) DEFAULT NULL,
  `new_approver` varchar(255) DEFAULT NULL,
  `new_channel` int DEFAULT NULL,
  `new_company_code` bigint DEFAULT NULL,
  `new_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `new_creating_user_id` bigint DEFAULT NULL,
  `new_is_frozen` bit(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT fk_approver_id FOREIGN KEY (`approver_id`) references approver(id) ON DELETE SET NULL
);

CREATE TABLE `approval_delegation_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `operation` varchar(10),
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updating_user` varchar(25) DEFAULT NULL,
  `approval_delegation_id` bigint,
  `old_assign_unapproved` bit(1) NULL,
  `old_assigned_to` varchar(255) DEFAULT NULL,
  `old_company_code` bigint NULL,
  `old_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `old_creating_user_id` bigint NULL,
  `old_end_date` date DEFAULT NULL,
  `old_is_frozen` bit(1) NULL,
  `old_remarks` varchar(255) DEFAULT NULL,
  `old_start_date` date DEFAULT NULL,
  `old_originator` varchar(255) DEFAULT NULL,
  `new_assign_unapproved` bit(1) NULL,
  `new_assigned_to` varchar(255) DEFAULT NULL,
  `new_company_code` bigint NULL,
  `new_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `new_creating_user_id` bigint NULL,
  `new_end_date` date DEFAULT NULL,
  `new_is_frozen` bit(1) NULL,
  `new_remarks` varchar(255) DEFAULT NULL,
  `new_start_date` date DEFAULT NULL,
  `new_originator` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT fk_approval_delegation_id FOREIGN KEY (`approval_delegation_id`) references approval_delegation(id) ON DELETE SET NULL
) 

CREATE TABLE `expense_metadata_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `operation` varchar(10),
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updating_user` varchar(25) DEFAULT NULL,
  `expense_metadata_id` bigint,
  `old_company_code` bigint,
  `old_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `old_creating_user_id` bigint,
  `old_description` varchar(255) DEFAULT NULL,
  `old_expense_group` varchar(255) DEFAULT NULL,
  `old_expense_group_prefix` varchar(255) DEFAULT NULL,
  `old_expense_type` varchar(255) DEFAULT NULL,
  `old_expense_type_prefix` varchar(255) DEFAULT NULL,
  `old_is_frozen` bit(1) DEFAULT 0,
  `old_purpose_required` bit(1),
  `old_applicable_gender` varchar(255) DEFAULT NULL,
  `old_limit_days` int DEFAULT NULL,
  `old_definitions_count` int,
  `old_rules_count` int,
  `old_subgroups_count` int,
  `new_company_code` bigint,
  `new_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `new_creating_user_id` bigint,
  `new_description` varchar(255) DEFAULT NULL,
  `new_expense_group` varchar(255) DEFAULT NULL,
  `new_expense_group_prefix` varchar(255) DEFAULT NULL,
  `new_expense_type` varchar(255) DEFAULT NULL,
  `new_expense_type_prefix` varchar(255) DEFAULT NULL,
  `new_is_frozen` bit(1) DEFAULT 0,
  `new_purpose_required` bit(1),
  `new_applicable_gender` varchar(255) DEFAULT NULL,
  `new_limit_days` int DEFAULT NULL,
  `new_definitions_count` int,
  `new_rules_count` int,
  `new_subgroups_count` int,
  PRIMARY KEY (`id`),
  CONSTRAINT fk_expense_metadata_id FOREIGN KEY (`expense_metadata_id`) REFERENCES expense_metadata(id) ON DELETE SET NULL
);

CREATE TABLE `expense_subgroup_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `operation` varchar(10),
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updating_user` varchar(25) DEFAULT NULL,
  `expense_subgroup_id` bigint,
  `old_company_code` bigint,
  `old_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `old_creating_user_id` bigint,
  `old_description` varchar(255) DEFAULT NULL,
  `old_expense_code` varchar(255) DEFAULT NULL,
  `old_expense_genre` varchar(255) DEFAULT NULL,
  `old_expense_genre_prefix` varchar(255) DEFAULT NULL,
  `old_expense_metadata_id` bigint,
  `old_expense_subgroup` varchar(255) DEFAULT NULL,
  `old_expense_subgroup_prefix` varchar(255) DEFAULT NULL,
  `old_frequency` varchar(255) DEFAULT NULL,
  `old_gl_account_code` varchar(255) DEFAULT NULL,
  `old_is_date_range_applicable` bit(1),
  `old_is_destination_location_applicable` bit(1),
  `old_is_expense_identifier_applicable` bit(1),
  `old_is_frozen` bit(1),
  `old_is_gst_entry_allowed` bit(1),
  `old_is_location_required` bit(1),
  `old_is_source_location_applicable` bit(1),
  `old_is_standard_deduction_applicable` bit(1),
  `old_merchant_required` bit(1),
  `old_applicable_gender` varchar(255) DEFAULT NULL,
  `old_is_mobility_descriptor_applicable` bit(1),
  `old_is_transport_descriptor_applicable` bit(1),
  `old_is_travel_descriptor_applicable` bit(1),
  `old_rules_count` int,
  `new_company_code` bigint,
  `new_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `new_creating_user_id` bigint,
  `new_description` varchar(255) DEFAULT NULL,
  `new_expense_code` varchar(255) DEFAULT NULL,
  `new_expense_genre` varchar(255) DEFAULT NULL,
  `new_expense_genre_prefix` varchar(255) DEFAULT NULL,
  `new_expense_metadata_id` bigint,
  `new_expense_subgroup` varchar(255) DEFAULT NULL,
  `new_expense_subgroup_prefix` varchar(255) DEFAULT NULL,
  `new_frequency` varchar(255) DEFAULT NULL,
  `new_gl_account_code` varchar(255) DEFAULT NULL,
  `new_is_date_range_applicable` bit(1),
  `new_is_destination_location_applicable` bit(1),
  `new_is_expense_identifier_applicable` bit(1),
  `new_is_frozen` bit(1),
  `new_is_gst_entry_allowed` bit(1),
  `new_is_location_required` bit(1),
  `new_is_source_location_applicable` bit(1),
  `new_is_standard_deduction_applicable` bit(1),
  `new_merchant_required` bit(1),
  `new_applicable_gender` varchar(255) DEFAULT NULL,
  `new_is_mobility_descriptor_applicable` bit(1),
  `new_is_transport_descriptor_applicable` bit(1),
  `new_is_travel_descriptor_applicable` bit(1),
  `new_rules_count` int,
  PRIMARY KEY (`id`),
  CONSTRAINT fk_expense_subgroup_id FOREIGN KEY (expense_subgroup_id) REFERENCES expense_subgroup (`id`) ON DELETE SET NULL
);


CREATE TABLE `expense_rule_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `operation` varchar(10),
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updating_user` varchar(25) DEFAULT NULL,
  `expense_rule_id` bigint,
  `old_branch_code` varchar(255) DEFAULT NULL,
  `old_can_exceed_limit` bit(1),
  `old_company_code` bigint,
  `old_cost_center_code` varchar(255) DEFAULT NULL,
  `old_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `old_creating_user_id` bigint,
  `old_department_code` varchar(255) DEFAULT NULL,
  `old_employee_grade` varchar(255) DEFAULT NULL,
  `old_employee_type` varchar(255) DEFAULT NULL,
  `old_end_date` date DEFAULT NULL,
  `old_expense_subgroup_id` bigint,
  `old_invoice_required_threshold` double(22,2) DEFAULT NULL,
  `old_is_frozen` bit(1),
  `old_is_invoice_required` bit(1),
  `old_is_per_diem_allowed` bit(1),
  `old_is_unit_rate_applicable` bit(1),
  `old_limit_amount` double(22,2) DEFAULT NULL,
  `old_limit_days` int DEFAULT NULL,
  `old_location_category` varchar(50) DEFAULT NULL,
  `old_maximum_amount` double(22,2) DEFAULT NULL,
  `old_per_diem_amount` double(22,2) DEFAULT NULL,
  `old_standard_deduction_rate` double(22,2) DEFAULT NULL,
  `old_start_date` date DEFAULT NULL,
  `old_unit_of_measure` varchar(255) DEFAULT NULL,
  `old_unit_rate` double(22,2) DEFAULT NULL,
  `old_unit_rate_type` varchar(255) DEFAULT NULL,
  `new_branch_code` varchar(255) DEFAULT NULL,
  `new_can_exceed_limit` bit(1),
  `new_company_code` bigint,
  `new_cost_center_code` varchar(255) DEFAULT NULL,
  `new_created_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `new_creating_user_id` bigint,
  `new_department_code` varchar(255) DEFAULT NULL,
  `new_employee_grade` varchar(255) DEFAULT NULL,
  `new_employee_type` varchar(255) DEFAULT NULL,
  `new_end_date` date DEFAULT NULL,
  `new_expense_subgroup_id` bigint,
  `new_invoice_required_threshold` double(22,2) DEFAULT NULL,
  `new_is_frozen` bit(1),
  `new_is_invoice_required` bit(1),
  `new_is_per_diem_allowed` bit(1),
  `new_is_unit_rate_applicable` bit(1),
  `new_limit_amount` double(22,2) DEFAULT NULL,
  `new_limit_days` int DEFAULT NULL,
  `new_location_category` varchar(50) DEFAULT NULL,
  `new_maximum_amount` double(22,2) DEFAULT NULL,
  `new_per_diem_amount` double(22,2) DEFAULT NULL,
  `new_standard_deduction_rate` double(22,2) DEFAULT NULL,
  `new_start_date` date DEFAULT NULL,
  `new_unit_of_measure` varchar(255) DEFAULT NULL,
  `new_unit_rate` double(22,2) DEFAULT NULL,
  `new_unit_rate_type` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_expense_rule_id` FOREIGN KEY (expense_rule_id) REFERENCES `expense_rule` (`id`) ON DELETE SET NULL
)
