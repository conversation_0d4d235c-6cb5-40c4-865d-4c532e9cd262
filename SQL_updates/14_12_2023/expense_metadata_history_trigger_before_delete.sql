CREATE DEFINER=`root`@`%` TRIG<PERSON>R `expense_metadata_BEFORE_DELETE` BEFORE DELETE ON `expense_metadata` FOR EACH ROW BEGIN
INSERT INTO expense_metadata_history (
		operation,
        timestamp,
        updating_user,
        expense_metadata_id,
        old_company_code,
        old_created_timestamp,
        old_creating_user_id,
        old_description,
        old_expense_group,
        old_expense_group_prefix,
        old_expense_type,
        old_expense_type_prefix,
        old_is_frozen,
        old_purpose_required,
        old_applicable_gender,
        old_limit_days,
        old_definitions_count,
        old_rules_count,
        old_subgroups_count,
		new_company_code,
        new_created_timestamp,
        new_creating_user_id,
        new_description,
        new_expense_group,
        new_expense_group_prefix,
        new_expense_type,
        new_expense_type_prefix,
        new_is_frozen,
        new_purpose_required,
        new_applicable_gender,
        new_limit_days,
        new_definitions_count,
        new_rules_count,
        new_subgroups_count
	) VALUES (
		"DELETE",
        CURRENT_TIMESTAMP,
		OLD.updating_user_id,
        OLD.id,
		OLD.company_code,
        OLD.created_timestamp,
        OLD.creating_user_id,
        OLD.description,
        OLD.expense_group,
        OLD.expense_group_prefix,
        OLD.expense_type,
        OLD.expense_type_prefix,
        OLD.is_frozen,
        OLD.purpose_required,
        OLD.applicable_gender,
        OLD.limit_days,
        OLD.definitions_count,
        OLD.rules_count,
        OLD.subgroups_count,
		NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
    );
END