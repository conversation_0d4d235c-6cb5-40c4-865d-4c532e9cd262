image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.6.3-jdk11-gcloud441

definitions:
  caches: { maven: ~/.m2 }

  steps:
    # 1 ─ build once
    - step: &build
        name: Build JAR + WAR
        script:
          - VERSION="${BITBUCKET_TAG}"
          - mvn clean install
        artifacts:
          - target/pay-expense-pvv.jar
          - target/pay-expense-pvv.war

    # 2 ─ push Docker image (ABFL and ICICI)
    - step: &push-image-abfl
        name: Push Docker image → Artifact Registry
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.6.3-jdk11-gcloud441 #this image has kubectl,gcloud helm command and maven3.6.3
        services: [docker]
        script:
          - VERSION="${BITBUCKET_TAG}"
          - IMAGE="asia-south1-docker.pkg.dev/noble-stratum-393405/pay-expense-abfl/uat/backend:${VERSION}"
          - SAS_IMAGE="asia-south1-docker.pkg.dev/$GCP_PROJECT_ID_DEMO/$IMAGE_REPO_DEMO/backend:$VERSION"
          - echo "$GCLOUD_KEYFILE_DEMO" > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json
          - gcloud config set project "$GCP_PROJECT_ID_DEMO"
          - gcloud auth configure-docker asia-south1-docker.pkg.dev
          - docker build -t "${IMAGE}" --build-arg JAR_FILE=target/pay-expense-pvv.jar .
          - docker build -t "${SAS_IMAGE}" --build-arg JAR_FILE=target/pay-expense-pvv.jar .
          - docker push $IMAGE
          - docker push $SAS_IMAGE
        artifacts:   # keep WAR for next step
          - target/pay-expense-pvv.war

    # 3 ─ upload WAR to a read-only bucket (for Tomcat tenants)
    - step: &upload-war
        name: Upload WAR → GCS
        script:
          - echo $GCLOUD_KEYFILE_DEMO > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json
          - gcloud config set project $GCP_PROJECT_ID_DEMO
          - gcloud auth configure-docker asia-south1-docker.pkg.dev
          - VERSION="${BITBUCKET_TAG}"
          - gsutil cp target/pay-expense-pvv.war "gs://payexpense/payexpense-artifacts/${VERSION}/pem.war"
          # update the “pointer” to latest
          - echo "${VERSION}" | gsutil cp - "gs://payexpense/payexpense-artifacts/latest.txt"

    # 4 ─ package + push Helm chart (ICICI)
    - step: &package-helm
        name: Package & push Helm chart
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.6.3-jdk11-gcloud441
        script:
#          - chmod +x ./get_version.sh
#          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION="${BITBUCKET_TAG}"
          - sed -i "s/image_tag/$VERSION/g" "./pem-deployment-gcp/values-demo.yaml"
          - sed -i "s/aws_access_key/$AWS_ACCESS_KEY/g" "./pem-deployment-gcp/values-demo.yaml"
          - sed -i "s/aws_secret_key/$AWS_SECRET_KEY/g" "./pem-deployment-gcp/values-demo.yaml"
          - sed -i "s/bitbucket-db-password/$bitbucket_db_password_demo/g" "./pem-deployment-gcp/values-demo.yaml"
          - echo $GCLOUD_KEYFILE_DEMO > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json
          - gcloud config set project $GCP_PROJECT_ID_DEMO
          - gcloud auth configure-docker asia-south1-docker.pkg.dev
          - gcloud container clusters get-credentials $GCP_CLUSTER_DEMO --zone=$GCP_ZONE_DEMO --project $GCP_PROJECT_ID_DEMO #authentication with gke cluster
          - helm upgrade -i pembackend -f ./pem-deployment-gcp/values-demo.yaml  ./pem-deployment-gcp --namespace demo

    - step: &fast-test
        name: Build & unit test locally
        runs-on:
          - self.hosted # label you registered
          - macos
        script:
          - echo "I am running on the laptop!"
          - mvn -B clean verify
pipelines:
  tags:
    "**":
#      - step: *fast-test
      - step: *build
      - step: *push-image-abfl
      - step: *upload-war
      - step: *package-helm
