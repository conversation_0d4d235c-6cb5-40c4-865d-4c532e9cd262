<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.9</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>in.taxgenie</groupId>
    <artifactId>pay-expense-pvv</artifactId>
<!--    <version>*******</version>-->
    <version>1.328.0</version>
    <name>${project.name}</name>
    <description>PayExpense backend - TaxGenie</description>
    <properties>
        <java.version>11</java.version>
        <querydsl.version>4.4.0</querydsl.version>
        <sonar.organization>personalpvv</sonar.organization>
        <sonar.host.url>https://sonarcloud.io</sonar.host.url>
        <project.build.sourceEncoding>ASCII</project.build.sourceEncoding>
        <deployment.type>jar</deployment.type>
        <project.name>pay-expense-pvv</project.name>
    </properties>
    <packaging>${deployment.type}</packaging>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>spring-cloud-gcp-dependencies</artifactId>
                <version>3.4.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.28</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <version>2.5.5</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger2 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.springfox/springfox-boot-starter -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger-ui -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.2</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <scope>runtime</scope>
            <version>0.11.2</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <scope>runtime</scope>
            <version>0.11.2</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-s3 -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.12.167</version>
        </dependency>

        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>spring-cloud-gcp-starter-storage</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.codehaus.mojo/exec-maven-plugin -->
        <dependency>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/net.sf.jasperreports/jasperreports -->
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>6.19.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20180130</version>
        </dependency>
        <!-- Font Extensions as many fonts not available in JDK used in Docker -->
        <dependency>
            <groupId>ar.com.fdvs</groupId>
            <artifactId>DynamicJasper-core-fonts</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.5.2</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>4.4.0</version>
        </dependency>

        <!-- Querydsl runtime -->
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <version>${querydsl.version}</version>
        </dependency>

        <!-- Querydsl annotation processor (JPA) -->
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-apt</artifactId>
            <version>${querydsl.version}</version>
            <classifier>jpa</classifier>
            <scope>provided</scope>
        </dependency>

        <!-- Bring back javax.annotation.Generated on Java 11+ -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.pdfbox/pdfbox -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>3.0.5</version>
        </dependency>

</dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>parse-version</id>
                        <goals>
                            <goal>parse-version</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <!-- This brings in the JPAAnnotationProcessor -->
                        <path>
                            <groupId>com.querydsl</groupId>
                            <artifactId>querydsl-apt</artifactId>
                            <version>${querydsl.version}</version>
                            <classifier>jpa</classifier>
                        </path>
                        <!-- The runtime support -->
                        <path>
                            <groupId>com.querydsl</groupId>
                            <artifactId>querydsl-jpa</artifactId>
                            <version>${querydsl.version}</version>
                        </path>
                        <!-- Bring in the javax.annotation.Generated class -->
                        <path>
                            <groupId>javax.annotation</groupId>
                            <artifactId>javax.annotation-api</artifactId>
                            <version>1.3.2</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.8.1</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.3.2</version>
                <executions>
                    <execution>
                        <id>attach-war</id>
                        <phase>package</phase>
                        <goals>
                            <goal>war</goal>
                        </goals>
                        <configuration>
                            <failOnMissingWebXml>false</failOnMissingWebXml>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- https://mvnrepository.com/artifact/org.sonarsource.scanner.maven/sonar-maven-plugin -->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.9.1.2184</version>
            </plugin>
                <!-- Plugin for WAR packaging -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <configuration>
                        <!-- Set the main class to extend SpringBootServletInitializer -->
                        <archive>
                            <manifest>
                                <addClasspath>true</addClasspath>
                                <classpathPrefix>lib/</classpathPrefix>
                                <mainClass>in.taxgenie.pay_expense_pvv.PayExpensePvvApplication</mainClass>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>print-active-profile</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <target>
                                    <echo>Active Profile: ${spring.profiles.active}</echo>
                                </target>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <includes>
                    <include>META-INF/context.xml</include>
                </includes>
                <targetPath>META-INF</targetPath>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>war</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals><goal>repackage</goal></goals>
                            </execution>
                        </executions>
                        <configuration>
                            <packaging>war</packaging>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>build-war</id>
            <activation>
                <property>
                    <name>buildWar</name>
                </property>
            </activation>
            <properties>
                <deployment.type>war</deployment.type>
            </properties>
        </profile>
        <profile>
            <id>deploy-dev</id>
            <activation>
                <property>
                    <name>deployDev</name>
                </property>
            </activation>
            <properties>
                <project.name>pem_dev</project.name>
                <profile.name>dev</profile.name>
            </properties>
            <build>
                <plugins>
                    <!-- Rename the context-test.xml file to context.xml -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>rename-context-file</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <copy file="${project.basedir}/src/main/resources/__logback.xml"
                                              tofile="${project.build.directory}/${project.build.finalName}/WEB-INF/classes/logback.xml"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>deploy-abhfl-uat</id>
            <activation>
                <property>
                    <name>deployAbhflUat</name>
                </property>
            </activation>
            <properties>
                <project.name>pem_abhfl</project.name>
                <profile.name>abhfl-uat</profile.name>
            </properties>
        </profile>
        <profile>
            <id>deploy-test</id>
            <activation>
                <property>
                    <name>deployTest</name>
                </property>
            </activation>
            <properties>
                <project.name>pem_test</project.name>
                <profile.name>test</profile.name>
            </properties>
            <build>
                <plugins>
                    <!-- Other plugins... -->

                    <!-- Copy the context-test.xml file to the META-INF directory -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>3.3.2</version>
                        <configuration>
                            <webResources>
                                <resource>
                                    <directory>${project.basedir}/src/main/webapp/META-INF</directory>
                                    <includes>
                                        <include>context-test.xml</include>
                                    </includes>
                                    <targetPath>META-INF</targetPath>
                                </resource>
                            </webResources>
                        </configuration>
                    </plugin>

                    <!-- Rename the context-test.xml file to context.xml -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>rename-context-file</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <copy file="${project.basedir}/src/main/webapp/META-INF/context-test.xml"
                                              tofile="${project.build.directory}/${project.build.finalName}/META-INF/context.xml"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>bump-patch</id>
            <activation>
                <property>
                    <name>bumpPatch</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>versions-maven-plugin</artifactId>
                        <version>2.8.1</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>set</goal>
                                </goals>
                                <phase>validate</phase>
                                <configuration>
                                    <newVersion>
                                        ${parsedVersion.majorVersion}.${parsedVersion.minorVersion}.${parsedVersion.nextIncrementalVersion}
                                    </newVersion>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>bump-minor</id>
            <activation>
                <property>
                    <name>bumpMinor</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>versions-maven-plugin</artifactId>
                        <version>2.8.1</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>set</goal>
                                </goals>
                                <phase>validate</phase>
                                <configuration>
                                    <newVersion>${parsedVersion.majorVersion}.${parsedVersion.nextMinorVersion}.0
                                    </newVersion>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>bump-major</id>
            <activation>
                <property>
                    <name>bumpMajor</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>versions-maven-plugin</artifactId>
                        <version>2.8.1</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>set</goal>
                                </goals>
                                <phase>validate</phase>
                                <configuration>
                                    <newVersion>${parsedVersion.nextMajorVersion}.0.0</newVersion>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-preprod</id>
            <activation>
                <property>
                    <name>releasePreprod</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>aws-login</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>dockerize-command.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-add</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>add</argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-commit</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>commit</argument>
                                        <argument>-m</argument>
                                        <argument>Before release: ${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-tag</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>tag</argument>
                                        <argument>${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-push-origin</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>push</argument>
                                        <argument>--all</argument>
                                        <argument>origin</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>dockerize</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>build</argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:${project.version}
                                        </argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest
                                        </argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-versioned</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:${project.version}
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-latest</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>deploy</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>ssh-tasks.preprod.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-uat</id>
            <activation>
                <property>
                    <name>releaseUat</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>aws-login</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>dockerize-command.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-add</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>add</argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-commit</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>commit</argument>
                                        <argument>-m</argument>
                                        <argument>Before release: ${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-tag</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>tag</argument>
                                        <argument>${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-push-origin</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>push</argument>
                                        <argument>--all</argument>
                                        <argument>origin</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>dockerize</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>build</argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:${project.version}
                                        </argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest
                                        </argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-versioned</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:${project.version}
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-latest</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>deploy</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>ssh-tasks.uat.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-production</id>
            <activation>
                <property>
                    <name>releaseProduction</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>aws-login</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>dockerize-command.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-add</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>add</argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-commit</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>commit</argument>
                                        <argument>-m</argument>
                                        <argument>Before release: ${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-tag</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>tag</argument>
                                        <argument>${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-push-origin</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>push</argument>
                                        <argument>--all</argument>
                                        <argument>origin</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>dockerize</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>build</argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:${project.version}
                                        </argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest
                                        </argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-versioned</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:${project.version}
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-latest</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>deploy</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>ssh-tasks.prod.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
