package in.taxgenie.pay_expense_pvv.services;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.LocalDate;
import java.util.stream.Stream;

class DummyTest {
    private final Dummy dummy;

    DummyTest() {
        this.dummy = new Dummy();
    }


    @ParameterizedTest
    @MethodSource("getOverlapDateSource")
    void overlapDatesAreDetected(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2){
        Assertions.assertTrue(dummy.isDateRangeOverlap(start1, end1, start2, end2));
    }

    @ParameterizedTest
    @MethodSource("getNonOverlapDateSource")
    void nonOverlapDatesAreExcluded(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2){
        Assertions.assertFalse(dummy.isDateRangeOverlap(start1, end1, start2, end2));
    }

    private static Stream<Arguments> getOverlapDateSource() {
        return Stream.of(
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31)),
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 11, 25),
                        LocalDate.of(2021, 12, 25)),
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 12, 25),
                        LocalDate.of(2022, 1, 25)),
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 11, 25),
                        LocalDate.of(2022, 1, 25)),
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 12, 10),
                        LocalDate.of(2021, 12, 25)),
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2022, 1, 25)),
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 11, 1),
                        LocalDate.of(2021, 12, 31))
        );
    }

    private static Stream<Arguments> getNonOverlapDateSource() {
        return Stream.of(
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 11, 1),
                        LocalDate.of(2021, 11, 30)),
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2022, 1, 25)),
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2021, 10, 25),
                        LocalDate.of(2021, 12, 1)),
                Arguments.arguments(
                        LocalDate.of(2021, 12, 1),
                        LocalDate.of(2021, 12, 31),
                        LocalDate.of(2022, 1, 1),
                        LocalDate.of(2022, 1, 25))
        );
    }
}