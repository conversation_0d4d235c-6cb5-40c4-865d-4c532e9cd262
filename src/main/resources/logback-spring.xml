<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- make Spring properties available -->
    <springProperty scope="context" name="LOG_LOCATION"   source="LOG_LOCATION"/>
    <springProperty scope="context" name="LOG_NAME"       source="LOG_NAME"/>
    <springProperty scope="context" name="APP_LOG_LEVEL"  source="APP_LOG_LEVEL"/>
    <springProperty scope="context" name="ROOT_LOG_LEVEL" source="ROOT_LOG_LEVEL"/>

    <!-- define console once -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %line - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- LOCAL: only console -->
    <springProfile name="local">
        <logger name="in.taxgenie" level="${APP_LOG_LEVEL}" additivity="false">
            <appender-ref ref="CONSOLE"/>
        </logger>
        <root level="${ROOT_LOG_LEVEL}">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <!-- NON-LOCAL: console + file + html -->
    <springProfile name="!local">

        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_LOCATION}/${LOG_NAME}.log</file>
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <Pattern>%d{yyyy-MM-dd HH:mm:ss} - %msg%n</Pattern>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${LOG_LOCATION}/archived/${LOG_NAME}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>10MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
            </rollingPolicy>
        </appender>

        <appender name="HTML" class="ch.qos.logback.core.FileAppender">
            <file>${LOG_LOCATION}/${LOG_NAME}.html</file>
            <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                <layout class="ch.qos.logback.classic.html.HTMLLayout">
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}%thread%level%logger%msg</pattern>
                </layout>
            </encoder>
        </appender>

        <logger name="in.taxgenie" level="${APP_LOG_LEVEL}" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
            <appender-ref ref="HTML"/>
        </logger>

        <root level="${ROOT_LOG_LEVEL}" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
            <appender-ref ref="HTML"/>
        </root>

    </springProfile>

</configuration>