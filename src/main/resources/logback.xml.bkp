<?xml version="1.0" encoding="UTF-8"?>
<configuration >
    <property resource="application.properties" />
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_LOCATION}/${LOG_NAME}.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss} - %msg%n
            </Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            rollover daily
            <fileNamePattern>
                ${LOG_LOCATION}/archived/${LOG_NAME}.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <appender name="htmlAppender" class="ch.qos.logback.core.FileAppender">
        <file>${LOG_LOCATION}/${LOG_NAME}.html</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="ch.qos.logback.classic.html.HTMLLayout">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}%thread%level%logger%msg</pattern>
            </layout>
        </encoder>
    </appender>

    <logger name="in.taxgenie" level="${APP_LOG_LEVEL}" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="htmlAppender"/>
    </logger>

    <logger name="ROOT" level="${ROOT_LOG_LEVEL}" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="htmlAppender"/>
    </logger>

</configuration>
