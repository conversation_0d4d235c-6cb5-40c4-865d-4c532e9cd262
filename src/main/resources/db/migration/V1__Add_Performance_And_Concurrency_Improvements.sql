-- Performance and Concurrency Improvements for Expense Report Submit API
-- This migration adds indexes, constraints, and optimistic locking support

-- 1. Add version column for optimistic locking
ALTER TABLE expense_report ADD COLUMN version BIGINT DEFAULT 0;

-- 2. Add unique constraint to prevent duplicate submissions
-- This constraint ensures that the same saveTime cannot be used twice for the same report
ALTER TABLE report_state ADD CONSTRAINT uk_report_state_save_time 
UNIQUE (company_code, expense_report_id, save_time);

-- 3. Add performance indexes for common query patterns

-- Index for report lookup by company, employee email, and status
CREATE INDEX idx_expense_report_company_employee_status 
ON expense_report(company_code, employee_email, report_status);

-- Index for report state lookup by company, report ID, and save time
CREATE INDEX idx_report_state_lookup 
ON report_state(company_code, expense_report_id, save_time);

-- Index for report state by company, report ID, and status
CREATE INDEX idx_report_state_company_report_status 
ON report_state(company_code, expense_report_id, status);

-- Index for expense report by company and creating user
CREATE INDEX idx_expense_report_company_user 
ON expense_report(company_code, creating_user_id, report_status);

-- Index for expense report by submit date (for queue operations)
CREATE INDEX idx_expense_report_submit_date 
ON expense_report(submit_date, company_code, report_status);

-- Index for expense validation queries
CREATE INDEX idx_expense_report_metadata_user 
ON expense_report(company_code, creating_user_id, expense_metadata_id, report_status);

-- 4. Add constraints for data integrity

-- Ensure report claim amount is non-negative
ALTER TABLE expense_report ADD CONSTRAINT chk_report_claim_amount 
CHECK (report_claim_amount >= 0);

-- Ensure expense claim amount is non-negative
ALTER TABLE expense ADD CONSTRAINT chk_expense_claim_amount 
CHECK (claim_amount >= 0);

-- Ensure end date is not before start date
ALTER TABLE expense_report ADD CONSTRAINT chk_report_date_range 
CHECK (end_date >= start_date);

-- 5. Add audit columns if they don't exist
ALTER TABLE expense_report ADD COLUMN IF NOT EXISTS last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- 6. Create index for audit and monitoring
CREATE INDEX idx_expense_report_last_modified 
ON expense_report(last_modified_date);

-- 7. Add index for report states by level and status (for approval workflow)
CREATE INDEX idx_report_state_level_status 
ON report_state(company_code, expense_report_id, level, status);

-- 8. Add composite index for employee service queries
CREATE INDEX idx_expense_report_employee_lookup 
ON expense_report(company_code, employee_email, employee_code);

-- 9. Update statistics for query optimizer
ANALYZE TABLE expense_report;
ANALYZE TABLE report_state;
ANALYZE TABLE expense;
