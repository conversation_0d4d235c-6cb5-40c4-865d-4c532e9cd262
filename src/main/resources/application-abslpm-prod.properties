#ABSLPM - Prod
spring.datasource.url=**************************************************************************************
spring.datasource.username=admin
spring.datasource.password=admin123

#   AWS S3
aws.region=ap-south-1
aws.endpoint.url=s3://p2p-payexpense-prod/taxgenie_payexpense/
aws.access.key=********************
aws.secret.key=9rqKQc6dhywFUuzN9cu4sPhjywYY7yejH4ML7zQz
aws.bucket.name=p2p-abspm-prod
aws.root.directory=taxgenie_payexpense

PAY_EXPENSE_CUSTOMER_ID=12744
COMMUNICATION_ENGINE_URL=https://payexpense.insideabc.com/com_abslpm
CAM_URL=https://payexpense.insideabc.com/cam_abslpm
CEM_URL=https://payexpense.insideabc.com/cem_abslpm/api/v1

APPROVAL_EMAIL_TEMPLATE_ID=pe-approval-format
ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID=pe-acknowledgement-format
SENDBACK_EMAIL_TEMPLATE_ID=pe-sentback-for-creator
SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID=pe-submit-for-creator
SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID=pe-submit-for-approver
APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID=pe-approved-for-creator
APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID=pe-approved-for-approver
POSTED_EMAIL_TEMPLATE_ID=pe-posted-for-creator
PAID_EMAIL_TEMPLATE_ID=pe-paid-for-creator
NEW_USER_WELCOME_EMAIL_TEMPLATE_ID=pe-new-user-welcome
SENDBACK_REMINDER_EMAIL_TEMPLATE_ID=pe-sent-back-reminder
APPROVAL_REMINDER_EMAIL_TEMPLATE_ID=pe-approval-reminder

server.servlet.context-path=/pem_abslpm

PAY_EXPENSE_SENDER_EMAIL=<EMAIL>

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
hibernate.dialect.storage_engine=innodb
spring.jackson.deserialization.fail-on-unknown-properties=false

#   File upload limit
spring.servlet.multipart.max-file-size=10MB

#   Logging
logging.level.in.taxgenie=TRACE


JWT_SECRET=1E223539-0D5B-4279-8F9A-B3B13BDC7A29-DA01E40D-6806-40FA-B280-44ACB4902845
PAY_EXPENSE_PRODUCT_ID=4

ALLOWED_ORIGINS='http://localhost:4200,https://payexpense.insideabc.com,http://************'

#   Profile
spring.profiles.active=prod

# Application connection size
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.com.zaxxer.hikari=TRACE
spring.datasource.hikari.maximum-pool-size=150
spring.datasource.hikari.minimum-idle=20
