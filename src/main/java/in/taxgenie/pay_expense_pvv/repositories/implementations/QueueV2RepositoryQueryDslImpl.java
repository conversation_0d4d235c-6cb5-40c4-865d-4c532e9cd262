package in.taxgenie.pay_expense_pvv.repositories.implementations;

import com.querydsl.core.BooleanBuilder;
import static com.querydsl.core.types.dsl.Expressions.constant;

import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.repositories.QueueV2Repository;
import in.taxgenie.pay_expense_pvv.repositories.QueueV2RepositoryJpa;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.queue.QueueRowDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.querydsl.core.types.Projections.constructor;

@Repository
public class QueueV2RepositoryQueryDslImpl implements QueueV2RepositoryJpa {

    @PersistenceContext
    private EntityManager entityManager;

    private final JPAQueryFactory qf;

    public QueueV2RepositoryQueryDslImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
        this.qf  = new JPAQueryFactory(entityManager);
    }

    /* ?????????????????????? ADMIN QUEUE ????????????????????? */
    /* ADMIN QUEUE */

    /* ADMIN QUEUE */
    @Override
    public Page<QueueRowDTO> findAdminQueue(Long companyCode,
                                            Pageable pageable,
                                            Map<String, List<String>> filters) {
        QExpenseReport er = QExpenseReport.expenseReport;
        QExpenseMetadata emd = QExpenseMetadata.expenseMetadata;
        QReportState rs = QReportState.reportState;
        QReportState rs2 = new QReportState("rs2");


        BooleanBuilder where = new BooleanBuilder()
                .and(er.companyCode.eq(companyCode));
        applyFilters(where, er, rs, filters);

        List<OrderSpecifier<?>> orderSpec = buildOrder(pageable.getSort(), er, Order.DESC, er.id);

        List<QueueRowDTO> rows = qf
                .select(constructor(QueueRowDTO.class, buildProjection(er, emd, rs)))
                .from(er)
                .join(er.expenseMetadata, emd)
                .leftJoin(er.reportStates, rs)
                .on(
                        rs.level.eq(
                                JPAExpressions
                                        .select(rs2.level.min())
                                        .from(rs2)
                                        .where(
                                                rs2.expenseReport.eq(er),
                                                rs2.status.eq(ExpenseActionStatus.UNACTIONED)
                                        )
                        ),
                        rs.status.eq(ExpenseActionStatus.UNACTIONED)
                )
                .where(where)
                .orderBy(orderSpec.toArray(new OrderSpecifier<?>[0]))
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();

        long total = countAdmin(companyCode, filters);
        return new PageImpl<>(rows, pageable, total);
    }


    private long countAdmin(Long companyCode, Map<String, List<String>> filters) {
        QExpenseReport er = QExpenseReport.expenseReport;
        QReportState rs = QReportState.reportState;
        BooleanBuilder where = new BooleanBuilder();
        where.and(er.companyCode.eq(companyCode));
        applyFilters(where, er, rs, filters);
        return qf.select(er.id.count())
                .from(er)
                .where(where)
                .fetchOne();
    }

    /* CHECKER QUEUE */
    @Override
    public Page<QueueRowDTO> findCheckerQueue(Long companyCode,
                                              StateChannel channel,
                                              ExpenseActionStatus expenseActionStatus,
                                              String userEmail,
                                              List<ReportStatus> reportStatuses,
                                              Pageable pageable,
                                              Map<String, List<String>> filters) {
        QReportState rs = QReportState.reportState;
        QExpenseReport er = QExpenseReport.expenseReport;
        QExpenseMetadata emd = QExpenseMetadata.expenseMetadata;

        BooleanBuilder where = new BooleanBuilder()
                .and(rs.companyCode.eq(companyCode))
                .and(er.reportStatus.in(reportStatuses))
                .and(rs.channel.eq(channel))
                .and(rs.status.eq(expenseActionStatus))
                .and(rs.approver.eq(userEmail))
                .and(rs.level.eq(er.actionLevel));
        applyFilters(where, er, rs, filters);

        List<OrderSpecifier<?>> orderSpec = buildOrder(pageable.getSort(), er, Order.ASC, er.id);

        List<QueueRowDTO> rows = qf
                .select(constructor(QueueRowDTO.class, buildProjection(er, emd, rs)))
                .from(rs)
                .join(rs.expenseReport, er)
                .join(er.expenseMetadata, emd)
                .where(where)
                .orderBy(orderSpec.toArray(new OrderSpecifier<?>[0]))
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();

        long total = countChecker(companyCode, channel, expenseActionStatus, userEmail, reportStatuses, filters);
        return new PageImpl<>(rows, pageable, total);
    }

    private long countChecker(Long companyCode,
                              StateChannel channel,
                              ExpenseActionStatus expenseActionStatus,
                              String userEmail,
                              List<ReportStatus> reportStatuses,
                              Map<String, List<String>> filters) {
        QReportState rs = QReportState.reportState;
        QExpenseReport er = QExpenseReport.expenseReport;
        BooleanBuilder where = new BooleanBuilder();
        where.and(rs.companyCode.eq(companyCode));
        where.and(er.reportStatus.in(reportStatuses));
        where.and(rs.channel.eq(channel));
        where.and(rs.status.eq(expenseActionStatus));
        where.and(rs.approver.eq(userEmail));
        where.and(rs.level.eq(er.actionLevel));
        applyFilters(where, er, rs, filters);
        return qf.select(rs.id.count())
                .from(rs)
                .join(rs.expenseReport, er)
                .where(where)
                .fetchOne();
    }

    /* USER QUEUE */
    @Override
    public Page<QueueRowDTO> findUserQueue(Long companyCode,
                                           Long userId,
                                           Pageable pageable,
                                           Map<String, List<String>> filters) {
        QExpenseReport   er  = QExpenseReport.expenseReport;
        QExpenseMetadata emd = QExpenseMetadata.expenseMetadata;
        QReportState     rs  = QReportState.reportState;
        QReportState     rs2 = new QReportState("rs2");

        // only filter on the report itself
        BooleanBuilder where = new BooleanBuilder()
                .and(er.companyCode.eq(companyCode))
                .and(er.creatingUserId.eq(userId));
        applyFilters(where, er, rs, filters);

        // inject the ON-clause into the LEFT JOIN
        List<QueueRowDTO> rows = qf
                .select(constructor(QueueRowDTO.class, buildUserProjection(er, emd, rs)))
                .from(er)
                .join(er.expenseMetadata, emd)
                .leftJoin(er.reportStates, rs)
                .on(
                        rs.level.eq(
                                JPAExpressions
                                        .select(rs2.level.min())
                                        .from(rs2)
                                        .where(
                                                rs2.expenseReport.eq(er),
                                                rs2.status.eq(ExpenseActionStatus.UNACTIONED)
                                        )
                        ),
                        rs.status.eq(ExpenseActionStatus.UNACTIONED)
                )
                .where(where)
                .orderBy(buildOrder(pageable.getSort(), er, Order.DESC, er.id).toArray(new OrderSpecifier<?>[0]))
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();

        long total = countUser(companyCode, userId, filters);
        return new PageImpl<>(rows, pageable, total);
    }

    private long countUser(Long companyCode, Long userId, Map<String, List<String>> filters) {
        QExpenseReport er = QExpenseReport.expenseReport;
        BooleanBuilder where = new BooleanBuilder();
        where.and(er.companyCode.eq(companyCode));
        where.and(er.creatingUserId.eq(userId));
        applyFilters(where, er, null, filters);
        return qf.select(er.id.count())
                .from(er)
                .where(where)
                .fetchOne();
    }

    /* HELPERS */
    private com.querydsl.core.types.Expression<?>[] buildProjection(QExpenseReport er,
                                                                    QExpenseMetadata emd,
                                                                    QReportState rs) {
        return new com.querydsl.core.types.Expression<?>[]{
                rs.approverEmployeeCode, rs.approverFirstName, rs.level, rs.approverLastName,
                er.id, er.documentIdentifier, er.createdDate, er.submitDate, er.startDate, er.endDate,
                er.reportTitle, er.description, er.purpose, er.reportClaimAmount, er.reportStatus,
                er.actionLevel, er.containsDeviation, rs.deviationRemarks, er.reportSgstAmount,
                er.reportCgstAmount, er.reportIgstAmount, er.reportTaxableAmount, er.firstName,
                er.middleName, er.lastName, er.employeeEmail, er.employeeCode, er.employeeGrade,
                er.employeeSystemId, er.gender, emd.id, emd.expenseGroup, emd.expenseType,
                er.sendBackRemarks, er.rejectRemarks, er.delegationRemarks, er.defaultApproverRemarks,
                er.containsSentBack, er.glPostingDate, er.paymentDate
        };
    }

    private com.querydsl.core.types.Expression<?>[] buildUserProjection(QExpenseReport er,
                                                                        QExpenseMetadata emd,
                                                                        QReportState rs) {
        return new com.querydsl.core.types.Expression<?>[]{
                Expressions.stringTemplate("case when {0} then {1} else null end",
                        er.reportStatus.eq(ReportStatus.SUBMITTED), rs.approverEmployeeCode),
                Expressions.stringTemplate("case when {0} then {1} else null end",
                        er.reportStatus.eq(ReportStatus.SUBMITTED), rs.approverFirstName),
                rs.level,
                Expressions.stringTemplate("case when {0} then {1} else null end",
                        er.reportStatus.eq(ReportStatus.SUBMITTED), rs.approverLastName),
                er.id, er.documentIdentifier, er.createdDate, er.submitDate, er.startDate, er.endDate,
                er.reportTitle, er.description, er.purpose, er.reportClaimAmount, er.reportStatus,
                er.actionLevel, er.containsDeviation, rs.deviationRemarks, er.reportSgstAmount,
                er.reportCgstAmount, er.reportIgstAmount, er.reportTaxableAmount, er.firstName,
                er.middleName, er.lastName, er.employeeEmail, er.employeeCode, er.employeeGrade,
                er.employeeSystemId, er.gender, emd.id, emd.expenseGroup, emd.expenseType,
                er.sendBackRemarks, er.rejectRemarks, er.delegationRemarks, er.defaultApproverRemarks,
                er.containsSentBack, er.glPostingDate, er.paymentDate
        };
    }

    private void applyFilters(BooleanBuilder into,
                              QExpenseReport er,
                              QReportState rs,
                              Map<String, List<String>> filters) {
        if (filters == null || filters.isEmpty()) return;
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String key = entry.getKey();
            List<String> values = entry.getValue();
            if (values == null || values.isEmpty()) continue;
            BooleanBuilder orBlock = new BooleanBuilder();
            for (String value : values) {
                if (value == null || value.isBlank()) continue;
                String likePattern = "%" + value.toLowerCase() + "%";

                if (StringConstants.EMPLOYEE_NAME.equals(key)) {
                    orBlock.or(Expressions.stringTemplate("lower(concat({0},' ',{1}))",
                            er.firstName, er.lastName).like(likePattern));
                } else if (StringConstants.EMPLOYEE_EMAIL.equals(key)) {
                    orBlock.or(er.employeeEmail.lower().like(likePattern));
                } else if (StringConstants.EMPLOYEE_CODE.equals(key)) {
                    orBlock.or(er.employeeCode.lower().like(likePattern));
                } else if (StringConstants.APPROVER_NAME.equals(key)) {
                    if (rs != null) {
                        orBlock.or(Expressions.stringTemplate("lower(concat({0},' ',{1}))",
                                rs.approverFirstName, rs.approverLastName).like(likePattern));
                    }
                } else if (StringConstants.APPROVER_EMPLOYEE_CODE.equals(key)) {
                    if (rs != null) {
                        orBlock.or(rs.approverEmployeeCode.lower().like(likePattern));
                    }
                } else if (StringConstants.DOCUMENT_IDENTIFIER.equals(key)) {
                    orBlock.or(er.documentIdentifier.lower().like(likePattern));
                } else if (StringConstants.REPORT_TITLE.equals(key)) {
                    orBlock.or(er.reportTitle.lower().like(likePattern));
                } else if (StringConstants.REPORT_CLAIM_AMOUNT.equals(key)) {
                    orBlock.or(Expressions.stringTemplate("cast({0} as string)", er.reportClaimAmount)
                            .like(value.replace("%", "")));
                } else if (StringConstants.CREATED_DATE.equals(key)) {
                    handleDateFilter(orBlock, er, value);
                } else if (StringConstants.REPORT_STATUS.equals(key)) {
                    List<ReportStatus> sts = new ArrayList<ReportStatus>();
                    for (String v : values) {
                        sts.add(ReportStatus.valueOf(v.toUpperCase()));
                    }
                    orBlock.or(er.reportStatus.in(sts));
                }
            }
            if (orBlock.hasValue()) {
                into.and(orBlock);
            }
        }
    }

    private void handleDateFilter(BooleanBuilder orBlock, QExpenseReport er, String value) {
        String likePattern = "%" + value.toLowerCase() + "%";
        LocalDate start = null;
        LocalDate end = null;
        try {
            if (value.length() == 10) {
                start = LocalDate.parse(value, DateTimeFormatter.ISO_DATE);
                end = start;
            } else if (value.length() == 7) {
                start = LocalDate.parse(value + "-01");
                end = start.plusMonths(1).minusDays(1);
            } else if (value.length() == 4) {
                start = LocalDate.parse(value + "-01-01");
                end = start.plusYears(1).minusDays(1);
            }
        } catch (DateTimeParseException ignored) {}
        if (start != null && end != null) {
            orBlock.or(er.createdDate.between(start, end));
        } else {
            orBlock.or(Expressions.stringTemplate(
                            dbDateToStringFunc() + "({0},'%Y-%m-%d')", er.createdDate)
                    .like(likePattern));
        }
    }


    @SuppressWarnings({"rawtypes","unchecked"})
    private List<OrderSpecifier<?>> buildOrder(
            Sort sort,
            QExpenseReport er,
            Order defaultDir,
            com.querydsl.core.types.Expression defaultExpr   // raw Expression
    ) {
        List<OrderSpecifier<?>> orders = new ArrayList<>();

        if (sort == null || sort.isEmpty()) {
            // default fallback e.g. er.id DESC
            orders.add(new OrderSpecifier(defaultDir, defaultExpr));
            return orders;
        }

        // dynamic ?root? so we can sort by any property name
        PathBuilder<?> root = new PathBuilder(er.getType(), er.getMetadata());

        for (Sort.Order s : sort) {
            // grab a raw Expression for a Comparable column
            com.querydsl.core.types.Expression expr =
                    root.get(s.getProperty(), Comparable.class);

            orders.add(new OrderSpecifier(
                    s.isAscending() ? Order.ASC : Order.DESC,
                    expr
            ));
        }
        return orders;
    }

    private static final String DATE_FMT_FUNC = detectMySql() ? "date_format" : "to_char";
    private static String dbDateToStringFunc() { return DATE_FMT_FUNC; }
    private static boolean detectMySql() {
        try {
            return org.hibernate.cfg.Environment.getProperties().toString().toLowerCase().contains("mysql");
        } catch (Exception ex) {
            return true;
        }
    }
}