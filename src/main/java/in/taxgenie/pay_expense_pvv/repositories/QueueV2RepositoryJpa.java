package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.StateChannel;
import in.taxgenie.pay_expense_pvv.viewmodels.queue.QueueRowDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface QueueV2RepositoryJpa {
    Page<QueueRowDTO> findAdminQueue(Long companyCode, Pageable pageable,
                                     Map<String, List<String>> filters);

    Page<QueueRowDTO> findCheckerQueue(Long companyCode,
                                       StateChannel channel,
                                       ExpenseActionStatus expenseActionStatus,
                                       String userEmail,
                                       List<ReportStatus> reportStatuses,
                                       Pageable pageable,
                                       Map<String, List<String>> filters);

    Page<QueueRowDTO> findUserQueue(Long companyCode,
                                    Long userId,
                                    Pageable pageable,
                                    Map<String, List<String>> filters);
}
