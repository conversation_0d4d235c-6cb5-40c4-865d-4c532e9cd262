package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.DocumentIdentifier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IDocumentIdentifierRepository extends JpaRepository<DocumentIdentifier, Long> {
    Optional<DocumentIdentifier> findFirstByCompanyCodeAndExpenseTypeAndYearAndMonth(long companyCode, String type, int year, int month);
}

