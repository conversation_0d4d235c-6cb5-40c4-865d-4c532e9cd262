package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ExpenseSubgroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IExpenseSubgroupRepository extends JpaRepository<ExpenseSubgroup, Long> {
    @Query("select count(e) from ExpenseSubgroup e where e.companyCode = ?1 and e.expenseCode = ?2 and e.expenseMetadataId = ?3 and e.id <> ?4 and e.isFrozen = false")
    long getCountByCode(long companyCode, String expenseCode, long metadataId, long id);

    @Query("select count(e) from ExpenseSubgroup e where e.companyCode = ?1 and e.expenseSubgroup = ?2 and e.expenseMetadataId = ?3 and e.id <> ?4 and e.isFrozen = false")
    long getCountBySubgroup(long companyCode, String subgroup, long metadataId, long id);

    @Query("select count(e) from ExpenseSubgroup e where e.companyCode = ?1 and e.expenseSubgroupPrefix = ?2 and e.expenseMetadataId = ?3 and e.id <> ?4 and e.isFrozen = false")
    long getCountByPrefix(long companyCode, String prefix, long metadataId, long id);

    Optional<ExpenseSubgroup> findByCompanyCodeAndId(long companyCode, long id);

    @Query("select e from ExpenseSubgroup  e where e.companyCode = ?1 and e.isFrozen = false")
    List<ExpenseSubgroup> findAllByCompanyCode(long companyCode);

    List<ExpenseSubgroup> findAllByCompanyCodeAndExpenseMetadataId(long companyCode, long metadataId);
}
