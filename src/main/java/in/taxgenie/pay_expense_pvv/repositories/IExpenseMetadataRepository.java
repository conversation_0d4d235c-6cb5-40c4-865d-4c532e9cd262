package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ExpenseMetadata;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IExpenseMetadataRepository extends JpaRepository<ExpenseMetadata, Long> {
    Optional<ExpenseMetadata> findByCompanyCodeAndId(long companyCode, long id);
    List<ExpenseMetadata> findByCompanyCode(long companyCode);

    @Query("select count(e) from ExpenseMetadata e where e.companyCode = ?1 and upper(e.expenseTypePrefix) = upper(?2) and upper(e.expenseGroupPrefix) = upper(?3) and e.isFrozen = false")
    long getCountByPrefixes(long companyCode, String expenseTypePrefix, String expenseGroupPrefix);

    @Query("select count(e) from ExpenseMetadata e where e.companyCode = ?1 and upper(e.expenseType) = upper(?2) and upper(e.expenseGroup) = upper(?3)  and e.isFrozen = false")
    long getCountOfTypeAndGroup(long companyCode, String expenseType, String expenseGroup);
}
