package in.taxgenie.pay_expense_pvv.repositories;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import in.taxgenie.pay_expense_pvv.entities.Expense;
import in.taxgenie.pay_expense_pvv.entities.ReportState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import in.taxgenie.pay_expense_pvv.entities.ExpenseReport;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.services.interfaces.ExpenseReportCSVViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.AdminQueueViewModel;

@Repository
public interface IExpenseReportRepository extends JpaRepository<ExpenseReport, Long> {
    Optional<ExpenseReport> findByCompanyCodeAndId(long companyCode, long id);
    Optional<ExpenseReport> findByCompanyCodeAndIdAndCreatingUserId(long companyCode, long id, long creatingUserId);

    Optional<ExpenseReport> findByCompanyCodeAndIdAndEmployeeEmail(long companyCode, long id, String email);
    Optional<ExpenseReport> findByCompanyCodeAndIdAndReportStatusNot(long companyCode, long id,  ReportStatus status);
    long countByCompanyCodeAndReportStatusAndCreatingUserId(long companyCode, ReportStatus status, long creatingUserId);
    @Query("select e from ExpenseReport e where e.companyCode = ?1 and e.creatingUserId = ?2 order by e.id DESC")
    List<ExpenseReport> getUserQueue(long companyCode, long creatingUserId);

    @Query("select e from ExpenseReport e where e.companyCode = ?1 order by e.id DESC")
    List<ExpenseReport> getAdminQueue(long companyCode);

    @Query(value="select rs.approver_employee_code as currentApproverEmployeeCode, rs.approver_first_name as currentApproverFirstName, rs.level as level, rs.approver_last_name as currentApproverLastName, er1.id, er1.document_identifier as documentIdentifier, er1.created_date as createdDate, er1.submit_date as submitDate, er1.start_date as startDate, er1.end_date as endDate, er1.report_title as reportTitle, er1.description as description, er1.purpose as purpose, er1.report_claim_amount as reportClaimAmount, er1.report_status as reportStatus, er1.action_level as actionLevel, er1.contains_deviation as containsDeviation, er1.deviation_remarks as deviationRemarks, er1.report_sgst_amount as reportSgstAmount, er1.report_cgst_amount as reportCgstAmount, er1.report_igst_amount as reportIgstAmount, er1.report_taxable_amount as reportTaxableAmount, er1.first_name as firstName, er1.middle_name as middleName, er1.last_name as lastName, er1.employee_email as employeeEmail, er1.employee_code as employeeCode, er1.employee_grade as employeeGrade, er1.employee_system_id as employeeSystemId, er1.gender as gender, er1.expense_metadata_id as expenseMetadataId, em.expense_group as expenseGroup, em.expense_type as expenseType, er1.send_back_remarks as sendBackRemarks, er1.reject_remarks as rejectRemarks, er1.delegation_remarks as delegationRemarks, er1.default_approver_remarks as defaultApproverRemarks, er1.contains_sent_back as containsSentBack, er1.gl_posting_date as glPostingDate, er1.payment_date as paymentDate from expense_report as er1 inner join expense_metadata em on er1.expense_metadata_id left join report_state rs on rs.id = (select rs2.id from report_state as rs2 where rs2.expense_report_id = er1.id and rs2.status=0 order by rs2.level limit 1) where er1.company_code = ?1 and er1.expense_metadata_id = em.id order by id desc",nativeQuery = true)
    List<AdminQueueViewModel> getAdminQueueV2(long companyCode);

    @Query("select e from ExpenseReport e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.expenseMetadataId = ?3 and e.reportStatus in ?4 order by e.id DESC")
    List<ExpenseReport> getReportsByMetadataUserStatus(long companyCode, long creatingUserId, long metadataId, List<ReportStatus> statusList);

    @Query("select e from ExpenseReport e where e.companyCode = ?1 and e.reportStatus = ?2 and e.isPostedToGl = false and e.acknowledgedByErp = false and e.employeeGlMainAccountCode is not null order by e.id DESC")
    List<ExpenseReport> getReportsForErpProcessing(long companyCode, ReportStatus status);

    List<ExpenseReport> getByCompanyCode(long companyCode);
//    ReportState
    @Query("SELECT rs.expenseReport FROM ReportState rs " +
            "WHERE rs.companyCode = :companyCode " +
            "AND rs.approverEmployeeCode = :employeeCode " +
            "AND rs.approver = :employeeEmail " +
            "AND rs.expenseReport.actionLevel NOT IN (0, -2)")
    List<ExpenseReport> getApproverListByEmail(@Param("companyCode") long companyCode,
                                                                         @Param("employeeCode") String employeeCode,
                                                                         @Param("employeeEmail") String employeeEmail);

    // simplest, hard-coded ?false?
    List<ExpenseReport> findByCompanyCodeAndEmployeeCodeAndIsPostedToGlFalse(long companyCode, String employeeCode);

    /**
     * Bulk?updates the four GL columns when:
     *   ? report.isPostedToGl = false
     *   ? AND (overwrite = true OR any column is null/blank)
     */
    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query(
            "update ExpenseReport r set " +
                    "  r.employeeGlMainAccountCode = :gl, " +
                    "  r.employeeProfitCenter      = :pc, " +
                    "  r.employeeCostCenter        = :cc, " +
                    "  r.dimension02               = :seg " +
                    "where r.companyCode   = :company " +
                    "  and r.employeeCode  = :emp " +
                    "  and r.isPostedToGl  = false " +
                    "  and ( :overwrite = true " +
                    "        or trim(coalesce(r.employeeGlMainAccountCode, '')) = '' " +
                    "        or trim(coalesce(r.employeeProfitCenter,      '')) = '' " +
                    "        or trim(coalesce(r.employeeCostCenter,        '')) = '' " +
                    "        or trim(coalesce(r.dimension02,               '')) = '' )"
    )
    int bulkUpdateGlCodes(
            @Param("company")   long    company,
            @Param("emp")       String  employee,
            @Param("gl")        String  gl,
            @Param("pc")        String  profitCenter,
            @Param("cc")        String  costCenter,
            @Param("seg")       String  segment,
            @Param("overwrite") boolean overwrite
    );

    @Query("select e from ExpenseReport e where e.companyCode = ?1 and e.id = ?2 and e.reportStatus not in ?3 ")
    Optional<ExpenseReport> getReportsForErpPosting(long companyCode, long id, List<ReportStatus> status);

    @Query("select sum(e.reportClaimAmount) from ExpenseReport e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.reportStatus in ?3 and e.submitDate >= ?4 and e.submitDate <= ?5")
    Optional<Double> getTotalClaimForPeriod(long companyCode, long userId, List<ReportStatus> status, LocalDate start, LocalDate end);

    @Query("select sum(e.totalPaidAmount) from ExpenseReport e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.submitDate >= ?3 and e.submitDate <= ?4")
    Optional<Double> getTotalPaidForPeriod(long companyCode, long userId, LocalDate start, LocalDate end);

    @Query("select sum(e.totalTdsAmount) from ExpenseReport e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.submitDate >= ?3 and e.submitDate <= ?4")
    Optional<Double> getTotalTdsForPeriod(long companyCode, long userId, LocalDate start, LocalDate end);

    @Query("select count(e) from ExpenseReport e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.reportStatus = ?3 and e.submitDate >= ?4 and e.submitDate <= ?5")
    Optional<Long> getReportCountByCriteria(long companyCode, long userId, ReportStatus status, LocalDate start, LocalDate end);

    @Query("select count(e) from ExpenseReport e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.reportStatus = ?3 and e.createdDate >= ?4 and e.createdDate <= ?5")
    Optional<Long> getReportCountByCriteriaAndCreation(long companyCode, long userId, ReportStatus status, LocalDate start, LocalDate end);

    List<ExpenseReport> findAllByReportStatus(ReportStatus status);

    @Query("select er from ExpenseReport er where er.companyCode = ?1  " +
            " and er.createdDate  between ?2 and ?3  and er.reportStatus = ?4 " +
            " and UPPER(TRIM(er.value03)) like ?5 order by er.id desc ")
    List<ExpenseReport> getByExpenseReportByTATSelection(long companyCode, LocalDate fromDate, LocalDate toDate,ReportStatus voucherStatus, String entity);

    @Query("select er from ExpenseReport er where er.companyCode = ?1  " +
            " and er.createdDate  between ?2 and ?3  and er.reportStatus not in (?4 , ?5) " +
            " and UPPER(TRIM(er.value03)) like ?6 order by er.id desc ")
    List<ExpenseReport> getByExpenseReportByTATSelectionForAll(long companyCode, LocalDate fromDate, LocalDate toDate,ReportStatus voucherStatus, ReportStatus revokeStatus, String entity);
    @Query("select er from ExpenseReport er where er.companyCode = ?1 and er.expenseMetadata.expenseType like ?2 and er.createdDate  between ?3 and ?4 " +
            " and er.reportStatus = ?5 and UPPER(TRIM(er.value03))  like ?6 order by er.employeeCode ")
    List<ExpenseReport> getByExpenseType(long companyCode, String expenseType, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, String entity);

    @Query("select er from ExpenseReport er where er.companyCode = ?1 and er.expenseMetadata.expenseType like ?2 and  " +
            " er.createdDate  between ?3 and ?4 and er.reportStatus not in (?5 , ?6) and UPPER(TRIM(er.value03)) like ?7  order by er.employeeCode ")
    List<ExpenseReport> getByExpenseTypeForAll(long companyCode, String expenseType, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus, String entity);

	@Query(value = "select er.company_code as companyCode, er.created_date as createdDate, er.report_status as reportStatus , er.document_identifier as documentIdentifier ,er.report_claim_amount as reportClaimAmount,er.paid_status as paidStatus, er.total_paid_amount as totalPaidAmount , er.gl_posting_date as glPostingDate, er.id as id,"
			+ "er.employee_branch as employeeBranch, er.start_date as startDate , er.end_date as endDate, er.value03 as value03 ,er.employee_grade as employeeGrade, er.submit_date as submitDate, pt.payment_date as paymentDate, pt.payment_reference as paymentReference,"
			+ "em.expense_type as expenseType, er.report_title as reportTitle, er.first_name as firstName, er.middle_name as middleName, er.last_name as lastName, er.employee_cost_center as employeeCostCenter, er.employee_department as employeeDepartment, "
			+ "count(rs.id) as sentBackTimes, "
			+ "case when rs.status = 3 then rs.updated_timestamp end as sentBackOnDate ,"
			+ "case when rs.status = 3 then rs.remarks end as remarks,"
			+ "case when rs.status = 3 then rs.approver_employee_code end as sentBackByCode,"
			+ "case when rs.status = 3 then CONCAT(rs.approver_first_name, ' ', rs.approver_last_name) end as sentBackBy,"
			+ "case when rs.status = 1 and er.report_status = 3 then rs.approver_employee_code end as currentApproverEmployeeCode,"
			+ "case when rs.status = 1 and er.report_status = 3 then CONCAT(rs.approver_first_name, ' ', rs.approver_last_name) end as currentApproverName,"
			+ "case when rs.status = 1 and er.report_status = 3 then rs.action_date end  as approvedDate "
			+ "from expense_report er inner join expense_metadata em on em.id = er.expense_metadata_id left join payment pt on pt.expense_report_id = er.id "
			+ "left join report_state rs on rs.expense_report_id = (select rs2.expense_report_id from report_state rs2 where rs2.expense_report_id = er.id order by rs2.updated_timestamp desc limit 1) "
			+" left join expense e on  e.expense_report_id = (er.id and e.company_code = ?1) "
			+ "where er.company_code = ?1  and em.expense_type like ?2  and (er.created_date between ?3  and ?4 ) and er.report_status not in (?5, ?6) and UPPER(TRIM(er.value03)) like ?7 group by er.id", nativeQuery = true)
	List<ExpenseReportCSVViewModel> getByExpenseTypeForAllV2(long companyCode, String expenseTypeForSeletion,
			Date fromDate, Date toDate, ReportStatus voucherStatus, ReportStatus revokeStatus, String entity);

	@Query(value = "select er.created_date,er.report_status,er.document_identifier,er.report_claim_amount,er.paid_status,er.total_paid_amount,er.gl_posting_date,er.id,"
			+ "er.company_code,er.employee_branch,er.start_date,er.end_date,er.value03,er.submit_date,pt.payment_date,pt.payment_reference,"
			+ "em.expense_type,er.report_title,er.first_name,er.middle_name,er.last_name,er.employee_cost_center,er.employee_department,er.employee_grade, "
			+ "case when rs.status = 3 then rs.updated_timestamp end as sent_back_on_date,"
			+ "case when rs.status = 3 then rs.remarks end as sent_back_remarks,"
			+ "case when rs.status = 3 then rs.approver_employee_code end as sent_back_by_code,"
			+ "case when rs.status = 3 then concat(rs.approver_first_name + ' ' + rs.approver_last_name) end as sent_back_by,"
			+ "case when rs.status = 1 and er.report_status = 3 then rs.approver_employee_code end as current_approver_employee_code,"
			+ "case when rs.status = 1 and er.report_status = 3 then concat(rs.approver_first_name + ' ' + rs.approver_last_name) end as current_approver_name,"
			+ "case when rs.status = 1 and er.report_status = 3 then rs.action_date end as approved_date "
			+ "from expense_report er inner join expense_metadata em on em.id = er.expense_metadata_id left join payment pt on pt.expense_report_id = er.id left join report_state rs on rs.id = (select rs2.id from report_state as rs2 where rs2.expense_report_id = er.id and rs2.status = 0 order by rs2.level limit 1) "
			+ "where er.company_code = ?1  and em.expense_type like ?2  and (er.created_date between ?3  and ?4 ) and er.report_status not in (?5, ?6) and UPPER(TRIM(er.value03)) like ?7", nativeQuery = true)
	List<ExpenseReportCSVViewModel> getByExpenseTypeV2(long companyCode, String expenseTypeForSeletion,
			LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, String entity);

}
