package in.taxgenie.pay_expense_pvv.repositories;

import com.fasterxml.jackson.annotation.OptBoolean;
import in.taxgenie.pay_expense_pvv.entities.Payment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IPaymentRepository extends JpaRepository<Payment, Long> {

    Optional<Payment> findFirstByCompanyCodeAndExpenseReportIdOrderByCreatedTimestampDesc(long companyCode, long reportId);
    Optional<Payment> findByExpenseReportId(long expenseReportId);


}
