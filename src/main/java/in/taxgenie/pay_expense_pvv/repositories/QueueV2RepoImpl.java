package in.taxgenie.pay_expense_pvv.repositories;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;

@Repository("QueueV2Repository")
public class QueueV2RepoImpl implements QueueV2Repository {

	@PersistenceContext
	EntityManager em;

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAdminQueueData(String queryString, int pageNumber, int numberOfRecords, String sortBy,
			String sort) {
		List<Object[]> list = new ArrayList<Object[]>();
		if (null != sortBy) {
			queryString += " ORDER BY " + sortBy + " " + sort;
			queryString += ",  er1.id DESC ";
		} else {
			queryString += " ORDER BY er1.id DESC ";
		}
		int startPosition = pageNumber * numberOfRecords;

		list = em.createNativeQuery(queryString).setFirstResult(startPosition).setMaxResults(numberOfRecords)
				.getResultList();
		return list;
	}

	@Override
	public Integer getTotalElements(String query) {

		return ((BigInteger) em.createNativeQuery(query).getSingleResult()).intValue();

	}

	@Override
	public String selectAllQueryForAdmin(long companyCode) {

		StringBuilder queryString = new StringBuilder();
		queryString.append(selectQueryQueue()).append(fromQueryForAdmin()).append(whereQuery(companyCode));
		return queryString.toString();
	}

	public String selectQueryQueue() {
		StringBuilder sb = new StringBuilder();
		return sb.append(
				"select  rs.approver_employee_code, rs.approver_first_name, rs.level,rs.approver_last_name, er1.id, er1.document_identifier, ")
				.append("er1.created_date, er1.submit_date, er1.start_date, er1.end_date, er1.report_title, er1.description, er1.purpose, ")
				.append("er1.report_claim_amount, er1.report_status, er1.action_level, er1.contains_deviation, er1.deviation_remarks, ")
				.append("er1.report_sgst_amount, er1.report_cgst_amount, er1.report_igst_amount, er1.report_taxable_amount, er1.first_name, ")
				.append("er1.middle_name, er1.last_name , er1.employee_email, er1.employee_code, er1.employee_grade, er1.employee_system_id, ")
				.append("er1.gender, er1.expense_metadata_id, em.expense_group, em.expense_type, er1.send_back_remarks, er1.reject_remarks, ")
				.append("er1.delegation_remarks, er1.default_approver_remarks, er1.contains_sent_back, er1.gl_posting_date, er1.payment_date ")
				.toString();
	}

	@Override
	public String selectCountQueryForAdmin(long companyCode) {

		return new StringBuilder("select count(er1.id)").append(fromQueryForAdmin()).append(whereQuery(companyCode))
				.toString();

	}

	@Override
	public String fromQueryForAdmin() {

		return new StringBuilder("FROM expense_report er1 ")
				.append("INNER JOIN expense_metadata em on er1.expense_metadata_id = em.id ")
				.append("LEFT JOIN report_state rs on rs.id =  (select rs2.id from report_state as rs2 where rs2.expense_report_id = er1.id and rs2.status = 0 ")
				.append("ORDER BY rs2.level limit 1) ").toString();
	}

	@Override
	public String whereQuery(long companyCode) {
		return new StringBuilder("WHERE er1.company_code = \'" + companyCode + "\' ").toString();

	}

	@Override
	public String selectCountQueryForChecker(long companyCode, int checker, int expenseActionStatus, String userEmail) {
		return new StringBuilder("select count(er1.id) ").append(fromQueryForChecker())
				.append(whereQueryChecker(companyCode, checker, expenseActionStatus, userEmail)).toString();
	}

	@Override
	public String selectAllQueryForChecker(long companyCode, int checker, int expenseActionStatus, String userEmail) {
		StringBuilder queryString = new StringBuilder();
		return queryString.append(selectQueryQueue()).append(fromQueryForChecker())
				.append(whereQueryChecker(companyCode, checker, expenseActionStatus, userEmail)).toString();
	}

	public String fromQueryForChecker() {

		return new StringBuilder(
				" from report_state rs inner join expense_report er1 ON (rs.expense_report_id = er1.id and rs.level = er1.action_level) ")
						.append("INNER JOIN expense_metadata em on er1.expense_metadata_id = em.id ").toString();
	}

	public String whereQueryChecker(long companyCode, int checker, int expenseActionStatus, String userEmail) {
		return new StringBuilder("WHERE rs.company_code = \'" + companyCode + "\' ")
				.append("and er1.report_status IN (:reportStatus) ").append("and rs.channel = \'" + checker + "\' ")
				.append("and rs.status = \'" + expenseActionStatus + "\' ")
				.append("and rs.approver = \'" + userEmail + "\' ").toString();

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getCheckerQueueData(String queryString, List<Integer> reportStatus, int pageNumber,
			int numberOfRecords, String sortBy, String sort) {
		List<Object[]> list = new ArrayList<>();

		if (null != sortBy) {
			queryString += " ORDER BY " + sortBy + " " + sort;
			queryString += ",  er1.id ASC ";
		} else {
			queryString += " ORDER BY er1.id ASC ";
		}
		int startPosition = pageNumber * numberOfRecords;

		list = em.createNativeQuery(queryString).setParameter("reportStatus", reportStatus)
				.setFirstResult(startPosition).setMaxResults(numberOfRecords).getResultList();
		return list;
	}

	@Override
	public Integer getCheckerTotalElements(String query, List<Integer> reportStatus) {

		return ((BigInteger) em.createNativeQuery(query).setParameter("reportStatus", reportStatus).getSingleResult())
				.intValue();

	}

	@Override
	public String selectCountQueryForUser(long companyCode, long userId) {
		return new StringBuilder("select count(er1.id) ").append(fromQueryForUser())
				.append(whereQueryUser(companyCode, userId)).toString();
	}

	@Override
	public String selectAllQueryForUser(long companyCode, long userId) {
		StringBuilder queryString = new StringBuilder();
		return queryString.append(
				"select case when report_status=1 then rs.approver_employee_code end as approver_employee_code, ")
				.append("case when report_status=1 then rs.approver_first_name end as approver_first_name, ")
				.append("rs.level, case when report_status=1 then rs.approver_last_name end as approver_last_name, ")
				.append("er1.id, er1.document_identifier, ")
				.append("er1.created_date, er1.submit_date, er1.start_date, er1.end_date, er1.report_title, er1.description, er1.purpose, ")
				.append("er1.report_claim_amount, er1.report_status, er1.action_level, er1.contains_deviation, er1.deviation_remarks, ")
				.append("er1.report_sgst_amount, er1.report_cgst_amount, er1.report_igst_amount, er1.report_taxable_amount, er1.first_name, ")
				.append("er1.middle_name, er1.last_name , er1.employee_email, er1.employee_code, er1.employee_grade, er1.employee_system_id, ")
				.append("er1.gender, er1.expense_metadata_id, em.expense_group, em.expense_type, er1.send_back_remarks, er1.reject_remarks, ")
				.append("er1.delegation_remarks, er1.default_approver_remarks, er1.contains_sent_back, er1.gl_posting_date, er1.payment_date ")
				.append(fromQueryForUser()).append(whereQueryUser(companyCode, userId)).toString();
	}

	public String fromQueryForUser() {
		return new StringBuilder("FROM expense_report er1 ")
				.append("INNER JOIN expense_metadata em on er1.expense_metadata_id = em.id ")
				.append("LEFT JOIN report_state rs on rs.id =  (select rs2.id from report_state as rs2 where rs2.expense_report_id = er1.id and rs2.status = 0 ")
				.append("ORDER BY rs2.level limit 1) ").toString();
	}

	public String whereQueryUser(long companyCode, long userId) {
		return new StringBuilder("WHERE er1.company_code = \'" + companyCode + "\' ")
				.append("and er1.creating_user_id = \'" + userId + "\' ").toString();

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getUserQueueData(String queryString, int pageNumber, int numberOfRecords, String sortBy,
			String sort) {
		List<Object[]> list = new ArrayList<>();
		if (null != sortBy) {
			queryString += " ORDER BY " + sortBy + " " + sort;
			queryString += ", er1.id DESC ";
		} else {
			queryString += " ORDER BY er1.id DESC ";
		}
		int startPosition = pageNumber * numberOfRecords;

		list = em.createNativeQuery(queryString).setFirstResult(startPosition).setMaxResults(numberOfRecords)
				.getResultList();
		return list;
	}

}