package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ApprovalDefinition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IApprovalDefinitionRepository extends JpaRepository<ApprovalDefinition, Long> {
    Optional<ApprovalDefinition> findByCompanyCodeAndId(long companyCode, long id);
    Optional<ApprovalDefinition> findByCompanyCodeAndExpenseMetadataIdAndLimitAmountLessThanEqual(long companyCode, long expenseMetadataId, Double claimAmount);

    List<ApprovalDefinition> findByCompanyCodeAndExpenseMetadataId(long companyCode, long expenseMetadataId);
    List<ApprovalDefinition> findByCompanyCode(long companyCode);
    List<ApprovalDefinition> findByExpenseMetadataId(long metadataId);
    long countByExpenseMetadataIdAndLevelAndCompanyCodeAndIsFrozenIsFalse(long expenseMetadataId, int level, long companyCode);
    long countByExpenseMetadataIdAndApprovalMatcherAndApprovalTitleAndCompanyCodeAndIsFrozenIsFalse(long expenseMetadataId, String approvalMatcher, String approvalTitle, long companyCode);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.id = ?2")
    Optional<ApprovalDefinition> findByCompanyIdAndDefinitionId(long companyCode, long id);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.expenseMetadataId = ?2 and a.limitAmount < ?3 and a.isFrozen = false order by a.level")
    List<ApprovalDefinition> findApplicableDefinitions(long companyCode, long expenseMetadataId, Double claimAmount);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.expenseMetadataId = ?2 and a.limitAmount = ?3 and a.isFrozen = false order by a.level")
    List<ApprovalDefinition> findApplicableDefinitionsEqualAmount(long companyCode, long expenseMetadataId, Double claimAmount);

    Optional<ApprovalDefinition> findFirstByCompanyCodeAndExpenseMetadataIdAndLimitAmountGreaterThanAndIsFrozenIsFalseOrderByLevel(long companyCode, long expenseMetadataId, double claimAmount);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.expenseMetadataId = ?2 and a.limitAmount = ?3 and a.isFrozen = false order by a.level")
    List<ApprovalDefinition> getEqualLimitAmountDefinitionsInLastBand(long companyCode, long expenseMetadataId, double limitAmount);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.isFrozen = false")
    List<ApprovalDefinition> findAllActiveDefinitions(long companyCode);

    @Query("select count(a) from ApprovalDefinition a where a.companyCode = ?1 and a.expenseMetadataId = ?2 and a.level = ?3 and a.id <> ?4 and a.isFrozen = false")
    long getCountByCriteria(long companyCode, long expenseMetadataId, int level, long definitionId);
}
