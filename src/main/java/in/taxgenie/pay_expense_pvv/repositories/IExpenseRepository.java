package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.Expense;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.IExpenseAggregatesCount;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.IExpenseAggregatesSum;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.IExpenseValidationAggregates;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface IExpenseRepository extends JpaRepository<Expense, Long> {
    Optional<Expense> findByCompanyCodeAndId(long companyCode, long id);

    @Query("select e from Expense e where e.companyCode = ?1 and e.expenseSubgroupId = ?2 and e.expenseSubgroup.isDateRangeApplicable = ?3 and e.expenseReport.reportStatus in ?4 and e.expenseReport.employeeEmail = ?5")
    List<Expense> getByDateRange(long companyCode, long expenseMetadataId, boolean isDateRangeApplicable, List<ReportStatus> expenseStatuses, String creatingUserEmail);

    boolean existsByCompanyCodeAndIdAndCreatingUserId(long companyCode, long id, long userId);

    List<Expense> findAllByCompanyCodeAndExpenseReportId(long companyCode, long reportId);

    @Query("select sum(e.claimAmount) from Expense e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.expenseSubgroup.expenseMetadataId = ?3 and e.expenseDate = ?4")
    Double getExpensesOnDay(long companyCode, long userId, long metadataId, LocalDate expenseDate);

    @Query("select sum(e.claimAmount) from Expense e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.expenseSubgroup.expenseMetadataId = ?3 and e.expenseReport.reportStatus in ?4 and e.expenseDate >= ?5 and e.expenseDate <= ?6")
    Double getExpensesBetween(long companyCode, long userId, long metadataId, List<ReportStatus> statusList, LocalDate start, LocalDate end);

    @Query("select count(e) from Expense e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.expenseSubgroup.expenseMetadataId = ?3 and e.expenseReport.reportStatus in ?4 and e.expenseDate >= ?5 and e.expenseDate <= ?6")
    long getExpenseCountBetween(long companyCode, long userId, long metadataId, List<ReportStatus> statusList, LocalDate start, LocalDate end);

    @Query("select e from Expense e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.expenseSubgroup.expenseMetadataId = ?3 and e.expenseReport.reportStatus in ?4 and e.expenseDate >= ?5 and e.expenseDate <= ?6")
    List<Expense> getExpensesCountBetween(long companyCode, long userId, long metadataId, List<ReportStatus> statusList, LocalDate start, LocalDate end);

    @Query("select sum(e.claimAmount) as sum, e.expenseDate as expenseDate from Expense e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.expenseSubgroup.expenseMetadataId = ?3 and e.expenseReport.reportStatus in ?4 and e.expenseDate >= ?5 and e.expenseDate <= ?6 group by function('date_format', e.expenseDate, '%Y-%m')")
    List<IExpenseAggregatesSum> getExpensesSumByMonth(long companyCode, long userId, long metadataId, List<ReportStatus> statusList, LocalDate start, LocalDate end);

    @Query("select count(e) as count, e.expenseDate as expenseDate from Expense e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.expenseSubgroup.expenseMetadataId = ?3 and e.expenseReport.reportStatus in ?4 and e.expenseDate >= ?5 and e.expenseDate <= ?6 group by function('date_format', e.expenseDate, '%Y-%m')")
    List<IExpenseAggregatesCount> getExpensesCountByMonth(long companyCode, long userId, long metadataId, List<ReportStatus> statusList, LocalDate start, LocalDate end);

    @Query("select sum(e.claimAmount) as sum, count(e) as count, e.expenseDate as expenseDate from Expense e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.expenseSubgroup.expenseMetadataId = ?3 and e.expenseReport.reportStatus in ?4 and e.expenseDate >= ?5 and e.expenseDate <= ?6 group by function('date_format', e.expenseDate, '%Y-%m')")
    List<IExpenseValidationAggregates> getExpensesAggregatesByMonth(long companyCode, long userId, long metadataId, List<ReportStatus> statusList, LocalDate start, LocalDate end);


    @Query(" select ex FROM Expense ex " +
            " where ex.companyCode = ?1 and  ex.expenseDate between ?2 and  ?3   " +
            " and ex.expenseReport.reportStatus =  ?4 " +
            " and UPPER(TRIM(ex.expenseReport.value03)) like ?5 order by ex.expenseReport.id , ex.expenseDate ")

    List<Expense> getExpenseLineItems(long companyCode, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, String entity);

    @Query(" select ex FROM Expense ex " +
            " where ex.companyCode = ?1 and  ex.expenseDate between ?2 and  ?3   " +
            " and ex.expenseReport.reportStatus not in (?4, ?5) " +
            " and UPPER(TRIM(ex.expenseReport.value03)) like ?6 order by ex.expenseReport.id , ex.expenseDate ")

    List<Expense> getExpenseLineItemsForAll(long companyCode, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatue, String entity);
    @Query("select sum(ex.invoiceAmount) from Expense ex where  ex.companyCode = ?1 and ex.expenseReportId = ?2")
    Optional<Double> getTotalExpenses(long companyCode, long expenseReportId );
}
