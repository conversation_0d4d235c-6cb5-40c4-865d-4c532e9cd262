package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.ReportState;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.StateChannel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IReportStateRepository extends JpaRepository<ReportState, Long> {
    Optional<ReportState> findByCompanyCodeAndIdAndSaveTime(long companyCode, long id, long saveTime);
    @Query("select e from ReportState e where e.companyCode = ?1 and upper(e.approver) = upper(?2) and e.status = ?3 and e.isDelegated = false and e.expenseReport.reportStatus in ?4")
    List<ReportState> findStatesByOriginalApproverActionStatus(long companyCode, String approver, ExpenseActionStatus status, List<ReportStatus> statusList);

    @Query("select e from ReportState e where e.companyCode = ?1 and upper(e.approver) = upper(?2) and e.expenseReport.reportStatus = ?3 and e.isDelegated = true")
    List<ReportState> findStatesByDelegatedApproverReportState(long companyCode, String delegatedApprover, ReportStatus expenseStatus);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.expenseReport.reportStatus = ?2")
    List<ReportState> findStatesByReportState(long companyCode, ReportStatus expenseStatus);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.expenseReportId = ?2 and e.expenseReport.reportStatus in ?3 order by e.updatedTimestamp")
    List<ReportState> findByExpenseReportIdSorted(long companyCode, long id, List<ReportStatus> statusList);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.expenseReportId = ?2 and e.expenseReport.reportStatus in ?3 order by e.level")
    List<ReportState> findByExpenseReportIdSortedByLevel(long companyCode, long id, List<ReportStatus> statusList);
    
    @Query("select e from ReportState e where e.companyCode = ?1 and e.expenseReport.reportStatus in ?2 and e.channel = ?3 and e.status = ?4 and e.approver = ?5 and e.level = e.expenseReport.actionLevel order by e.expenseReport.submitDate")
    List<ReportState> getApproverQueue(long companyCode, List<ReportStatus> statusList, StateChannel channel, ExpenseActionStatus status, String approverEmail);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.expenseReport.reportStatus in ?2 and e.channel = ?3 and e.status = ?4 and e.level = e.expenseReport.actionLevel order by e.expenseReport.submitDate")
    List<ReportState> getApproverQueueTemp(long companyCode, List<ReportStatus> statusList, StateChannel channel, ExpenseActionStatus status);

    @Query("select count(e) from ReportState e where e.companyCode = ?1 and e.expenseReportId = ?2")
    long getStateCount(long companyCode, long expenseId);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.expenseReportId = ?2 and e.level = ?3 and e.status = ?4")
    Optional<ReportState> getStateForResubmission(long companyCode, long expenseId, int level, ExpenseActionStatus status);

    Optional<ReportState> findFirstByCompanyCodeAndExpenseReportIdAndStatusOrderByLevel(long companyCode, long expenseId, ExpenseActionStatus status);

    Optional<ReportState> findTopByCompanyCodeAndExpenseReportIdAndLevelAndStatus(long companyCode, long expenseId, int level, ExpenseActionStatus status);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.expenseReportId = ?2 order by e.level")
    List<ReportState> getStatesByExpenseReport(long companyCode, long id);

    Optional<ReportState> findFirstByCompanyCodeAndExpenseReportIdAndStatus(long companyCode, long expenseId, ExpenseActionStatus status);


    @Query("select e from ReportState e where e.companyCode = ?1 and e.expenseReportId = ?2 and e.status in ?3 order by e.level")
    List<ReportState> getActionedStatesByExpenseReport(long companyCode, long id, List<ExpenseActionStatus> statusList);

    Optional<ReportState> findFirstByCompanyCodeAndExpenseReportIdOrderByLevelDesc(long companyCode, long expenseReportId);
    List<ReportState> findAllByCompanyCodeAndExpenseReportIdOrderByLevelDesc(long companyCode, long expenseReportId);
    Optional<ReportState> findFirstByCompanyCodeAndExpenseReportIdAndStatusOrderByLevelDesc(long companyCode, long expenseReportId, ExpenseActionStatus status);

    @Query("select rs from ReportState rs where rs.companyCode = ?1 and rs.expenseReportId = ?2 and rs.status = ?3 order by rs.updatedTimestamp desc")
    Optional<List<ReportState>> getStateDetailsByupdatedTimeStampDesc(long companyCode, long expenseReportId, ExpenseActionStatus status);

    @Query("select rs from ReportState rs where rs.companyCode = ?1 and rs.expenseReportId = ?2 and rs.status = ?3 order by rs.updatedTimestamp asc")
    Optional<List<ReportState>> getStateDetailsByupdatedTimeStampAsc(long companyCode, long expenseReportId, ExpenseActionStatus status);

	
}
