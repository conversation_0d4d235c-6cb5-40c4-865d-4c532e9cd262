package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ExpenseRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IExpenseRuleRepository extends JpaRepository<ExpenseRule, Long> {
    Optional<ExpenseRule> findByCompanyCodeAndId(long companyCode, long id);
    List<ExpenseRule> findByCompanyCodeAndExpenseSubgroupId(long companyCode, long subgroupId);
}
