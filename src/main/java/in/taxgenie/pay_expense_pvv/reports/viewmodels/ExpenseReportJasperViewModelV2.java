package in.taxgenie.pay_expense_pvv.reports.viewmodels;

import java.time.LocalDate;

public class ExpenseReportJasperViewModelV2 {
    private Integer fiscalYear;
    private String employeeCode;
    private String employeeName;
    private String employeeGrade;
    private String reportTitle;
    private String documentIdentifier;
    private LocalDate createdDate;
    private Double voucherAmount;
    private String currency;
    private LocalDate startDate;
    private LocalDate endDate;
    private String expenseType;
    private String voucherStatus;
    private String pendingAt;
    private String employeeCostCenter;
    private String location;
    private String paymentStatus;
    private LocalDate paymentDate;
    private LocalDate postedDate;
    private String paymentReference;
    private String mobileNumber;
    private String limitExceeded;
    private String sentBackByCode;
    private String sentBackBy;
    private LocalDate sentBackOnDate;
    private String sentBackOnLevel;
    private  Integer sentBackTimes;
    private String sentBackRemarks;
    private String currentApproverEmployeeCode;
    private String currentApproverName;
    private LocalDate approvedDate;
    private String approverLevel;
    private String entity;
    private Long delay;

    public ExpenseReportJasperViewModelV2() {	};

    public ExpenseReportJasperViewModelV2(Integer fiscalYear, String employeeCode, String employeeName, String employeeGrade, String reportTitle, String documentIdentifier, LocalDate createdDate, Double voucherAmount, String currency, LocalDate startDate, LocalDate endDate, String expenseType, String voucherStatus, String pendingAt, String employeeCostCenter, String location, String paymentStatus, LocalDate paymentDate, LocalDate postedDate, String paymentReference, String mobileNumber, String limitExceeded, String sentBackByCode, String sentBackBy, LocalDate sentBackOnDate, String sentBackOnLevel, Integer sentBackTimes, String sentBackRemarks, String currentApproverEmployeeCode, String currentApproverName, LocalDate approvedDate, String approverLevel, String entity, Long delay) {
        this.fiscalYear = fiscalYear;
        this.employeeCode = employeeCode;
        this.employeeName = employeeName;
        this.employeeGrade = employeeGrade;
        this.reportTitle = reportTitle;
        this.documentIdentifier = documentIdentifier;
        this.createdDate = createdDate;
        this.voucherAmount = voucherAmount;
        this.currency = currency;
        this.startDate = startDate;
        this.endDate = endDate;
        this.expenseType = expenseType;
        this.voucherStatus = voucherStatus;
        this.pendingAt = pendingAt;
        this.employeeCostCenter = employeeCostCenter;
        this.location = location;
        this.paymentStatus = paymentStatus;
        this.paymentDate = paymentDate;
        this.postedDate = postedDate;
        this.paymentReference = paymentReference;
        this.mobileNumber = mobileNumber;
        this.limitExceeded = limitExceeded;
        this.sentBackByCode = sentBackByCode;
        this.sentBackBy = sentBackBy;
        this.sentBackOnDate = sentBackOnDate;
        this.sentBackOnLevel = sentBackOnLevel;
        this.sentBackTimes = sentBackTimes;
        this.sentBackRemarks = sentBackRemarks;
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
        this.currentApproverName = currentApproverName;
        this.approvedDate = approvedDate;
        this.approverLevel = approverLevel;
        this.entity = entity;
        this.delay = delay;
    }

    public Integer getFiscalYear() {
        return fiscalYear;
    }

    public void setFiscalYear(Integer fiscalYear) {
        this.fiscalYear = fiscalYear;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public Double getVoucherAmount() {
        return voucherAmount;
    }

    public void setVoucherAmount(Double voucherAmount) {
        this.voucherAmount = voucherAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getVoucherStatus() {
        return voucherStatus;
    }

    public void setVoucherStatus(String voucherStatus) {
        this.voucherStatus = voucherStatus;
    }

    public String getPendingAt() {
        return pendingAt;
    }

    public void setPendingAt(String pendingAt) {
        this.pendingAt = pendingAt;
    }

    public String getEmployeeCostCenter() {
        return employeeCostCenter;
    }

    public void setEmployeeCostCenter(String employeeCostCenter) {
        this.employeeCostCenter = employeeCostCenter;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public LocalDate getPostedDate() {
        return postedDate;
    }

    public void setPostedDate(LocalDate postedDate) {
        this.postedDate = postedDate;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getLimitExceeded() {
        return limitExceeded;
    }

    public void setLimitExceeded(String limitExceeded) {
        this.limitExceeded = limitExceeded;
    }

    public String getSentBackByCode() {
        return sentBackByCode;
    }

    public void setSentBackByCode(String sentBackByCode) {
        this.sentBackByCode = sentBackByCode;
    }

    public String getSentBackBy() {
        return sentBackBy;
    }

    public void setSentBackBy(String sentBackBy) {
        this.sentBackBy = sentBackBy;
    }

    public LocalDate getSentBackOnDate() {
        return sentBackOnDate;
    }

    public void setSentBackOnDate(LocalDate sentBackOnDate) {
        this.sentBackOnDate = sentBackOnDate;
    }

    public String getSentBackOnLevel() {
        return sentBackOnLevel;
    }

    public void setSentBackOnLevel(String sentBackOnLevel) {
        this.sentBackOnLevel = sentBackOnLevel;
    }

    public Integer getSentBackTimes() {
        return sentBackTimes;
    }

    public void setSentBackTimes(Integer sentBackTimes) {
        this.sentBackTimes = sentBackTimes;
    }

    public String getSentBackRemarks() {
        return sentBackRemarks;
    }

    public void setSentBackRemarks(String sentBackRemarks) {
        this.sentBackRemarks = sentBackRemarks;
    }

    public String getCurrentApproverEmployeeCode() {
        return currentApproverEmployeeCode;
    }

    public void setCurrentApproverEmployeeCode(String currentApproverEmployeeCode) {
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
    }

    public String getCurrentApproverName() {
        return currentApproverName;
    }

    public void setCurrentApproverName(String currentApproverName) {
        this.currentApproverName = currentApproverName;
    }

    public LocalDate getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(LocalDate approvedDate) {
        this.approvedDate = approvedDate;
    }

    public String getApproverLevel() {
        return approverLevel;
    }

    public void setApproverLevel(String approverLevel) {
        this.approverLevel = approverLevel;
    }

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    public Long getDelay() {
        return delay;
    }

    public void setDelay(Long delay) {
        this.delay = delay;
    }
}
