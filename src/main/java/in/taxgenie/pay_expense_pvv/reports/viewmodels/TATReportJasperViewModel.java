package in.taxgenie.pay_expense_pvv.reports.viewmodels;


import in.taxgenie.pay_expense_pvv.entities.PaidStatus;

import java.time.LocalDate;

public class TATReportJasperViewModel {
    private long id;
    private String documentIdentifier;
    private LocalDate createdDate;
    private LocalDate submitDate;
    private String reportTitle;
    private double reportClaimAmount;
    private double voucherAmount;
    private PaidStatus paidStatus;
    private double totalPaidAmount;


    //  Employee master data
    private String employeeCostCenter;
    private String mobileNumber;
    private String employeeGrade;


    private String department;
    private String location;
    private String paymentStatus;
    private long expenseMetadataId;
    private String expenseType;
    private String currentApproverName;
    private String currentApproverEmployeeCode;
    private boolean containsSentBack;

    private String approverCode;
    private String approverName;
    private LocalDate approvedDate;
    private String approverLevel;

    private LocalDate b1ApproverDate;
    private long tat1;
    private String b1ApproverName;

    private LocalDate b2ApproverDate;
    private long tat2;
    private String b2ApproverName;

    private LocalDate b3ApproverDate;
    private long tat3;
    private String b3ApproverName;

    private LocalDate b4ApproverDate;
    private long tat4;
    private String b4ApproverName;

    private LocalDate b5ApproverDate;
    private long tat5;
    private String b5ApproverName;


    private LocalDate checker1ApproverDate;
    private long tat6;
    private String checker1ApproverName;

    private LocalDate checker2ApproverDate;
    private long tat7;
    private String checker2ApproverName;

    private long totalBATAT;
    private long totalCheckerTAT;
    private long totalTAT;
    private long paymentTAT;

    private String voucherStatus;
    private String pendingAt;

    private  int sentBackTimes;
    private String sentBackByCode;
    private LocalDate sentBackOnDate;
    private String sentBackOnLevel;
    private String sentBackBy;
    private String sentBackRemarks;

    private LocalDate startDate;
    private LocalDate endDate;

    //  gl related
    private LocalDate paymentDate;
    private String paymentReference;
    private double totalExpenses;

    private String entity;


    @Override
    public String toString() {
        return "TATReportJasperViewModel{" +
                "id=" + id +
                ", documentIdentifier='" + documentIdentifier + '\'' +
                ", createdDate=" + createdDate +
                ", submitDate=" + submitDate +
                ", reportTitle='" + reportTitle + '\'' +
                ", reportClaimAmount=" + reportClaimAmount +
                ", voucherAmount=" + voucherAmount +
                ", paidStatus=" + paidStatus +
                ", totalPaidAmount=" + totalPaidAmount +
                ", employeeCostCenter='" + employeeCostCenter + '\'' +
                ", mobileNumber='" + mobileNumber + '\'' +
                ", employeeGrade='" + employeeGrade + '\'' +
                ", department='" + department + '\'' +
                ", location='" + location + '\'' +
                ", paymentStatus='" + paymentStatus + '\'' +
                ", expenseMetadataId=" + expenseMetadataId +
                ", expenseType='" + expenseType + '\'' +
                ", currentApproverName='" + currentApproverName + '\'' +
                ", currentApproverEmployeeCode='" + currentApproverEmployeeCode + '\'' +
                ", containsSentBack=" + containsSentBack +
                ", approverCode='" + approverCode + '\'' +
                ", approverName='" + approverName + '\'' +
                ", approvedDate=" + approvedDate +
                ", approverLevel='" + approverLevel + '\'' +
                ", b1ApproverDate=" + b1ApproverDate +
                ", tat1=" + tat1 +
                ", b1ApproverName='" + b1ApproverName + '\'' +
                ", b2ApproverDate=" + b2ApproverDate +
                ", tat2=" + tat2 +
                ", b2ApproverName='" + b2ApproverName + '\'' +
                ", b3ApproverDate=" + b3ApproverDate +
                ", tat3=" + tat3 +
                ", b3ApproverName='" + b3ApproverName + '\'' +
                ", b4ApproverDate=" + b4ApproverDate +
                ", tat4=" + tat4 +
                ", b4ApproverName='" + b4ApproverName + '\'' +
                ", b5ApproverDate=" + b5ApproverDate +
                ", tat5=" + tat5 +
                ", b5ApproverName='" + b5ApproverName + '\'' +
                ", checker1ApproverDate=" + checker1ApproverDate +
                ", tat6=" + tat6 +
                ", checker1ApproverName='" + checker1ApproverName + '\'' +
                ", checker2ApproverDate=" + checker2ApproverDate +
                ", tat7=" + tat7 +
                ", checker2ApproverName='" + checker2ApproverName + '\'' +
                ", totalBATAT=" + totalBATAT +
                ", totalCheckerTAT=" + totalCheckerTAT +
                ", totalTAT=" + totalTAT +
                ", paymentTAT=" + paymentTAT +
                ", voucherStatus='" + voucherStatus + '\'' +
                ", pendingAt='" + pendingAt + '\'' +
                ", sentBackTimes=" + sentBackTimes +
                ", sentBackByCode='" + sentBackByCode + '\'' +
                ", sentBackOnDate=" + sentBackOnDate +
                ", sentBackOnLevel='" + sentBackOnLevel + '\'' +
                ", sentBackBy='" + sentBackBy + '\'' +
                ", sentBackRemarks='" + sentBackRemarks + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", paymentDate=" + paymentDate +
                ", paymentReference='" + paymentReference + '\'' +
                ", totalExpenses=" + totalExpenses +
                ", entity='" + entity + '\'' +
                '}';
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }



    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDate getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDate submitDate) {
        this.submitDate = submitDate;
    }


    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }



    public double getReportClaimAmount() {
        return reportClaimAmount;
    }

    public void setReportClaimAmount(double reportClaimAmount) {
        this.reportClaimAmount = reportClaimAmount;
    }





    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getSentBackRemarks() {
        return sentBackRemarks;
    }

    public void setSentBackRemarks(String sentBackRemarks) {
        this.sentBackRemarks = sentBackRemarks;
    }


    public PaidStatus getPaidStatus() {  return paidStatus;  }

    public void setPaidStatus(PaidStatus paidStatus) {
        this.paidStatus = paidStatus;
    }

    public Double getTotalPaidAmount() {
        return totalPaidAmount;
    }

    public void setTotalPaidAmount(Double totalPaidAmount) {
        this.totalPaidAmount = totalPaidAmount;
    }


    public String getCurrentApproverEmployeeCode() {
        return currentApproverEmployeeCode;
    }

    public void setCurrentApproverEmployeeCode(String currentApproverEmployeeCode) {
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
    }

    public boolean isContainsSentBack() {
        return containsSentBack;
    }

    public void setContainsSentBack(boolean containsSentBack) {
        this.containsSentBack = containsSentBack;
    }


    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public void setTotalPaidAmount(double totalPaidAmount) {
        this.totalPaidAmount = totalPaidAmount;
    }

    public int getSentBackTimes() {
        return sentBackTimes;
    }

    public void setSentBackTimes(int sentBackTimes) {
        this.sentBackTimes = sentBackTimes;
    }

    public String getSentBackByCode() {
        return sentBackByCode;
    }

    public void setSentBackByCode(String sentBackByCode) {
        this.sentBackByCode = sentBackByCode;
    }

    public LocalDate getSentBackOnDate() {
        return sentBackOnDate;
    }

    public void setSentBackOnDate(LocalDate sentBackOnDate) {
        this.sentBackOnDate = sentBackOnDate;
    }

    public String getSentBackOnLevel() {
        return sentBackOnLevel;
    }

    public void setSentBackOnLevel(String sentBackOnLevel) {
        this.sentBackOnLevel = sentBackOnLevel;
    }

    public String getSentBackBy() {
        return sentBackBy;
    }
    public void setSentBackBy(String sentBackBy) {
        this.sentBackBy = sentBackBy;
    }

    public double getTotalExpenses() {
        return totalExpenses;
    }

    public void setTotalExpenses(double totalExpenses) {
        this.totalExpenses = totalExpenses;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }


    public String getCurrentApproverName() {
        return currentApproverName;
    }

    public void setCurrentApproverName(String currentApproverName) {
        this.currentApproverName = currentApproverName;
    }

    public double getVoucherAmount() {
        return voucherAmount;
    }

    public void setVoucherAmount(double voucherAmount) {
        this.voucherAmount = voucherAmount;
    }


    public String getApproverCode() {
        return approverCode;
    }

    public void setApproverCode(String approverCode) {
        this.approverCode = approverCode;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public LocalDate getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(LocalDate approvedDate) {this.approvedDate = approvedDate;}

    public String getApproverLevel() {
        return approverLevel;
    }

    public void setApproverLevel(String approverLevel) {
        this.approverLevel = approverLevel;
    }

    public String getPendingAt() {
        return pendingAt;
    }

    public void setPendingAt(String pendingAt) {
        this.pendingAt = pendingAt;
    }

    public String getVoucherStatus() {
        return voucherStatus;
    }

    public void setVoucherStatus(String voucherStatus) {
        this.voucherStatus = voucherStatus;
    }

    public String getEmployeeCostCenter() {
        return employeeCostCenter;
    }

    public void setEmployeeCostCenter(String employeeCostCenter) {
        this.employeeCostCenter = employeeCostCenter;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public LocalDate getB1ApproverDate() {
        return b1ApproverDate;
    }

    public void setB1ApproverDate(LocalDate b1ApproverDate) {
        this.b1ApproverDate = b1ApproverDate;
    }

    public long getTat1() {
        return tat1;
    }

    public void setTat1(long tat1) {
        this.tat1 = tat1;
    }

    public String getB1ApproverName() {
        return b1ApproverName;
    }

    public void setB1ApproverName(String b1ApproverName) {
        this.b1ApproverName = b1ApproverName;
    }

    public LocalDate getB2ApproverDate() {
        return b2ApproverDate;
    }

    public void setB2ApproverDate(LocalDate b2ApproverDate) {
        this.b2ApproverDate = b2ApproverDate;
    }

    public long getTat2() {
        return tat2;
    }

    public void setTat2(long tat2) {
        this.tat2 = tat2;
    }

    public String getB2ApproverName() {
        return b2ApproverName;
    }

    public void setB2ApproverName(String b2ApproverName) {
        this.b2ApproverName = b2ApproverName;
    }

    public LocalDate getB3ApproverDate() {
        return b3ApproverDate;
    }

    public void setB3ApproverDate(LocalDate b3ApproverDate) {
        this.b3ApproverDate = b3ApproverDate;
    }

    public long getTat3() {
        return tat3;
    }

    public void setTat3(long tat3) {
        this.tat3 = tat3;
    }

    public String getB3ApproverName() {
        return b3ApproverName;
    }

    public void setB3ApproverName(String b3ApproverName) {
        this.b3ApproverName = b3ApproverName;
    }

    public LocalDate getB4ApproverDate() {
        return b4ApproverDate;
    }

    public void setB4ApproverDate(LocalDate b4ApproverDate) {
        this.b4ApproverDate = b4ApproverDate;
    }

    public long getTat4() {
        return tat4;
    }

    public void setTat4(long tat4) {
        this.tat4 = tat4;
    }

    public String getB4ApproverName() {
        return b4ApproverName;
    }

    public void setB4ApproverName(String b4ApproverName) {
        this.b4ApproverName = b4ApproverName;
    }

    public LocalDate getB5ApproverDate() {
        return b5ApproverDate;
    }

    public void setB5ApproverDate(LocalDate b5ApproverDate) {
        this.b5ApproverDate = b5ApproverDate;
    }

    public long getTat5() {
        return tat5;
    }

    public void setTat5(long tat5) {
        this.tat5 = tat5;
    }

    public String getB5ApproverName() {
        return b5ApproverName;
    }

    public void setB5ApproverName(String b5ApproverName) {
        this.b5ApproverName = b5ApproverName;
    }

    public LocalDate getChecker1ApproverDate() {
        return checker1ApproverDate;
    }

    public void setChecker1ApproverDate(LocalDate checker1ApproverDate) {
        this.checker1ApproverDate = checker1ApproverDate;
    }

    public long getTat6() {
        return tat6;
    }

    public void setTat6(long tat6) {
        this.tat6 = tat6;
    }

    public String getChecker1ApproverName() {
        return checker1ApproverName;
    }

    public void setChecker1ApproverName(String checker1ApproverName) {
        this.checker1ApproverName = checker1ApproverName;
    }

    public LocalDate getChecker2ApproverDate() {
        return checker2ApproverDate;
    }

    public void setChecker2ApproverDate(LocalDate checker2ApproverDate) {
        this.checker2ApproverDate = checker2ApproverDate;
    }

    public long getTat7() {
        return tat7;
    }

    public void setTat7(long tat7) {
        this.tat7 = tat7;
    }

    public String getChecker2ApproverName() {
        return checker2ApproverName;
    }

    public void setChecker2ApproverName(String checker2ApproverName) {
        this.checker2ApproverName = checker2ApproverName;
    }

    public long getTotalBATAT() {
        return totalBATAT;
    }

    public void setTotalBATAT(long totalBATAT) {
        this.totalBATAT = totalBATAT;
    }

    public long getTotalCheckerTAT() {
        return totalCheckerTAT;
    }

    public void setTotalCheckerTAT(long totalCheckerTAT) {
        this.totalCheckerTAT = totalCheckerTAT;
    }

    public long getTotalTAT() {
        return totalTAT;
    }

    public void setTotalTAT(long totalTAT) {
        this.totalTAT = totalTAT;
    }

    public long getPaymentTAT() {
        return paymentTAT;
    }

    public void setPaymentTAT(long paymentTAT) {
        this.paymentTAT = paymentTAT;
    }

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
