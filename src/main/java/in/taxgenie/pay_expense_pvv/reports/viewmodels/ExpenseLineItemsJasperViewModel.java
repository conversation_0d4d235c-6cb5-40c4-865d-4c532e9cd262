package in.taxgenie.pay_expense_pvv.reports.viewmodels;

import java.time.LocalDate;

public class ExpenseLineItemsJasperViewModel {

    private int fiscalYear;
    private String documentIdentifier;
    private LocalDate reportCreatedDate;
    private String reportTitle;
    private String expenseType;
    private String expenseName;
    private LocalDate expenseDate;
    private String sourceLocation;
    private String destinationLocation;
    private String employeeCode;
    private String employeeName;
    private String employeeGrade;
    private LocalDate submitDate;
    private String voucherStatus;
    private Double voucherAmount;
    private String currency;
    private Double invoiceAmount;
    private String employeeGLCode;
    private String glDocumentReference;
    private LocalDate paymentDate;
    private String paymentReference;
    private LocalDate startDate;
    private LocalDate endDate;
    private String employeeCostCenter;
    private String location;
    private String description;
    private String entity;


    @Override
    public String toString() {
        return "ExpenseLineItemsJasperViewModel{" +
                "fiscalYear=" + fiscalYear +
                ", documentIdentifier='" + documentIdentifier + '\'' +
                ", reportCreatedDate=" + reportCreatedDate +
                ", reportTitle='" + reportTitle + '\'' +
                ", expenseType='" + expenseType + '\'' +
                ", expenseName='" + expenseName + '\'' +
                ", expenseDate=" + expenseDate +
                ", sourceLocation='" + sourceLocation + '\'' +
                ", destinationLocation='" + destinationLocation + '\'' +
                ", employeeCode='" + employeeCode + '\'' +
                ", employeeName='" + employeeName + '\'' +
                ", employeeGrade='" + employeeGrade + '\'' +
                ", submitDate=" + submitDate +
                ", voucherStatus='" + voucherStatus + '\'' +
                ", voucherAmount=" + voucherAmount +
                ", currency='" + currency + '\'' +
                ", invoiceAmount=" + invoiceAmount +
                ", employeeGLCode='" + employeeGLCode + '\'' +
                ", glDocumentReference='" + glDocumentReference + '\'' +
                ", paymentDate=" + paymentDate +
                ", paymentReference='" + paymentReference + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", employeeCostCenter='" + employeeCostCenter + '\'' +
                ", location='" + location + '\'' +
                ", description='" + description + '\'' +
                ", entity='" + entity + '\'' +
                '}';
    }

    public int getFiscalYear() {
        return fiscalYear;
    }

    public void setFiscalYear(int fiscalYear) {
        this.fiscalYear = fiscalYear;
    }



    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public LocalDate getReportCreatedDate() {
        return reportCreatedDate;
    }

    public void setReportCreatedDate(LocalDate reportCreatedDate) {
        this.reportCreatedDate = reportCreatedDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getExpenseName() {
        return expenseName;
    }

    public void setExpenseName(String expenseName) {
        this.expenseName = expenseName;
    }

    public String getDestinationLocation() {
        return destinationLocation;
    }

    public void setDestinationLocation(String destinationLocation) {
        this.destinationLocation = destinationLocation;
    }

    public String getSourceLocation() {
        return sourceLocation;
    }

    public void setSourceLocation(String sourceLocation) {
        this.sourceLocation = sourceLocation;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmployeeGLCode() {
        return employeeGLCode;
    }

    public void setEmployeeGLCode(String employeeGLCode) {
        this.employeeGLCode = employeeGLCode;
    }

    public String getGlDocumentReference() {
        return glDocumentReference;
    }

    public void setGlDocumentReference(String glDocumentReference) {
        this.glDocumentReference = glDocumentReference;
    }


    public String getEmployeeCostCenter() {
        return employeeCostCenter;
    }

    public void setEmployeeCostCenter(String employeeCostCenter) {
        this.employeeCostCenter = employeeCostCenter;
    }

    public LocalDate getExpenseDate() {
        return expenseDate;
    }

    public void setExpenseDate(LocalDate expenseDate) {
        this.expenseDate = expenseDate;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Double getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(Double invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public Double getVoucherAmount() {
        return voucherAmount;
    }

    public void setVoucherAmount(Double voucherAmount) {
        this.voucherAmount = voucherAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }


    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public LocalDate getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDate submitDate) {
        this.submitDate = submitDate;
    }

    public String getVoucherStatus() {
        return voucherStatus;
    }

    public void setVoucherStatus(String voucherStatus) {
        this.voucherStatus = voucherStatus;
    }
}
