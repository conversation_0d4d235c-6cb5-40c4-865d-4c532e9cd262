package in.taxgenie.pay_expense_pvv.reports.viewmodels;


import in.taxgenie.pay_expense_pvv.entities.PaidStatus;

import java.time.LocalDate;

public class TATReportJasperViewModelV2 {
    private String documentIdentifier;
    private LocalDate createdDate;
    private String voucherStatus;
    private LocalDate submitDate;
    private String employeeGrade;
    private String pendingAt;
    private String expenseType;
    private String employeeCostCenter;
    private String department;
    private String location;
    private double voucherAmount;
    private LocalDate b1ApproverDate;
    private String tat1;
    private String b1ApproverName;
    private LocalDate b2ApproverDate;
    private String tat2;
    private String b2ApproverName;

    private LocalDate b3ApproverDate;
    private String tat3;
    private String b3ApproverName;

    private LocalDate b4ApproverDate;
    private String tat4;
    private String b4ApproverName;

    private LocalDate b5ApproverDate;
    private String tat5;
    private String b5ApproverName;
    private String totalBATAT;

    private LocalDate checker1ApproverDate;
    private String tat6;
    private String checker1ApproverName;

    private LocalDate checker2ApproverDate;
    private String tat7;
    private String checker2ApproverName;
    private LocalDate paymentDate;
    private String paymentReference;
    private String paymentTAT;
    private String totalCheckerTAT;
    private String totalTAT;
    private String entity;

    public TATReportJasperViewModelV2() {};

    public TATReportJasperViewModelV2(String documentIdentifier, LocalDate createdDate, String voucherStatus, LocalDate submitDate, String employeeGrade, String pendingAt, String expenseType, String employeeCostCenter, String department, String location, double voucherAmount, LocalDate b1ApproverDate, String tat1, String b1ApproverName, LocalDate b2ApproverDate, String tat2, String b2ApproverName, LocalDate b3ApproverDate, String tat3, String b3ApproverName, LocalDate b4ApproverDate, String tat4, String b4ApproverName, LocalDate b5ApproverDate, String tat5, String b5ApproverName, String totalBATAT, LocalDate checker1ApproverDate, String tat6, String checker1ApproverName, LocalDate checker2ApproverDate, String tat7, String checker2ApproverName, LocalDate paymentDate, String paymentReference, String paymentTAT, String totalCheckerTAT, String totalTAT, String entity) {
        this.documentIdentifier = documentIdentifier;
        this.createdDate = createdDate;
        this.voucherStatus = voucherStatus;
        this.submitDate = submitDate;
        this.employeeGrade = employeeGrade;
        this.pendingAt = pendingAt;
        this.expenseType = expenseType;
        this.employeeCostCenter = employeeCostCenter;
        this.department = department;
        this.location = location;
        this.voucherAmount = voucherAmount;
        this.b1ApproverDate = b1ApproverDate;
        this.tat1 = tat1;
        this.b1ApproverName = b1ApproverName;
        this.b2ApproverDate = b2ApproverDate;
        this.tat2 = tat2;
        this.b2ApproverName = b2ApproverName;
        this.b3ApproverDate = b3ApproverDate;
        this.tat3 = tat3;
        this.b3ApproverName = b3ApproverName;
        this.b4ApproverDate = b4ApproverDate;
        this.tat4 = tat4;
        this.b4ApproverName = b4ApproverName;
        this.b5ApproverDate = b5ApproverDate;
        this.tat5 = tat5;
        this.b5ApproverName = b5ApproverName;
        this.totalBATAT = totalBATAT;
        this.checker1ApproverDate = checker1ApproverDate;
        this.tat6 = tat6;
        this.checker1ApproverName = checker1ApproverName;
        this.checker2ApproverDate = checker2ApproverDate;
        this.tat7 = tat7;
        this.checker2ApproverName = checker2ApproverName;
        this.paymentDate = paymentDate;
        this.paymentReference = paymentReference;
        this.paymentTAT = paymentTAT;
        this.totalCheckerTAT = totalCheckerTAT;
        this.totalTAT = totalTAT;
        this.entity = entity;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public String getVoucherStatus() {
        return voucherStatus;
    }

    public void setVoucherStatus(String voucherStatus) {
        this.voucherStatus = voucherStatus;
    }

    public LocalDate getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDate submitDate) {
        this.submitDate = submitDate;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public String getPendingAt() {
        return pendingAt;
    }

    public void setPendingAt(String pendingAt) {
        this.pendingAt = pendingAt;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getEmployeeCostCenter() {
        return employeeCostCenter;
    }

    public void setEmployeeCostCenter(String employeeCostCenter) {
        this.employeeCostCenter = employeeCostCenter;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public double getVoucherAmount() {
        return voucherAmount;
    }

    public void setVoucherAmount(double voucherAmount) {
        this.voucherAmount = voucherAmount;
    }

    public LocalDate getB1ApproverDate() {
        return b1ApproverDate;
    }

    public void setB1ApproverDate(LocalDate b1ApproverDate) {
        this.b1ApproverDate = b1ApproverDate;
    }

    public String getTat1() {
        return tat1;
    }

    public void setTat1(String tat1) {
        this.tat1 = tat1;
    }

    public String getB1ApproverName() {
        return b1ApproverName;
    }

    public void setB1ApproverName(String b1ApproverName) {
        this.b1ApproverName = b1ApproverName;
    }

    public LocalDate getB2ApproverDate() {
        return b2ApproverDate;
    }

    public void setB2ApproverDate(LocalDate b2ApproverDate) {
        this.b2ApproverDate = b2ApproverDate;
    }

    public String getTat2() {
        return tat2;
    }

    public void setTat2(String tat2) {
        this.tat2 = tat2;
    }

    public String getB2ApproverName() {
        return b2ApproverName;
    }

    public void setB2ApproverName(String b2ApproverName) {
        this.b2ApproverName = b2ApproverName;
    }

    public LocalDate getB3ApproverDate() {
        return b3ApproverDate;
    }

    public void setB3ApproverDate(LocalDate b3ApproverDate) {
        this.b3ApproverDate = b3ApproverDate;
    }

    public String getTat3() {
        return tat3;
    }

    public void setTat3(String tat3) {
        this.tat3 = tat3;
    }

    public String getB3ApproverName() {
        return b3ApproverName;
    }

    public void setB3ApproverName(String b3ApproverName) {
        this.b3ApproverName = b3ApproverName;
    }

    public LocalDate getB4ApproverDate() {
        return b4ApproverDate;
    }

    public void setB4ApproverDate(LocalDate b4ApproverDate) {
        this.b4ApproverDate = b4ApproverDate;
    }

    public String getTat4() {
        return tat4;
    }

    public void setTat4(String tat4) {
        this.tat4 = tat4;
    }

    public String getB4ApproverName() {
        return b4ApproverName;
    }

    public void setB4ApproverName(String b4ApproverName) {
        this.b4ApproverName = b4ApproverName;
    }

    public LocalDate getB5ApproverDate() {
        return b5ApproverDate;
    }

    public void setB5ApproverDate(LocalDate b5ApproverDate) {
        this.b5ApproverDate = b5ApproverDate;
    }

    public String getTat5() {
        return tat5;
    }

    public void setTat5(String tat5) {
        this.tat5 = tat5;
    }

    public String getB5ApproverName() {
        return b5ApproverName;
    }

    public void setB5ApproverName(String b5ApproverName) {
        this.b5ApproverName = b5ApproverName;
    }

    public String getTotalBATAT() {
        return totalBATAT;
    }

    public void setTotalBATAT(String totalBATAT) {
        this.totalBATAT = totalBATAT;
    }

    public LocalDate getChecker1ApproverDate() {
        return checker1ApproverDate;
    }

    public void setChecker1ApproverDate(LocalDate checker1ApproverDate) {
        this.checker1ApproverDate = checker1ApproverDate;
    }

    public String getTat6() {
        return tat6;
    }

    public void setTat6(String tat6) {
        this.tat6 = tat6;
    }

    public String getChecker1ApproverName() {
        return checker1ApproverName;
    }

    public void setChecker1ApproverName(String checker1ApproverName) {
        this.checker1ApproverName = checker1ApproverName;
    }

    public LocalDate getChecker2ApproverDate() {
        return checker2ApproverDate;
    }

    public void setChecker2ApproverDate(LocalDate checker2ApproverDate) {
        this.checker2ApproverDate = checker2ApproverDate;
    }

    public String getTat7() {
        return tat7;
    }

    public void setTat7(String tat7) {
        this.tat7 = tat7;
    }

    public String getChecker2ApproverName() {
        return checker2ApproverName;
    }

    public void setChecker2ApproverName(String checker2ApproverName) {
        this.checker2ApproverName = checker2ApproverName;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public String getPaymentTAT() {
        return paymentTAT;
    }

    public void setPaymentTAT(String paymentTAT) {
        this.paymentTAT = paymentTAT;
    }

    public String getTotalCheckerTAT() {
        return totalCheckerTAT;
    }

    public void setTotalCheckerTAT(String totalCheckerTAT) {
        this.totalCheckerTAT = totalCheckerTAT;
    }

    public String getTotalTAT() {
        return totalTAT;
    }

    public void setTotalTAT(String totalTAT) {
        this.totalTAT = totalTAT;
    }

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }
}
