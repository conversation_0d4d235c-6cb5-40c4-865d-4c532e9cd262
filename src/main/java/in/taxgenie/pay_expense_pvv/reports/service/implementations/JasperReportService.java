package in.taxgenie.pay_expense_pvv.reports.service.implementations;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.nio.file.DirectoryNotEmptyException;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.reports.service.interfaces.IReportEnvironmentDataService;
import in.taxgenie.pay_expense_pvv.reports.viewmodels.*;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.repositories.implementations.ILineItemsReportRepository;
import in.taxgenie.pay_expense_pvv.utils.ExcelUtil;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.reports.service.interfaces.IJasperReportService;
import in.taxgenie.pay_expense_pvv.services.interfaces.ExpenseRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.ICemLookupService;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.BranchViewModel;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimpleXlsxReportConfiguration;

@Service
@Transactional
public class JasperReportService implements IJasperReportService, ResourceLoaderAware {

	private final IExpenseReportRepository expenseReportRepository;
	private final IExpenseRepository expenseRepository;
	private final IReportStateRepository reportStateRepository;
	private final IPaymentRepository paymentRepository;
	private final ICemLookupService cemLookupService;
	private final IExpenseVouchersReportRepository expenseVouchersReportRepository;

	private final ITATReportRepository tatReportRepository;
	private final ILineItemsReportRepository lineItemsReportRepository;
	private final IReportEnvironmentDataService reportEnvironmentDataService;
	private ResourceLoader resourceLoader;
	private ExcelUtil excelUtil;

	@Autowired
	ExpenseRepository expenseRepo;

	private JRBeanCollectionDataSource jrDataSource;
	private final Logger logger;
	private File jasperFile;

	private Resource resource;

	private List<BranchViewModel> branchViewModel;

	private HashMap<String, String> branchCodeToBranchName;

	private List<ExpenseReportJasperViewModel> expenseReportJasperViewModel;
	private List<ExpenseLineItemsJasperViewModel> expenseLineItemsJasperViewModel;
//	private List<TATReportJasperViewModel> tatReportJasperViewModel;

	private List<TATReportJasperViewModelV2> tatReportJasperViewModel;

	public JasperReportService(IExpenseReportRepository expenseReportRepository, IExpenseRepository expenseRepository,
							   IReportStateRepository reportStateRepository, IPaymentRepository paymentRepository,
							   ICemLookupService cemLookupService,
							   IExpenseVouchersReportRepository expenseVouchersReportRepository,
							   ITATReportRepository tatReportRepository,
							   ILineItemsReportRepository lineItemsReportRepository,
							   ExcelUtil excelUtil,
							   IReportEnvironmentDataService reportEnvironmentDataService) {
		this.expenseReportRepository = expenseReportRepository;
		this.expenseRepository = expenseRepository;
		this.reportStateRepository = reportStateRepository;
		this.paymentRepository = paymentRepository;
		this.cemLookupService = cemLookupService;
		this.expenseVouchersReportRepository = expenseVouchersReportRepository;
		this.tatReportRepository = tatReportRepository;
		this.lineItemsReportRepository = lineItemsReportRepository;
		this.excelUtil = excelUtil;
		this.reportEnvironmentDataService = reportEnvironmentDataService;
		this.logger = LoggerFactory.getLogger(this.getClass());
	}

	@Override
	public void setResourceLoader(final ResourceLoader resourceLoader) {
		this.resourceLoader = resourceLoader;
	}

	@Override
	public JSONObject prepareReports(Map<String, String> queryParameters, IAuthContextViewModel auth)
			throws IOException, JRException {

		JSONObject jsonObject = new JSONObject();
		Map<String, Object> parameter = new HashMap<>();
		parameter.put("Created By", "TaxGenie");
		String reportType = queryParameters.get("reportType");
		String reportFormat = queryParameters.get("reportFormat");
		ReportStatus voucherStatus = ReportStatus.DRAFT;
		ReportStatus revokeStatus = ReportStatus.REVOKED;
		voucherStatus = ReportStatus.valueOf(queryParameters.get("voucherStatus").toUpperCase());
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		String todateString = queryParameters.get("toDate");
		String fromdateString = queryParameters.get("fromDate");
		LocalDate fromDate = LocalDate.parse(fromdateString, dateFormatter);
		LocalDate toDate = LocalDate.parse(todateString, dateFormatter);
		String entity = queryParameters.get("entity").stripTrailing();
		if (entity.equalsIgnoreCase("All"))
			entity = "%";

		InputStream jasperFileStream = null;
		List<?> resultArray = null;

		String reportPath = reportType.strip() + "Report." + reportFormat;
		reportPath = reportEnvironmentDataService.getReportPath(reportPath);

		branchViewModel = cemLookupService.getBranches(auth);

		branchCodeToBranchName = new HashMap<>();
		for (BranchViewModel branchDetails : branchViewModel) {
			if (branchDetails.getBranchCode() != null && branchDetails.getBranchName() != null) {
				branchCodeToBranchName.put(branchDetails.getBranchCode().toUpperCase(), branchDetails.getBranchName());
			}
		}

		if (reportType.contains("All") || reportType.contains("Business") || reportType.contains("Regular") || reportType.contains("Travel")) {
//			if (reportType.contains("All")) {
//				reportType = "%";
//			}
//			resultArray = prepareExpenseReportViewModelV3(auth.getCompanyCode(), reportType, fromDate,
//					toDate, voucherStatus, revokeStatus, entity);
			if (reportType.contains("All")) {
				resultArray = prepareExpenseReportViewModelV3(auth.getCompanyCode(), "%", fromDate, toDate, voucherStatus, revokeStatus, entity);
			} else {
				resultArray = prepareExpenseReportViewModelV3(auth.getCompanyCode(), reportType, fromDate, toDate, voucherStatus, revokeStatus, entity);
			}
		} else if (reportType.contains("Line_Item")) {
			resultArray = prepareExpenseLineItemsReportViewModelV2(auth.getCompanyCode(), reportType,
					fromDate, toDate, voucherStatus, revokeStatus, entity);
		} else if (reportType.contains("TAT_Report")) {
			resultArray = prepareTATReportV2(auth.getCompanyCode(), fromDate, toDate, voucherStatus,
					revokeStatus, entity);
		}

		if (resultArray != null && !resultArray.isEmpty()) {
			if (reportType.contains("All") || reportType.contains("Business") || reportType.contains("Regular") || reportType.contains("Travel")) {
				excelUtil.writePOJOToCSV(reportPath, resultArray, StaticDataRegistry.EXPENSE_REPORT_HEADERS);
			} else if (reportType.contains("TAT_Report")) {
				excelUtil.writePOJOToCSV(reportPath, resultArray, StaticDataRegistry.TAT_REPORT_HEADERS);
			}
			else if (reportType.contains("Line_Item")) {
				excelUtil.writePOJOToCSV(reportPath, resultArray, StaticDataRegistry.LINE_ITEM_HEADERS);
			} else {
				// Default
				excelUtil.writePOJOToCSV(reportPath, resultArray);
			}
		}

		Path path = Paths.get(reportPath);

		try {
			byte[] fileContent = Files.readAllBytes(path);
			String encodedString = java.util.Base64.getEncoder().encodeToString(fileContent);
			jsonObject.put("encoded", encodedString);

			try {
				Files.delete(path);
			} catch (NoSuchFileException x) {
				System.err.format("%s: no such" + " file or directory%n", path);
			} catch (DirectoryNotEmptyException x) {
				System.err.format("%s not empty%n", path);
			} catch (IOException x) {
				// File permission problems are caught here.
				System.err.println(x);
			}

			return jsonObject;

		} catch (IOException ioException) {
			byte[] emptyArray = new byte[0];
			String encodedString = java.util.Base64.getEncoder().encodeToString(emptyArray);
			jsonObject.put("encoded", encodedString);
			return jsonObject;
		}
	}

	public List<ExpenseReportJasperViewModel> prepareExpenseReportViewModel(long companyCode, String expenseType,
			LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus,
			String entity) {
		String[] expenseTypeSplit = expenseType.split("_", 2);
		String expenseTypeForSeletion = expenseTypeSplit[0];
		List<ExpenseReport> expenseReport = new ArrayList<ExpenseReport>();
//		System.out.println(" Expense type is  and entity  " + expenseTypeForSeletion + " and entity " + entity);

		if (voucherStatus.name().equalsIgnoreCase("ALL")) {
			voucherStatus = ReportStatus.DRAFT;
			expenseReport = expenseReportRepository.getByExpenseTypeForAll(companyCode, expenseTypeForSeletion,
					fromDate, toDate, voucherStatus, revokeStatus, entity);
		} else {
			expenseReport = expenseReportRepository.getByExpenseType(companyCode, expenseTypeForSeletion, fromDate,
					toDate, voucherStatus, entity);
		}

		// return
		// expenseReportRepository.getByExpenseType(companyCode,expenseTypeForSeletion,fromAmount,toAmount,fromDate,toDate,voucherStatus)
		return expenseReport.stream().map(e -> {
			ExpenseReportJasperViewModel viewModel = new ExpenseReportJasperViewModel();
			BeanUtils.copyProperties(e, viewModel);
			viewModel.setExpenseType(e.getExpenseMetadata().getExpenseType());
			viewModel.setFiscalYear(e.getCreatedDate().getYear());
			viewModel.setEmployeeName(e.getFirstName() + " " + e.getLastName());

			// If a voucher has been sent back then the document number of the identifier
			// should have a '*' appended to end of it like in the website.
			// Here we are manually setting it instead of relying on the view model to
			// account for the above logic.
			String documentIdentifier = e.getReportStatus() == ReportStatus.SENT_BACK ? "*" + e.getDocumentIdentifier()
					: e.getDocumentIdentifier();
			viewModel.setDocumentIdentifier(documentIdentifier);

			viewModel.setVoucherAmount(e.getReportClaimAmount());
			viewModel.setCurrency("INR");
			// viewModel.setReportTitle(e.getReportTitle());
			// viewModel.setEmployeeCode(e.getEmployeeCode());
			// viewModel.setExpenseGroup(e.getExpenseMetadata().getExpenseGroup());
			viewModel.setPendingAt(e.getCurrentApproverFirstName() + " " + e.getCurrentApproverLastName());

			Optional<List<ReportState>> stateForSentBack = reportStateRepository.getStateDetailsByupdatedTimeStampDesc(
					e.getCompanyCode(), e.getId(), ExpenseActionStatus.SENT_BACK);
			if (!stateForSentBack.get().isEmpty()) {
				viewModel.setSentBackTimes(stateForSentBack.get().size());
				viewModel.setSentBackOnDate(stateForSentBack.get().get(0).getUpdatedTimestamp().toLocalDate());
				viewModel.setSentBackRemarks(stateForSentBack.get().get(0).getRemarks());
				viewModel.setSentBackByCode(stateForSentBack.get().get(0).getApproverEmployeeCode());
				viewModel.setSentBackBy(stateForSentBack.get().get(0).getApproverFirstName() + " "
						+ stateForSentBack.get().get(0).getApproverLastName());
			}

			if (e.getReportStatus() == ReportStatus.ACCEPTED) {
				Optional<List<ReportState>> currentState = reportStateRepository.getStateDetailsByupdatedTimeStampDesc(
						e.getCompanyCode(), e.getId(), ExpenseActionStatus.APPROVED);
				if (!currentState.get().isEmpty()) {
					viewModel.setCurrentApproverEmployeeCode(currentState.get().get(0).getApproverEmployeeCode());
					viewModel.setCurrentApproverName(currentState.get().get(0).getApproverFirstName() + " "
							+ currentState.get().get(0).getApproverLastName());
					viewModel.setApprovedDate(currentState.get().get(0).getActionDate());
				}
			}
			if (e.getPaidStatus() != null)
				viewModel.setPaymentStatus(e.getPaidStatus().name());
			if (e.getGlPostingDate() != null)
				viewModel.setPostedDate(e.getGlPostingDate());

			viewModel.setVoucherStatus(e.getReportStatus().toString());
//			System.out.println("REPORT ID " + e.getId());
			viewModel.setTotalExpenses(expenseRepository.getTotalExpenses(e.getCompanyCode(), e.getId()).orElse(0D));
			BranchViewModel branch = branchViewModel.stream()
					.filter(code -> code.getBranchCode().equalsIgnoreCase(e.getEmployeeBranch())).findFirst()
					.orElse(null);
			if (branch != null && branch.getBranchName() != null)
				viewModel.setLocation(branch.getBranchName());

			viewModel.setDelay(ChronoUnit.DAYS.between(e.getStartDate(), e.getEndDate()));

			viewModel.setEntity(e.getValue03().toUpperCase());

			// Setting of the Employee Grade Number from the reportState
			viewModel.setEmployeeGrade(e.getEmployeeGrade());

			// Submission Date of the expense. Cannot be null as all the draft vouchers are
			// filtered out.
			viewModel.setSubmitDate(e.getSubmitDate());

			// Payment information (UTR reference number UTR date)
			Optional<Payment> paymentInfo = paymentRepository.findByExpenseReportId(e.getId());

			if (paymentInfo.isPresent()) {
				viewModel.setPaymentDate(paymentInfo.get().getPaymentDate());
				viewModel.setPaymentReference(paymentInfo.get().getPaymentReference());
			} else {
				// If expense has not been paid yet, keep the payment date as null and the
				// payment reference code as Not paid.
				viewModel.setPaymentReference("Not Paid");
			}

			return viewModel;
		}).collect(Collectors.toList());

	}

	public List<ExpenseReportJasperViewModel> prepareExpenseReportViewModelV2(long companyCode, String expenseType,
			LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus,
			String entity) {
		String[] expenseTypeSplit = expenseType.split("_", 2);
		String expenseTypeForSeletion = expenseTypeSplit[0];
		List<ExpenseReportJasperViewModel> list = new ArrayList<>();
		List<Object[]> expenseReportModelFromRepo = new ArrayList<>();
		System.out.println(" Expense type is  and entity  " + expenseTypeForSeletion + " and entity " + entity);

		if (voucherStatus.name().equalsIgnoreCase("ALL")) {

			voucherStatus = ReportStatus.DRAFT;
			expenseReportModelFromRepo = expenseRepo.getByExpenseTypeForAllV2(companyCode, expenseTypeForSeletion,
					fromDate, toDate, voucherStatus, revokeStatus, entity);

		} else {
			expenseReportModelFromRepo = expenseRepo.getByExpenseTypeV2(companyCode, expenseTypeForSeletion, fromDate,
					toDate, voucherStatus, entity);
		}
		// return
		// expenseReportRepository.getByExpenseType(companyCode,expenseTypeForSeletion,fromAmount,toAmount,fromDate,toDate,voucherStatus)
		list = buildExpenseReportViewModel(expenseReportModelFromRepo);
		return list;
	}

	public List<ExpenseReportJasperViewModelV2> prepareExpenseReportViewModelV3(long companyCode, String expenseType,
																				LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus,
																				String entity) {
		List<ExpenseReportJasperViewModelV2> resultSet = null;
		String[] expenseTypeSplit = expenseType.split("_", 2);
		String expenseTypeForSeletion = expenseTypeSplit[0];
		List<ExpenseVouchersReport> expenseVoucherReportsFromRepo = new ArrayList<ExpenseVouchersReport>();
//		System.out.println(" Expense type is  and entity  " + expenseTypeForSeletion + " and entity " + entity);

		if (voucherStatus.name().equalsIgnoreCase("ALL")) {
			voucherStatus = ReportStatus.DRAFT;
			expenseVoucherReportsFromRepo = expenseVouchersReportRepository.getByExpenseTypeForAll(companyCode, expenseTypeForSeletion,
					fromDate, toDate, voucherStatus, revokeStatus, entity);
		} else {
			expenseVoucherReportsFromRepo = expenseVouchersReportRepository.getByExpenseType(companyCode, expenseTypeForSeletion, fromDate,
					toDate, voucherStatus, entity);
		}

		resultSet = buildExpenseReportViewModelV2(expenseVoucherReportsFromRepo);

		return resultSet;
	}

	private List<ExpenseReportJasperViewModelV2> buildExpenseReportViewModelV2(List<ExpenseVouchersReport> expenseReportModelFromRepo) {
		List<ExpenseReportJasperViewModelV2> list = new ArrayList<>();
		expenseReportModelFromRepo.forEach(obj -> {
			ExpenseReportJasperViewModelV2 view = new ExpenseReportJasperViewModelV2();
			BeanUtils.copyProperties(obj, view);

			ReportStatus reportStatus = obj.getReportStatus();
			PaidStatus paidStatus = null == obj.getPaidStatus() ? null : PaidStatus.values()[obj.getPaidStatus()];
			String firstName = null == obj.getFirstName() ? "" : obj.getFirstName();
			String lastName = null == obj.getLastName() ? "" : obj.getLastName();
			view.setVoucherAmount(obj.getReportClaimAmount());
			view.setPostedDate(obj.getGlPostingDate());
			view.setEntity(obj.getValue03());
			view.setSentBackOnDate(null == obj.getSentBackDate() ? null : (obj.getSentBackDate()));
			view.setSentBackRemarks(obj.getRemarks());
			view.setCurrentApproverEmployeeCode(obj.getCurrentApproverEmployeeCode());
			view.setCurrentApproverName(obj.getCurrentApproverName());

			if (null != obj.getCreatedDate()) {
				view.setFiscalYear(obj.getCreatedDate().getYear());
			}

			if (null != paidStatus)
				view.setPaymentStatus(paidStatus.name());

			view.setEmployeeName(firstName + " " + lastName);

			// If a voucher has been sent back then the document number of the identifier
			// should have a '*' appended to end of it like in the website.
			// Here we are manually setting it instead of relying on the view model to
			// account for the above logic.
			if (null != reportStatus && obj.getDocumentIdentifier() != null) {
				String documentIdentifier = reportStatus == ReportStatus.SENT_BACK
						? "*" + obj.getDocumentIdentifier()
						: obj.getDocumentIdentifier();
				view.setDocumentIdentifier(documentIdentifier);
				view.setVoucherStatus(reportStatus.name());
			} else {
				view.setDocumentIdentifier(null);
				view.setVoucherStatus(null);
			}

			view.setCurrency("INR");


			if (obj.getEmployeeBranch() != null) {
				view.setLocation(this.branchCodeToBranchName.get(obj.getEmployeeBranch()));
			} else {
				view.setLocation(null);
			}


			if (null != obj.getStartDate() && null != obj.getEndDate()) {
				view.setDelay(ChronoUnit.DAYS.between(obj.getStartDate(), obj.getEndDate()));
			} else {
				view.setDelay(null);
			}

			// Payment information (UTR reference number UTR date)

			if (null == obj.getPaymentReference()) {
				// If expense has not been paid yet, keep the payment date as null and the
				// payment reference code as Not paid.
				view.setPaymentReference("Not Paid");
			}
			view.setApproverLevel(null);
			view.setSentBackOnLevel(null);
			list.add(view);
		});
		return list;
	}

	private List<ExpenseReportJasperViewModel> buildExpenseReportViewModel(List<Object[]> expenseReportModelFromRepo) {
		List<ExpenseReportJasperViewModel> list = new ArrayList<>();
		expenseReportModelFromRepo.forEach(obj -> {
			ExpenseReportJasperViewModel view = new ExpenseReportJasperViewModel();

			view.setCompanyCode(null == obj[0] ? null : ((BigInteger) obj[0]).longValue());
			view.setCreatedDate(null == obj[1] ? null : ((Date) (obj[1])).toLocalDate());
			Integer reportStatus = null == obj[2] ? null : (Integer) (obj[2]);
			view.setDocumentIdentifier(null == obj[3] ? null : String.valueOf(obj[3]));
			view.setVoucherAmount(null == obj[4] ? null : (Double) (obj[4]));
			view.setPaidStatus(null == obj[5] ? null : PaidStatus.values()[(int) (obj[5])]);
			view.setTotalPaidAmount(null == obj[6] ? null : (Double) (obj[6]));
			view.setPostedDate(null == obj[7] ? null : ((Date) (obj[7])).toLocalDate());
			view.setId(null == obj[8] ? null : ((BigInteger) obj[8]).longValue());
			view.setEmployeeBranch(null == obj[9] ? null : String.valueOf(obj[9]));
			view.setStartDate(null == obj[10] ? null : ((Date) (obj[10])).toLocalDate());
			view.setEndDate(null == obj[11] ? null : ((Date) (obj[11])).toLocalDate());
			view.setEntity(null == obj[12] ? null : String.valueOf(obj[12]));
			view.setEmployeeGrade(null == obj[13] ? null : String.valueOf(obj[13]));
			view.setSubmitDate(null == obj[14] ? null : ((Date) (obj[14])).toLocalDate());
			view.setPaymentDate(null == obj[15] ? null : ((Date) (obj[15])).toLocalDate());
			view.setPaymentReference(null == obj[16] ? null : String.valueOf(obj[16]));
			view.setExpenseType(null == obj[17] ? null : String.valueOf(obj[17]));
			view.setReportTitle(null == obj[18] ? null : String.valueOf(obj[18]));
			view.setFirstName(null == obj[19] ? null : String.valueOf(obj[19]));
			view.setMiddleName(null == obj[20] ? null : String.valueOf(obj[20]));
			view.setLastName(null == obj[21] ? null : String.valueOf(obj[21]));
			view.setEmployeeCostCenter(null == obj[22] ? null : String.valueOf(obj[22]));
			view.setDepartment(null == obj[23] ? null : String.valueOf(obj[23]));
			view.setSentBackTimes(null == obj[24] ? null : ((Integer) obj[24]));
			view.setTotalExpenses(null == obj[25] ? null : (Double) (obj[25]));
			view.setSentBackOnDate(null == obj[26] ? null : ((Date) (obj[26])).toLocalDate());
			view.setSentBackRemarks(null == obj[27] ? null : String.valueOf(obj[27]));
			view.setSentBackByCode(null == obj[28] ? null : String.valueOf(obj[28]));
			view.setSentBackBy(null == obj[29] ? null : String.valueOf(obj[29]));
			view.setCurrentApproverEmployeeCode(null == obj[30] ? null : String.valueOf(obj[30]));
			view.setCurrentApproverName(null == obj[31] ? null : String.valueOf(obj[31]));
			view.setApprovedDate(null == obj[32] ? null : ((Date) (obj[32])).toLocalDate());
			view.setEmployeeCode(null == obj[33] ? null : String.valueOf(obj[33]));
			view.setPendingAt(null == obj[34] ? null : String.valueOf(obj[34]));
			if (null == view.getCurrentApproverEmployeeCode()) {
				// view.setCurrentApproverEmployeeCode(null == obj[35] ? null :
				// String.valueOf(obj[35]));
			}
			// 36 - employee_code in resultset
//			view.setTotalExpenses(null == obj[37] ? null : (Double) (obj[37]));
			view.setFiscalYear(view.getCreatedDate().getYear());
			view.setEmployeeName(view.getFirstName() + " " + view.getLastName());

			// If a voucher has been sent back then the document number of the identifier
			// should have a '*' appended to end of it like in the website.
			// Here we are manually setting it instead of relying on the view model to
			// account for the above logic.
			if (null != reportStatus) {
				String documentIdentifier = ReportStatus.values()[reportStatus] == ReportStatus.SENT_BACK
						? "*" + view.getDocumentIdentifier()
						: view.getDocumentIdentifier();
				view.setDocumentIdentifier(documentIdentifier);
				view.setVoucherStatus(ReportStatus.values()[reportStatus].name());
			} else {
				view.setDocumentIdentifier(null);
				view.setVoucherStatus(null);
			}

			view.setCurrency("INR");
//			System.out.println("REPORT ID " + view.getId());
//			view.setTotalExpenses(expenseRepository.getTotalExpenses(view.getCompanyCode(), view.getId()).orElse(0D));

			if (view.getEmployeeBranch() != null) {
				view.setLocation(this.branchCodeToBranchName.get(view.getEmployeeBranch()));
			}

			else
				view.setLocation(null);

			if (null != view.getStartDate() && null != view.getEndDate())
				view.setDelay(ChronoUnit.DAYS.between(view.getStartDate(), view.getEndDate()));
			else
				view.setDelay(null);

			// Payment information (UTR reference number UTR date)

			if (null == view.getPaymentReference()) {
				// If expense has not been paid yet, keep the payment date as null and the
				// payment reference code as Not paid.
				view.setPaymentReference("Not Paid");
			}
			view.setApproverLevel(null);
			view.setSentBackOnLevel(null);
			list.add(view);
		});
		return list;
	}

	public List<ExpenseLineItemsJasperViewModel> prepareExpenseLineItemsReportViewModel(long companyCode,
			String expenseType, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus,
			ReportStatus revokeStatus, String entity) {
		List<Expense> expenses = new ArrayList<Expense>();
		if (voucherStatus.name().equalsIgnoreCase("ALL")) {
			voucherStatus = ReportStatus.DRAFT;
			expenses = expenseRepository.getExpenseLineItemsForAll(companyCode, fromDate, toDate, voucherStatus,
					revokeStatus, entity);
		} else {
			expenses = expenseRepository.getExpenseLineItems(companyCode, fromDate, toDate, voucherStatus, entity);
		}
		return expenses.stream().map(e -> {
			ExpenseLineItemsJasperViewModel viewModel = new ExpenseLineItemsJasperViewModel();
			BeanUtils.copyProperties(e, viewModel);
			viewModel.setFiscalYear(e.getExpenseDate().getYear());

			// If a voucher has been sent back then the document number of the identifier
			// should have a '*' appended to end of it like in the website.
			// Here we are manually setting it instead of relying on the view model to
			// account for the above logic.
			String documentIdentifier = e.getExpenseReport().getReportStatus() == ReportStatus.SENT_BACK
					? "*" + e.getExpenseReport().getDocumentIdentifier()
					: e.getExpenseReport().getDocumentIdentifier();
			viewModel.setDocumentIdentifier(documentIdentifier);
//                    viewModel.setDocumentIdentifier(e.getExpenseReport().getDocumentIdentifier());

			viewModel.setReportCreatedDate(e.getExpenseReport().getCreatedDate());
			viewModel.setReportTitle(e.getExpenseReport().getReportTitle());
			viewModel.setExpenseType(e.getExpenseReport().getExpenseMetadata().getExpenseType());
			viewModel.setExpenseName(e.getExpenseSubgroup().getExpenseSubgroup());
			viewModel.setEmployeeName(e.getExpenseReport().getFirstName() + " " + e.getExpenseReport().getLastName());
			viewModel.setEmployeeCode(e.getExpenseReport().getEmployeeCode());
			viewModel.setVoucherAmount(e.getExpenseReport().getReportClaimAmount());
			viewModel.setCurrency("INR");
			viewModel.setEmployeeGLCode(e.getExpenseReport().getEmployeeGlMainAccountCode());
			viewModel.setGlDocumentReference(e.getExpenseReport().getGlDocumentReference());
			viewModel.setEmployeeCostCenter(e.getExpenseReport().getEmployeeCostCenter());
			viewModel.setSourceLocation(e.getSourceLocation());
			viewModel.setDestinationLocation(e.getDestinationLocation());
			viewModel.setEntity(e.getExpenseReport().getValue03().toUpperCase());

			// In the below changes all the information comes from the linked expense report
			// of the expense.

			// Get the location of the employee if present using the same code from generate
			// expense reports.
			BranchViewModel branch = branchViewModel.stream()
					.filter(code -> code.getBranchCode().equalsIgnoreCase(e.getExpenseReport().getEmployeeBranch()))
					.findFirst().orElse(null);
			if (branch != null && branch.getBranchName() != null)
				viewModel.setLocation(branch.getBranchName());

			// Setting the status of the expense via the status of the parent report
			// voucher.
			viewModel.setVoucherStatus(e.getExpenseReport().getReportStatus().toString());

			// Setting of the Employee Grade Number from the reportState
			viewModel.setEmployeeGrade(e.getExpenseReport().getEmployeeGrade());

			// Submission Date of the expense. Cannot be null as all the draft vouchers are
			// filtered out.
			viewModel.setSubmitDate(e.getExpenseReport().getSubmitDate());

			// Payment information (UTR reference number UTR date)
			Optional<Payment> paymentInfo = paymentRepository.findByExpenseReportId(e.getExpenseReport().getId());

			if (paymentInfo.isPresent()) {
				viewModel.setPaymentDate(paymentInfo.get().getPaymentDate());
				viewModel.setPaymentReference(paymentInfo.get().getPaymentReference());
			} else {
				// If expense has not been paid yet, keep the payment date as null and the
				// payment reference code as Not paid.
				viewModel.setPaymentReference("Not Paid");
			}

			return viewModel;
		}).collect(Collectors.toList());
	}

	public List<ExpenseLineItemsJasperViewModel> prepareExpenseLineItemsReportViewModelV2(long companyCode,
																						String expenseType, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus,
																						ReportStatus revokeStatus, String entity) {
		List<LineItemsReport> expenses = new ArrayList<LineItemsReport>();
		if (voucherStatus.name().equalsIgnoreCase("ALL")) {
			voucherStatus = ReportStatus.DRAFT;
			expenses = lineItemsReportRepository.getExpenseLineItemsForAll(companyCode, fromDate, toDate, voucherStatus,
					revokeStatus, entity);
		} else {
			expenses = lineItemsReportRepository.getExpenseLineItems(companyCode, fromDate, toDate, voucherStatus, entity);
		}
		return expenses.stream().map(obj -> {
			ExpenseLineItemsJasperViewModel view = new ExpenseLineItemsJasperViewModel();
			BeanUtils.copyProperties(obj, view);

			// If a voucher has been sent back then the document number of the identifier
			// should have a '*' appended to end of it like in the website.
			// Here we are manually setting it instead of relying on the view model to
			// account for the above logic.
			if (null != obj.getDocumentIdentifier()) {
				String documentIdentifier = obj.getVoucherStatus() == ReportStatus.SENT_BACK
						? "*" + obj.getDocumentIdentifier()
						: obj.getDocumentIdentifier();
				view.setDocumentIdentifier(documentIdentifier);
			}
			if (obj.getEmployeeBranch() != null) {
				view.setLocation(this.branchCodeToBranchName.get(obj.getEmployeeBranch()));
			}

			view.setGlDocumentReference(obj.getGlDocumentReference());
			view.setEmployeeGLCode(obj.getEmployeeGlCode());
			view.setExpenseDate(obj.getExpenseCreatedDate());

			// Setting the status of the expense via the status of the parent report
			// voucher.
			if (null != obj.getVoucherStatus()) {
				view.setVoucherStatus(obj.getVoucherStatus().toString());
			}

			if (null == obj.getPaymentDate()) {
				view.setPaymentReference("Not Paid");
			}

			return view;
		}).collect(Collectors.toList());
	}

	public List<TATReportJasperViewModel> prepareTATReport(long companyCode, LocalDate fromDate, LocalDate toDate,
			ReportStatus voucherStatus, ReportStatus revokeStatus, String entity) {
		List<ExpenseReport> expenseReportForTAT = new ArrayList<ExpenseReport>();
		if (voucherStatus.name().equalsIgnoreCase("ALL")) {
			voucherStatus = ReportStatus.DRAFT;
			expenseReportForTAT = expenseReportRepository.getByExpenseReportByTATSelectionForAll(companyCode, fromDate,
					toDate, voucherStatus, revokeStatus, entity);
		} else {
			expenseReportForTAT = expenseReportRepository.getByExpenseReportByTATSelection(companyCode, fromDate,
					toDate, voucherStatus, entity);
		}
		return expenseReportForTAT.stream().map(e -> {
			TATReportJasperViewModel viewModel = new TATReportJasperViewModel();
			BeanUtils.copyProperties(e, viewModel);

//                     viewModel.setDocumentIdentifier(e.getDocumentIdentifier());
			// If a voucher has been sent back then the document number of the identifier
			// should have a '*' appended to end of it like in the website.
			String documentIdentifier = e.getReportStatus() == ReportStatus.SENT_BACK ? "*" + e.getDocumentIdentifier()
					: e.getDocumentIdentifier();
			viewModel.setDocumentIdentifier(documentIdentifier);

			viewModel.setCreatedDate(e.getCreatedDate());
			viewModel.setVoucherStatus(e.getReportStatus().name());
			viewModel.setExpenseType(e.getExpenseMetadata().getExpenseType());
			viewModel.setVoucherAmount(e.getReportClaimAmount());
			viewModel.setEmployeeCostCenter(e.getEmployeeCostCenter());
			viewModel.setDepartment(e.getEmployeeDepartment());
			viewModel.setPendingAt(e.getCurrentApproverFirstName() + " " + e.getCurrentApproverLastName());
			viewModel.setEntity(e.getValue03().toUpperCase());

			ReportState stateDetails = new ReportState();
			if (e.getReportStatus() == ReportStatus.PAID) {
				Optional<List<ReportState>> currentStates = reportStateRepository.getStateDetailsByupdatedTimeStampAsc(
						e.getCompanyCode(), e.getId(), ExpenseActionStatus.APPROVED);
				if (currentStates.isPresent()) {
					Iterator<ReportState> statesIterator = currentStates.get().iterator();
					while (statesIterator.hasNext()) {
						stateDetails = statesIterator.next();
						if (stateDetails.getChannel().name() == StateChannel.APPROVER.name()) {
							if (viewModel.getB1ApproverDate() == null) {
								viewModel.setB1ApproverDate(stateDetails.getActionDate());
								viewModel.setTat1(
										ChronoUnit.DAYS.between(stateDetails.getActionDate(), e.getCreatedDate()));
								viewModel.setB1ApproverName(
										stateDetails.getApproverFirstName() + " " + stateDetails.getApproverLastName());

							} else if (viewModel.getB2ApproverDate() == null) {
								viewModel.setB2ApproverDate(stateDetails.getActionDate());
								viewModel.setTat2(
										ChronoUnit.DAYS.between(stateDetails.getActionDate(), e.getCreatedDate()));
								viewModel.setB2ApproverName(
										stateDetails.getApproverFirstName() + " " + stateDetails.getApproverLastName());

							} else if (viewModel.getB3ApproverDate() == null) {
								viewModel.setB3ApproverDate(stateDetails.getActionDate());
								viewModel.setTat3(
										ChronoUnit.DAYS.between(stateDetails.getActionDate(), e.getCreatedDate()));
								viewModel.setB3ApproverName(
										stateDetails.getApproverFirstName() + " " + stateDetails.getApproverLastName());

							} else if (viewModel.getB4ApproverDate() == null) {
								viewModel.setB4ApproverDate(stateDetails.getActionDate());
								viewModel.setTat4(
										ChronoUnit.DAYS.between(stateDetails.getActionDate(), e.getCreatedDate()));
								viewModel.setB4ApproverName(
										stateDetails.getApproverFirstName() + " " + stateDetails.getApproverLastName());
							} else {
								viewModel.setB5ApproverDate(stateDetails.getActionDate());
								viewModel.setTat5(
										ChronoUnit.DAYS.between(stateDetails.getActionDate(), e.getCreatedDate()));
								viewModel.setB5ApproverName(
										stateDetails.getApproverFirstName() + " " + stateDetails.getApproverLastName());
							}

						} else {
							if (viewModel.getChecker1ApproverDate() == null) {
								viewModel.setChecker1ApproverDate(stateDetails.getActionDate());
								viewModel.setTat6(
										ChronoUnit.DAYS.between(stateDetails.getActionDate(), e.getCreatedDate()));
								viewModel.setChecker1ApproverName(
										stateDetails.getApproverFirstName() + " " + stateDetails.getApproverLastName());
							} else {
								viewModel.setChecker2ApproverDate(stateDetails.getActionDate());
								viewModel.setTat7(
										ChronoUnit.DAYS.between(stateDetails.getActionDate(), e.getCreatedDate()));
								viewModel.setChecker2ApproverName(
										stateDetails.getApproverFirstName() + " " + stateDetails.getApproverLastName());
							}
						}
					}
				}
				viewModel.setCurrentApproverEmployeeCode(currentStates.get().get(0).getApproverEmployeeCode());
				viewModel.setCurrentApproverName(currentStates.get().get(0).getApproverFirstName() + " "
						+ currentStates.get().get(0).getApproverLastName());
			}

			viewModel.setTotalBATAT(viewModel.getTat1() + viewModel.getTat2() + viewModel.getTat3()
					+ viewModel.getTat4() + viewModel.getTat5());
			viewModel.setTotalCheckerTAT(viewModel.getTat6() + viewModel.getTat7());

//                    if(e.getPaymentDate() != null) {
//                        viewModel.setPaymentDate(e.getPaymentDate());
//                        viewModel.setPaymentTAT(ChronoUnit.DAYS.between(e.getPaymentDate(),e.getCreatedDate()));
//                    }
			viewModel.setTotalTAT(
					viewModel.getTotalBATAT() + viewModel.getTotalCheckerTAT() + viewModel.getPaymentTAT());

			// Get the location of the employee if present using the same code from generate
			// expense reports.
			BranchViewModel branch = branchViewModel.stream()
					.filter(code -> code.getBranchCode().equalsIgnoreCase(e.getEmployeeBranch())).findFirst()
					.orElse(null);
			if (branch != null && branch.getBranchName() != null)
				viewModel.setLocation(branch.getBranchName());

			// Setting of the Employee Grade Number from the reportState
			viewModel.setEmployeeGrade(e.getEmployeeGrade());

			// Submission Date of the expense. Cannot be null as all the draft vouchers are
			// filtered out.
			viewModel.setSubmitDate(e.getSubmitDate());

			// Payment information (UTR reference number UTR date)
			Optional<Payment> paymentInfo = paymentRepository.findByExpenseReportId(e.getId());

			// Here we get the payment data from the payment repository as in the expense
			// report we only have
			// the payment date, not its reference number.
			if (paymentInfo.isPresent()) {
				viewModel.setPaymentDate(paymentInfo.get().getPaymentDate());
				viewModel.setPaymentReference(paymentInfo.get().getPaymentReference());
				// Same logic as the original applies, only instead checking if the id exists in
				// the payment repo
				// instead of a date being present.
				viewModel
						.setPaymentTAT(ChronoUnit.DAYS.between(paymentInfo.get().getPaymentDate(), e.getCreatedDate()));
			} else {
				// If expense has not been paid yet, keep the payment date as null and the
				// payment reference code as Not paid.
				viewModel.setPaymentReference("Not Paid");
			}

			return viewModel;
		}).collect(Collectors.toList());
	}


	public List<TATReportJasperViewModelV2> prepareTATReportV2(long companyCode, LocalDate fromDate, LocalDate toDate,
															   ReportStatus voucherStatus, ReportStatus revokeStatus, String entity) {
		List<TatReport> expenseTATReportsFromRepo = new ArrayList<TatReport>();
		List<TATReportJasperViewModelV2> resultSet = null;

		if (voucherStatus.name().equalsIgnoreCase("ALL")) {
			voucherStatus = ReportStatus.DRAFT;
			expenseTATReportsFromRepo = tatReportRepository.getByExpenseReportByTATSelectionForAll(companyCode, fromDate,
					toDate, voucherStatus, revokeStatus, entity);
		} else {
			expenseTATReportsFromRepo = tatReportRepository.getByExpenseReportByTATSelection(companyCode, fromDate,
					toDate, voucherStatus, entity);
		}

		resultSet = buildTATReportViewModel(expenseTATReportsFromRepo);
		return resultSet;
	}

	private List<TATReportJasperViewModelV2> buildTATReportViewModel(List<TatReport> expenseTATReportsFromRepo) {
		List<TATReportJasperViewModelV2> list = new ArrayList<>();
		expenseTATReportsFromRepo.forEach(obj -> {
			try {
				TATReportJasperViewModelV2 view = new TATReportJasperViewModelV2();
				BeanUtils.copyProperties(obj, view);

				// If a voucher has been sent back then the document number of the identifier
				// should have a '*' appended to end of it like in the website.
				if (null != obj.getReportStatus()) {
					String documentIdentifier = obj.getReportStatus() == ReportStatus.SENT_BACK ? "*" + obj.getDocumentIdentifier()
							: obj.getDocumentIdentifier();
					view.setDocumentIdentifier(documentIdentifier);
				}


				view.setCreatedDate(obj.getCreatedDate());

				if (null != obj.getReportStatus()) {
					view.setVoucherStatus(obj.getReportStatus().name());
				}
				view.setExpenseType(obj.getExpenseType());
				view.setVoucherAmount(obj.getReportClaimAmount());
				view.setEmployeeCostCenter(obj.getEmployeeCostCenter());
				view.setDepartment(obj.getEmployeeDepartment());
				view.setPendingAt(obj.getPendingAt());
				if (null != obj.getEntity()) {
					view.setEntity(obj.getEntity().toUpperCase());
				}

				if (null != obj.getAllReportStateDataForReport()) {
					view = setTatReportValues(obj, view);
				}

				if (obj.getEmployeeBranch() != null) {
					view.setLocation(this.branchCodeToBranchName.get(obj.getEmployeeBranch()));
				}


				//			// Setting of the Employee Grade Number from the reportState
				//			view.setEmployeeGrade(obj.getEmployeeGrade());
				//
				//			// Submission Date of the expense. Cannot be null as all the draft vouchers are
				//			// filtered out.
				//			view.setSubmitDate(obj.getSubmitDate());

				if (null == view.getPaymentReference()) {
					view.setPaymentReference("Not Paid");
					//				view.setPaymentTAT(null);
					//				view.setPaymentDate(null);
				}

				list.add(view);
			} catch (Exception e) {
				System.out.println(e);
				System.out.println(obj.getDocumentIdentifier());
			}
		});
		return list;
	}

	private TATReportJasperViewModelV2 setTatReportValues(TatReport tatReportRow, TATReportJasperViewModelV2 view) {
		String input = tatReportRow.getAllReportStateDataForReport();
		String reportStateRowSeparator = "\\|"; // Since '|' is a special character in regular expressions, it needs to be escaped
		String reportStateColumnSeparator = ":";

		List<String> reportStateRow = null;
		String[] allStatesString = null;

		LocalDate actionDate = null;
		LocalDate createdDate = null;
		String approverName = null;
		String tatTime = null;
		Integer channel = null;

		Long totalBATAT = 0L;
		Long totalCheckerTAT = 0L;
		// paymentTAT already calculated in procedure
		Long totalPaymentTAT = null == view.getPaymentTAT() || view.getPaymentTAT().isEmpty() ? 0L : Long.parseLong(view.getPaymentTAT());
		Long totalTAT = 0L;
		int tatReportStateValueSize = 5;

		allStatesString = input.split(reportStateRowSeparator);

		for (String reportState : allStatesString) {
			reportStateRow = parseStringSeparatorHandleNullOrEmptyValues(reportState, tatReportStateValueSize, reportStateColumnSeparator.charAt(0));
			// Any value with 'raw' suffix will be subject to further processing and thus final value will come after null checks.
			approverName = reportStateRow.get(0);
			String actionDateRawValue = reportStateRow.get(1);
			String tatTimeRawValue = reportStateRow.get(2);
			String approverEmployeeCode = reportStateRow.get(3);
			String channelRawValue = reportStateRow.get(4);

			actionDate = null == actionDateRawValue || actionDateRawValue.isEmpty() ? null : LocalDate.parse(actionDateRawValue);
			tatTime = null == tatTimeRawValue || tatTimeRawValue.isEmpty() ? "" : tatTimeRawValue;
			channel = null == channelRawValue || channelRawValue.isEmpty() ? null : Integer.parseInt(channelRawValue);

			if (tatReportRow.getReportStatus() == ReportStatus.PAID) {

				if (channel == StateChannel.APPROVER.ordinal()) {
					if (view.getB1ApproverDate() == null) {
						view.setB1ApproverDate(actionDate);
						view.setTat1(tatTime);
						view.setB1ApproverName(approverName);

					} else if (view.getB2ApproverDate() == null) {
						view.setB2ApproverDate(actionDate);
						view.setTat2(
								tatTime);
						view.setB2ApproverName(
								approverName);

					} else if (view.getB3ApproverDate() == null) {
						view.setB3ApproverDate(actionDate);
						view.setTat3(
								tatTime);
						view.setB3ApproverName(
								approverName);

					} else if (view.getB4ApproverDate() == null) {
						view.setB4ApproverDate(actionDate);
						view.setTat4(
								tatTime);
						view.setB4ApproverName(
								approverName);
					} else {
						view.setB5ApproverDate(actionDate);
						view.setTat5(
								tatTime);
						view.setB5ApproverName(
								approverName);
					}

					if (!tatTime.isEmpty()) {
						totalBATAT += Long.parseLong(tatTime);
					}


				} else if (channel == StateChannel.CHECKER.ordinal()) {
					if (view.getChecker1ApproverDate() == null) {
						view.setChecker1ApproverDate(actionDate);
						view.setTat6(
								tatTime);
						view.setChecker1ApproverName(approverName);
					} else {
						view.setChecker2ApproverDate(actionDate);
						view.setTat7(
								tatTime);
						view.setChecker2ApproverName(approverName);
					}
				}

				if (!tatTime.isEmpty()) {
					totalCheckerTAT += Long.parseLong(tatTime);
				}

			}
			// Calculate the total aggreates only if the report has been paid.
			view.setTotalBATAT(String.valueOf(totalBATAT));
			view.setTotalCheckerTAT(String.valueOf(totalCheckerTAT));

			totalTAT = totalBATAT + totalCheckerTAT + totalPaymentTAT;
			// paymentTAT already calculated in procedure
			view.setTotalTAT(String.valueOf(totalTAT));
		}
		return view;
	}

	private List<String> parseStringSeparatorHandleNullOrEmptyValues(String ordersString, int n, char separator) {
		List<String> ordersList = new ArrayList<>(n);

		if (ordersString != null) {
			int count = 0;
			int start = 0;
			int length = ordersString.length();

			for (int i = 0; i < length && count < n; i++) {
				char currentChar = ordersString.charAt(i);

				if (currentChar == separator) {
					// Found a separator character
					if (start != i) {
						// Extract the order substring and trim it
						String order = ordersString.substring(start, i).trim();
						// Add the order to the list, or null if empty
						ordersList.add(order.isEmpty() ? null : order);
					} else {
						// Handle empty order (consecutive separators)
						ordersList.add(null);
					}
					// Update the start index for the next substring
					start = i + 1;
					// Increment the count of processed orders
					count++;
				} else if (i == length - 1) {
					// Reached the end of the string, handle the last order
					String order = ordersString.substring(start).trim();
					ordersList.add(order.isEmpty() ? null : order);
					count++;
				}
			}

			// Fill the remaining slots with null if necessary
			while (count < n) {
				ordersList.add(null);
				count++;
			}
		} else {
			// Handle null ordersString, add null values for the expected number of items
			for (int i = 0; i < n; i++) {
				ordersList.add(null);
			}
		}

		return ordersList;
	}

}
