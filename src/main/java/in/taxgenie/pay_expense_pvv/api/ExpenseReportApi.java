package in.taxgenie.pay_expense_pvv.api;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextFactory;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.response.interfaces.factory.IServerResponseFactory;
import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithBody;
import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithoutBody;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseReportApproverService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseReportCommonService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseReportUserService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("api/v1/expense-report")
@Api(value = "Expense Report Actions", tags = {"Expense Report API"})
public class ExpenseReportApi {
    private final IExpenseReportUserService reportUserService;
    private final IExpenseReportApproverService reportApproverService;
    private final IExpenseReportCommonService reportCommonService;
    private final IAuthContextFactory authContextFactory;
    private final IServerResponseFactory serverResponseFactory;
    private final Logger logger;

    public ExpenseReportApi(
            IExpenseReportUserService reportUserService,
            IExpenseReportApproverService reportApproverService,
            IExpenseReportCommonService reportCommonService,
            IAuthContextFactory authContextFactory,
            IServerResponseFactory serverResponseFactory
    ) {
        this.reportUserService = reportUserService;
        this.reportApproverService = reportApproverService;
        this.reportCommonService = reportCommonService;
        this.authContextFactory = authContextFactory;
        this.serverResponseFactory = serverResponseFactory;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @PreAuthorize("hasAnyAuthority('user', 'approver', 'checker')")
    @ApiOperation(value = "Get the Expense Report")
    @GetMapping("get/{reportId}")
    public ResponseEntity<IServerResponseWithBody<ExpenseReportViewModel>> getById(@PathVariable long reportId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportUserService.getById(reportId, auth)
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('user', 'approver', 'checker')")
    @ApiOperation(value = "Get limit consumptions by report id")
    @GetMapping("get/limit-consumption/{reportId}")
    public ResponseEntity<IServerResponseWithBody<List<ReportLimitConsumptionViewModel>>> getLimitConsumption(@PathVariable long reportId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportCommonService.getConsumption(reportId, auth)
                        )
                );
    }


    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Submit an existing Expense Report")
    @PutMapping("save")
    public ResponseEntity<IServerResponseWithoutBody> save(@RequestBody ExpenseReportUpdateViewModel viewModel) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        reportUserService.save(viewModel, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true
                        )
                );
    }

    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Submit an existing Expense Report via UPI")
    @PutMapping("upi/save")
    public ResponseEntity<IServerResponseWithoutBody> saveFromUPI(@RequestBody ExpenseReportUpdateViewModel viewModel) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        reportUserService.save(viewModel, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('admin','user', 'approver', 'checker')")
    @ApiOperation(value = "Get the states of a Expense Report")
    @GetMapping("get/states/{reportId}")
    public ResponseEntity<IServerResponseWithBody<List<ReportStateViewModel>>> getStatesById(@PathVariable long reportId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportCommonService.getStates(reportId, auth)
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Get user queue")
    @PostMapping("create/{metadataId}")
    public ResponseEntity<IServerResponseWithBody<ExpenseReportViewModel>> create(@PathVariable long metadataId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportUserService.create(metadataId, auth)
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Create and expense report from UPI call")
    @PostMapping("upi/create/{metadataId}")
    public ResponseEntity<IServerResponseWithBody<ExpenseReportViewModel>> createFromUPI(@PathVariable long metadataId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportUserService.create(metadataId, auth)
                        )
                );
    }



    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Submit an existing Expense Report")
    @PutMapping("submit/{reportId}/{saveDate}")
    public ResponseEntity<IServerResponseWithBody<ExpenseReportSubmitResultViewModel>> submit(@PathVariable long reportId, @PathVariable long saveDate) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportUserService.submit(reportId, auth, saveDate)
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Revoke an existing Expense Report")
    @PutMapping("revoke/{reportId}")
    public ResponseEntity<IServerResponseWithoutBody> revoke(@PathVariable long reportId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        reportUserService.revoke(reportId, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true
                        )
                );
    }

	@PreAuthorize("hasAnyAuthority('user')")
	@ApiOperation(value = "Get user queue")
	@GetMapping("user/queue")
	public ResponseEntity<IServerResponseWithBody<List<ExpenseReportViewModel>>> getUserQueue() {
		IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
		logger.info(" Auth in User queue " + auth.getCompanyCode() + "  " + auth.getUserId() + "  "
				+ auth.getAuthorities());
		return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
				.body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
						StaticDataRegistry.HTTP_MESSAGE_OK, true, reportUserService.getQueue(auth)));
	}

	@PreAuthorize("hasAnyAuthority('user')")
	@ApiOperation(value = "Get user queue")
	@PostMapping("user/queue")
	public ResponseEntity<IServerResponseWithBody<ExpenseReportViewModelV2>> getUserQueueV2(
			@RequestBody QueueFilterViewModel viewModel) {
		IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
		logger.info(" Auth in User queue " + auth.getCompanyCode() + "  " + auth.getUserId() + "  "
				+ auth.getAuthorities());
		return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
				.body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
						StaticDataRegistry.HTTP_MESSAGE_OK, true, reportUserService.getQueueV2(auth, viewModel)));
	}


    @ApiOperation(value = "Get approver queue")
    @PreAuthorize("hasAuthority('approver')")
    @GetMapping("approver/queue")
    public ResponseEntity<IServerResponseWithBody<List<ExpenseReportViewModel>>> getApproverQueue() {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        logger.info(" Auth in Approver queue "+auth.getCompanyCode()+ "  "+auth.getUserId() + "  "+auth.getAuthorities() );
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportApproverService.getApproverQueue(auth)
                        )
                );
				
	}
	@ApiOperation(value = "Get approver queue")
	@PreAuthorize("hasAuthority('approver')")
	@PostMapping("approver/queue")
	public ResponseEntity<IServerResponseWithBody<ExpenseReportViewModelV2>> getApproverQueueV2(@RequestBody QueueFilterViewModel viewModel) {
		IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
		logger.info(" Auth in Approver queue " + auth.getCompanyCode() + "  " + auth.getUserId() + "  "
				+ auth.getAuthorities());
		return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
				.body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
						StaticDataRegistry.HTTP_MESSAGE_OK, true, reportApproverService.getApproverQueueV2(auth, viewModel)));
	}

    @ApiOperation(value = "Get checker queue")
    @PreAuthorize("hasAuthority('checker')")
    @GetMapping("checker/queue")
    public ResponseEntity<IServerResponseWithBody<List<ExpenseReportViewModel>>> getCheckerQueur() {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        logger.info(" Auth in Checker queue "+auth.getCompanyCode()+ "  "+auth.getUserId() + "  "+auth.getAuthorities() );
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportApproverService.getCheckerQueue(auth)
                        )
                );
    }

	@ApiOperation(value = "Get checker queue")
	@PreAuthorize("hasAuthority('checker')")
	@PostMapping("checker/queue")
	public ResponseEntity<IServerResponseWithBody<ExpenseReportViewModelV2>> getCheckerQueueV2(
			@RequestBody QueueFilterViewModel viewModel) {
		IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
		logger.info(" Auth in Checker queue " + auth.getCompanyCode() + "  " + auth.getUserId() + "  "
				+ auth.getAuthorities());
		return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
				.body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
						StaticDataRegistry.HTTP_MESSAGE_OK, true,
						reportApproverService.getCheckerQueueV2(auth, viewModel)));
	}

	@PreAuthorize("hasAnyAuthority('admin', 'report')")
	@ApiOperation(value = "Get admin queue")
	@GetMapping("admin/queue")
	public ResponseEntity<IServerResponseWithBody<List<ExpenseReportViewModel>>> getAdminQueue() {
		IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        logger.info(" Auth in Admin queue "+auth.getCompanyCode()+ "  "+auth.getUserId() + "  "+auth.getAuthorities() );
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportApproverService.getAdminQueue(auth)
                        )
                );
	}

	@PreAuthorize("hasAnyAuthority('admin', 'report')")
	@ApiOperation(value = "Get admin queue")
	@PostMapping("admin/queue")
	public ResponseEntity<IServerResponseWithBody<ExpenseReportViewModelV2>> getAdminQueueV2(
			@RequestBody QueueFilterViewModel viewModel) {
		IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
		logger.info(" Auth in Admin queue " + auth.getCompanyCode() + "  " + auth.getUserId() + "  "
				+ auth.getAuthorities());
		return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
				.body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
						StaticDataRegistry.HTTP_MESSAGE_OK, true,
						reportApproverService.getAdminQueueV2(auth, viewModel)));
	}


    @ApiOperation(value = "Approve an Expense Report")
    @PreAuthorize("hasAuthority('approver')")
    @PostMapping("approver/approve")
    public ResponseEntity<IServerResponseWithoutBody> approverApprove(@RequestBody ReportStateCreateViewModel viewModel) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        reportApproverService.approve(viewModel, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true
                        )
                );
    }

    @ApiOperation(value = "Approve an Expense Report")
    @PreAuthorize("hasAuthority('approver')")
    @PostMapping("approver/approve/bulk")
    public ResponseEntity<IServerResponseWithoutBody> approverApproveBulk(@RequestBody BulkApproveContainerViewModel viewModel) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        reportApproverService.approveBulk(viewModel, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true
                        )
                );
    }


    @ApiOperation(value = "Send back an Expense Report")
    @PreAuthorize("hasAuthority('approver')")
    @PostMapping("approver/send-back")
    public ResponseEntity<IServerResponseWithBody<ExpenseReportSendBackResultViewModel>> approverSendBack(@RequestBody ReportStateCreateViewModel viewModel) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportApproverService.approverSendBack(viewModel, auth)
                        )
                );
    }

    @ApiOperation(value = "Approve an Expense Report")
    @PreAuthorize("hasAuthority('checker')")
    @PostMapping("checker/consent")
    public ResponseEntity<IServerResponseWithoutBody> checkerConsent(@RequestBody ReportStateCreateViewModel viewModel) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        reportApproverService.checkerConsent(viewModel, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true
                        )
                );
    }

    @ApiOperation(value = "Send back an Expense Report")
    @PreAuthorize("hasAuthority('checker')")
    @PostMapping("checker/send-back")
    public ResponseEntity<IServerResponseWithBody<ExpenseReportSendBackResultViewModel>> checkerSendBack(@RequestBody ReportStateCreateViewModel viewModel) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportApproverService.checkerSendBack(viewModel, auth)
                        )
                );
    }

    @ApiOperation(value = "Get similar expense reports")
    @PreAuthorize("hasAnyAuthority('admin','user','approver', 'checker')")
    @GetMapping("similar/{reportId}")
    public ResponseEntity<IServerResponseWithBody<List<ExpenseReportViewModel>>> getSimilarExpenseReports(@PathVariable long reportId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                reportApproverService.getSimilarExpenses(reportId, auth)
                        )
                );
    }

}
