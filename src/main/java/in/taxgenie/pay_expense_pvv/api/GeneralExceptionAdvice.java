package in.taxgenie.pay_expense_pvv.api;

import in.taxgenie.pay_expense_pvv.exceptions.*;
import in.taxgenie.pay_expense_pvv.response.interfaces.factory.IServerResponseFactory;
import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithoutBody;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.validation.ValidationErrorPageViewModel;
import in.taxgenie.pay_expense_pvv.validation.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.validation.ConstraintViolationException;

@ControllerAdvice
public class GeneralExceptionAdvice {
    private final IServerResponseFactory responseFactory;
    private final Logger logger;

    public GeneralExceptionAdvice(IServerResponseFactory responseFactory) {
        this.responseFactory = responseFactory;
        this.logger = LoggerFactory.getLogger(GeneralExceptionAdvice.class);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ValidationErrorPageViewModel> handleValidationException(MethodArgumentNotValidException exception) {
        logger.info("Exception caught: {}; message: {}", exception.getClass(), exception.getMessage());
        return ResponseEntity.badRequest().body(ValidationUtils.getValidationErrorPage(exception, exception.getBindingResult().getTarget().getClass()));
    }

    @ExceptionHandler(RecordNotFoundException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleRecordNotFoundException(RecordNotFoundException exception) {
        logger.info("Exception caught: {}; message: {}", exception.getClass(), exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_NOT_FOUND)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_NOT_FOUND,
                                exception.getMessage(),
                                false
                        )
                );
    }

    @ExceptionHandler(MasterRecordNotFoundException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleMasterRecordNotFoundException(MasterRecordNotFoundException exception) {
        logger.info("Exception caught: {}; message: {}", exception.getClass(), exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_NOT_FOUND)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_NOT_FOUND,
                                exception.getMessage(),
                                false
                        )
                );
    }

    @ExceptionHandler(ContextAccessViolationException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleContextAccessViolationException(ContextAccessViolationException exception) {
        logger.info("Exception caught: {}; message: {}", exception.getClass(), exception.getMessage());
        //  returning 404 is deliberate
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_NOT_FOUND)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_NOT_FOUND,
                                exception.getMessage(),
                                false
                        )
                );
    }

    @ExceptionHandler(DomainInvariantException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleDomainInvariantException(DomainInvariantException exception) {
        logger.info("Exception caught: {}; message: {}", exception.getClass(), exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST,
                                exception.getMessage(),
                                false
                        )
                );
    }

    @ExceptionHandler(RestConsumptionFailedException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleRestConsumptionFailedException(RestConsumptionFailedException exception) {
        logger.info("Exception caught: {}; message: {}", exception.getClass(), exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_SERVICE_ERROR)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_SERVICE_ERROR,
                                exception.getMessage(),
                                false
                        )
                );
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleAccessDeniedException(AccessDeniedException exception) {
        logger.info("Exception caught: {}; message: {}", exception.getClass(), exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_UNAUTHORIZED)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_UNAUTHORIZED,
                                exception.getMessage(),
                                false
                        )
                );
    }

    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleBadCredentialsException(BadCredentialsException exception) {
        logger.info("Exception caught: {}; message: {}", exception.getClass(), exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_UNAUTHENTICATED)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_UNAUTHENTICATED,
                                StaticDataRegistry.HTTP_MESSAGE_NOT_AUTHENTICATED,
                                false
                        )
                );
    }

    @ExceptionHandler(DuplicateRecordFoundException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleDuplicateRecordFoundException(DuplicateRecordFoundException exception) {
        logger.warn("Duplicate submission attempt: {}", exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST,
                                "This report has already been submitted. Please refresh and try again.",
                                false
                        )
                );
    }

    @ExceptionHandler(OptimisticLockingFailureException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleOptimisticLockingFailureException(OptimisticLockingFailureException exception) {
        logger.warn("Optimistic locking failure: {}", exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST,
                                "The report has been modified by another user. Please refresh and try again.",
                                false
                        )
                );
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleDataIntegrityViolationException(DataIntegrityViolationException exception) {
        logger.error("Data integrity violation: {}", exception.getMessage(), exception);

        // Check if it's a duplicate key violation (our unique constraint)
        if (exception.getMessage() != null && exception.getMessage().contains("uk_report_state_save_time")) {
            return ResponseEntity
                    .status(StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST)
                    .body(
                            responseFactory.getServerResponseWithoutBody(
                                    StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST,
                                    "This report has already been submitted. Please refresh and try again.",
                                    false
                            )
                    );
        }

        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST,
                                "Data validation error. Please check your input and try again.",
                                false
                        )
                );
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleConstraintViolationException(ConstraintViolationException exception) {
        logger.warn("Constraint violation: {}", exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST,
                                "Invalid input parameters: " + exception.getMessage(),
                                false
                        )
                );
    }



    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<IServerResponseWithoutBody> handleRuntimeException(RuntimeException exception) {
        logger.info("Exception caught: {}; message: {}", exception.getClass(), exception.getMessage());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST)
                .body(
                        responseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_BAD_REQUEST,
                                exception.getMessage(),
                                false
                        )
                );
    }
}
