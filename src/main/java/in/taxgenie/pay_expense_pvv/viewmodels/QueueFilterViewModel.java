package in.taxgenie.pay_expense_pvv.viewmodels;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.Positive;

public class QueueFilterViewModel {

	@Positive
	private Integer page = 0;

	@Positive
	private Integer pageSize = 5;

	private String searchString;

	private Map<String, List<String>> searchCriteria;

	private String sortByColumnName;

	private Integer sortOrder;

	public String getSortByColumnName() {
		return sortByColumnName;
	}

	public void setSortByColumnName(String sortByColumnName) {
		this.sortByColumnName = sortByColumnName;
	}

	public Integer getSortOrder() {
		return sortOrder;
	}

	public void setSortOrder(Integer sortOrder) {
		this.sortOrder = sortOrder;
	}

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public String getSearchString() {
		return searchString;
	}

	public void setSearchString(String searchString) {
		this.searchString = searchString;
	}

	public Map<String, List<String>> getSearchCriteria() {
		return searchCriteria;
	}

	public void setSearchCriteria(Map<String, List<String>> searchCriteria) {
		this.searchCriteria = searchCriteria;
	}

}
