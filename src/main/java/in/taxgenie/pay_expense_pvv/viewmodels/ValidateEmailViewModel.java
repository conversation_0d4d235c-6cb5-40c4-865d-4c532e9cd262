package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

public class ValidateEmailViewModel {

    private String message;
    private List<ValidateEmailDetailsViewModel> data;

    public ValidateEmailViewModel(String message, List<ValidateEmailDetailsViewModel> data) {
        this.message = message;
        this.data = data;
    }

    public static ValidateEmailViewModel success() {
        return new ValidateEmailViewModel("Success", Collections.emptyList());
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<ValidateEmailDetailsViewModel> getData() {
        return data;
    }

    public void setData(List<ValidateEmailDetailsViewModel> data) {
        this.data = data;
    }
}
