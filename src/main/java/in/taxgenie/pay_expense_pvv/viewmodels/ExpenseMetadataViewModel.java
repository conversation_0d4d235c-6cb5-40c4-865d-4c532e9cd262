package in.taxgenie.pay_expense_pvv.viewmodels;

public class ExpenseMetadataViewModel {
    private long id;
    private long companyCode;
    private String description;
    private String expenseType;
    private String expenseTypePrefix;
    private String expenseGroup;
    private String expenseGroupPrefix;
    private int definitionsCount;
    private int subgroupsCount;
    private int rulesCount;
    private boolean isFrozen;
    private boolean purposeRequired;
    private String applicableGender;
    private Integer limitDays;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getExpenseTypePrefix() {
        return expenseTypePrefix;
    }

    public void setExpenseTypePrefix(String expenseTypePrefix) {
        this.expenseTypePrefix = expenseTypePrefix;
    }

    public String getExpenseGroup() {
        return expenseGroup;
    }

    public void setExpenseGroup(String expenseGroup) {
        this.expenseGroup = expenseGroup;
    }

    public String getExpenseGroupPrefix() {
        return expenseGroupPrefix;
    }

    public void setExpenseGroupPrefix(String expenseGroupPrefix) {
        this.expenseGroupPrefix = expenseGroupPrefix;
    }

    public int getDefinitionsCount() {
        return definitionsCount;
    }

    public void setDefinitionsCount(int definitionsCount) {
        this.definitionsCount = definitionsCount;
    }

    public int getSubgroupsCount() {
        return subgroupsCount;
    }

    public void setSubgroupsCount(int subgroupsCount) {
        this.subgroupsCount = subgroupsCount;
    }

    public int getRulesCount() {
        return rulesCount;
    }

    public void setRulesCount(int rulesCount) {
        this.rulesCount = rulesCount;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public boolean isPurposeRequired() {
        return purposeRequired;
    }

    public void setPurposeRequired(boolean purposeRequired) {
        this.purposeRequired = purposeRequired;
    }

    public String getApplicableGender() {
        return applicableGender;
    }

    public void setApplicableGender(String applicableGender) {
        this.applicableGender = applicableGender;
    }

    public Integer getLimitDays() {
        return limitDays;
    }

    public void setLimitDays(Integer limitDays) {
        this.limitDays = limitDays;
    }
}
