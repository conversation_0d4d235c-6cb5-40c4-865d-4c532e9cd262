package in.taxgenie.pay_expense_pvv.viewmodels;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

public class ExpenseSubgroupUpdateViewModel {
    private long id;

    @NotBlank
    @Size(max=50)
    private String expenseCode;
    @NotBlank
    @Size(max=50)
    private String expenseSubgroup;
    @NotBlank
    @Size(max=5)
    private String expenseSubgroupPrefix;
    @Size(max=50)
    private String expenseGenre;
    @Size(max=5)
    private String expenseGenrePrefix;

    private boolean isLocationRequired;
    @Size(max=15)
    private String glAccountCode;
    @Size(max=200)
    private String description;

    @Size(max = 100)
    private String frequency;

    private boolean merchantRequired;

    //  flags
    private boolean isSourceLocationApplicable;
    private boolean isDestinationLocationApplicable;
    private boolean isGstEntryAllowed;
    private boolean isStandardDeductionApplicable;
    private boolean isDateRangeApplicable;
    private boolean isFrequencyApplicable;
    private boolean isExpenseIdentifierApplicable;
    private boolean isTransportDescriptorApplicable;
    private boolean isMobilityDescriptorApplicable;
    private boolean isTravelDescriptorApplicable;

    private String applicableGender;


    //  Common
    private boolean isFrozen;
    private long expenseMetadataId;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getExpenseCode() {
        return expenseCode;
    }

    public void setExpenseCode(String expenseCode) {
        this.expenseCode = expenseCode;
    }

    public String getExpenseSubgroup() {
        return expenseSubgroup;
    }

    public void setExpenseSubgroup(String expenseSubgroup) {
        this.expenseSubgroup = expenseSubgroup;
    }

    public String getExpenseSubgroupPrefix() {
        return expenseSubgroupPrefix;
    }

    public void setExpenseSubgroupPrefix(String expenseSubgroupPrefix) {
        this.expenseSubgroupPrefix = expenseSubgroupPrefix;
    }

    public String getExpenseGenre() {
        return expenseGenre;
    }

    public void setExpenseGenre(String expenseGenre) {
        this.expenseGenre = expenseGenre;
    }

    public String getExpenseGenrePrefix() {
        return expenseGenrePrefix;
    }

    public void setExpenseGenrePrefix(String expenseGenrePrefix) {
        this.expenseGenrePrefix = expenseGenrePrefix;
    }

    public boolean isLocationRequired() {
        return isLocationRequired;
    }

    public void setLocationRequired(boolean locationRequired) {
        isLocationRequired = locationRequired;
    }

    public String getGlAccountCode() {
        return glAccountCode;
    }

    public void setGlAccountCode(String glAccountCode) {
        this.glAccountCode = glAccountCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isSourceLocationApplicable() {
        return isSourceLocationApplicable;
    }

    public void setSourceLocationApplicable(boolean sourceLocationApplicable) {
        isSourceLocationApplicable = sourceLocationApplicable;
    }

    public boolean isDestinationLocationApplicable() {
        return isDestinationLocationApplicable;
    }

    public void setDestinationLocationApplicable(boolean destinationLocationApplicable) {
        isDestinationLocationApplicable = destinationLocationApplicable;
    }

    public boolean isGstEntryAllowed() {
        return isGstEntryAllowed;
    }

    public void setGstEntryAllowed(boolean gstEntryAllowed) {
        isGstEntryAllowed = gstEntryAllowed;
    }

    public boolean isStandardDeductionApplicable() {
        return isStandardDeductionApplicable;
    }

    public void setStandardDeductionApplicable(boolean standardDeductionApplicable) {
        isStandardDeductionApplicable = standardDeductionApplicable;
    }

    public boolean isDateRangeApplicable() {
        return isDateRangeApplicable;
    }

    public void setDateRangeApplicable(boolean dateRangeApplicable) {
        isDateRangeApplicable = dateRangeApplicable;
    }

    public boolean isFrequencyApplicable() {
        return isFrequencyApplicable;
    }

    public void setFrequencyApplicable(boolean frequencyApplicable) {
        isFrequencyApplicable = frequencyApplicable;
    }

    public boolean isExpenseIdentifierApplicable() {
        return isExpenseIdentifierApplicable;
    }

    public void setExpenseIdentifierApplicable(boolean expenseIdentifierApplicable) {
        isExpenseIdentifierApplicable = expenseIdentifierApplicable;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public boolean isMerchantRequired() {
        return merchantRequired;
    }

    public void setMerchantRequired(boolean merchantRequired) {
        this.merchantRequired = merchantRequired;
    }

    public String getApplicableGender() {
        return applicableGender;
    }

    public void setApplicableGender(String applicableGender) {
        this.applicableGender = applicableGender;
    }

    public boolean isTransportDescriptorApplicable() {
        return isTransportDescriptorApplicable;
    }

    public void setTransportDescriptorApplicable(boolean transportDescriptorApplicable) {
        isTransportDescriptorApplicable = transportDescriptorApplicable;
    }

    public boolean isMobilityDescriptorApplicable() {
        return isMobilityDescriptorApplicable;
    }

    public void setMobilityDescriptorApplicable(boolean mobilityDescriptorApplicable) {
        isMobilityDescriptorApplicable = mobilityDescriptorApplicable;
    }

    public boolean isTravelDescriptorApplicable() {
        return isTravelDescriptorApplicable;
    }

    public void setTravelDescriptorApplicable(boolean travelDescriptorApplicable) {
        isTravelDescriptorApplicable = travelDescriptorApplicable;
    }
}
