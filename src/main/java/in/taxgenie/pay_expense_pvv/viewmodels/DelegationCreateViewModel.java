package in.taxgenie.pay_expense_pvv.viewmodels;

import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDate;

public class DelegationCreateViewModel {
    @FutureOrPresent
    private LocalDate startDate;

    @FutureOrPresent
    private LocalDate endDate;

    @NotBlank
    @Size(max = 200)
    private String fromApprover;

    @NotBlank
    @Size(max = 200)
    private String toApprover;

    @Size(max = 200)
    private String remarks;

    private boolean assignUnapproved;

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getFromApprover() {
        return fromApprover;
    }

    public void setFromApprover(String fromApprover) {
        this.fromApprover = fromApprover;
    }

    public String getToApprover() {
        return toApprover;
    }

    public void setToApprover(String toApprover) {
        this.toApprover = toApprover;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public boolean isAssignUnapproved() {
        return assignUnapproved;
    }

    public void setAssignUnapproved(boolean assignUnapproved) {
        this.assignUnapproved = assignUnapproved;
    }
}
