package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.PaidStatus;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.time.LocalDate;
import java.time.ZonedDateTime;

public class ValidateEmailDetailsViewModel {
//    private long id;
//    private String documentIdentifier;
//
//    private LocalDate createdDate;
//    private LocalDate submitDate;
//    private LocalDate startDate;
//    private LocalDate endDate;
//
//    private String reportTitle;
//    private String description;
//
//    private double reportClaimAmount;
//    private ReportStatus reportStatus;
//    private int actionLevel;
//    private boolean containsDeviation;
//    private String deviationRemarks;
//
//    private Double reportTaxableAmount;
//
//    //  Employee master data
//    private String firstName;
//    private String middleName;
//    private String lastName;
//    private String employeeEmail;
//    private String employeeCode;

    private long id;
    private long companyCode;

    //  Core fields
    
    private String documentIdentifier;

    private LocalDate createdDate;
    private LocalDate submitDate;
    private LocalDate startDate;
    private LocalDate endDate;

    
    private String reportTitle;
    
    private String description;
    private double reportClaimAmount = 0;

    private ReportStatus reportStatus = ReportStatus.DRAFT;
    private int actionLevel = 0;
    private String deviationRemarks;
    private boolean containsDeviation;
    private boolean containsSentBack;
    private String expenseType;

    
    private String currentApproverFirstName;
    
    private String currentApproverLastName;
    
    private String currentApproverEmployeeCode;


    //  Employee master data
    
    private String firstName;
    
    private String middleName;
    
    private String lastName;

    private String employeeEmail;
    
    private String employeeCode;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDate getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDate submitDate) {
        this.submitDate = submitDate;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getReportClaimAmount() {
        return reportClaimAmount;
    }

    public void setReportClaimAmount(double reportClaimAmount) {
        this.reportClaimAmount = reportClaimAmount;
    }

    public ReportStatus getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(ReportStatus reportStatus) {
        this.reportStatus = reportStatus;
    }

    public int getActionLevel() {
        return actionLevel;
    }

    public void setActionLevel(int actionLevel) {
        this.actionLevel = actionLevel;
    }

    public String getDeviationRemarks() {
        return deviationRemarks;
    }

    public void setDeviationRemarks(String deviationRemarks) {
        this.deviationRemarks = deviationRemarks;
    }

    public boolean isContainsDeviation() {
        return containsDeviation;
    }

    public void setContainsDeviation(boolean containsDeviation) {
        this.containsDeviation = containsDeviation;
    }

    public boolean isContainsSentBack() {
        return containsSentBack;
    }

    public void setContainsSentBack(boolean containsSentBack) {
        this.containsSentBack = containsSentBack;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getCurrentApproverFirstName() {
        return currentApproverFirstName;
    }

    public void setCurrentApproverFirstName(String currentApproverFirstName) {
        this.currentApproverFirstName = currentApproverFirstName;
    }

    public String getCurrentApproverLastName() {
        return currentApproverLastName;
    }

    public void setCurrentApproverLastName(String currentApproverLastName) {
        this.currentApproverLastName = currentApproverLastName;
    }

    public String getCurrentApproverEmployeeCode() {
        return currentApproverEmployeeCode;
    }

    public void setCurrentApproverEmployeeCode(String currentApproverEmployeeCode) {
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmployeeEmail() {
        return employeeEmail;
    }

    public void setEmployeeEmail(String employeeEmail) {
        this.employeeEmail = employeeEmail;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }
}
