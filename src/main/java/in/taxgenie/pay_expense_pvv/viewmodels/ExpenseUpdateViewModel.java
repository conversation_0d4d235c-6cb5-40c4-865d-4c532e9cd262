package in.taxgenie.pay_expense_pvv.viewmodels;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;

public class ExpenseUpdateViewModel {
    private long id;
    private LocalDate createdDate;
    private LocalDate expenseDate;
    private LocalDate startDate;
    private LocalDate endDate;
    private String employeeEmail;
    private String invoiceNumber;
    private String merchantDetails;
    private String description;
    private Double quantity;
    private String unitOfMeasure;
    private Double unitRate;
    private String sourceLocation;
    private String destinationLocation;
    private String expenseIdentifier;
    private String frequency;
    private String locationCategory;
    private String location;
    private Double standardDeductionRate;
    private Double standardDeductionAmount;
    private double invoiceAmount;
    private double applicableAmount;
    private boolean isApplicableAmountLesser;
    private Double limitAmount;
    private Double claimAmount;

    //  GST related
    private String gstin;
    private Double sgstRate;
    private Double cgstRate;
    private Double igstRate;
    private Double sgstAmount;
    private Double cgstAmount;
    private Double igstAmount;
    private Double taxableAmount;

    @JsonIgnore
    private MultipartFile document1;
    private MultipartFile document2;
    private MultipartFile document3;

    private String document1Identifier;
    private String document2Identifier;
    private String document3Identifier;

    //  Descriptors
    private String transportDescriptor;
    private String mobilityDescriptor;
    private String travelDescriptor;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDate getExpenseDate() {
        return expenseDate;
    }

    public void setExpenseDate(LocalDate expenseDate) {
        this.expenseDate = expenseDate;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getEmployeeEmail() {
        return employeeEmail;
    }

    public void setEmployeeEmail(String employeeEmail) {
        this.employeeEmail = employeeEmail;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getMerchantDetails() {
        return merchantDetails;
    }

    public void setMerchantDetails(String merchantDetails) {
        this.merchantDetails = merchantDetails;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public Double getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(Double unitRate) {
        this.unitRate = unitRate;
    }

    public String getSourceLocation() {
        return sourceLocation;
    }

    public void setSourceLocation(String sourceLocation) {
        this.sourceLocation = sourceLocation;
    }

    public String getDestinationLocation() {
        return destinationLocation;
    }

    public void setDestinationLocation(String destinationLocation) {
        this.destinationLocation = destinationLocation;
    }

    public String getExpenseIdentifier() {
        return expenseIdentifier;
    }

    public void setExpenseIdentifier(String expenseIdentifier) {
        this.expenseIdentifier = expenseIdentifier;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(String locationCategory) {
        this.locationCategory = locationCategory;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Double getStandardDeductionRate() {
        return standardDeductionRate;
    }

    public void setStandardDeductionRate(Double standardDeductionRate) {
        this.standardDeductionRate = standardDeductionRate;
    }

    public Double getStandardDeductionAmount() {
        return standardDeductionAmount;
    }

    public void setStandardDeductionAmount(Double standardDeductionAmount) {
        this.standardDeductionAmount = standardDeductionAmount;
    }

    public double getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(double invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public double getApplicableAmount() {
        return applicableAmount;
    }

    public void setApplicableAmount(double applicableAmount) {
        this.applicableAmount = applicableAmount;
    }

    public boolean isApplicableAmountLesser() {
        return isApplicableAmountLesser;
    }

    public void setApplicableAmountLesser(boolean applicableAmountLesser) {
        isApplicableAmountLesser = applicableAmountLesser;
    }

    public Double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(Double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public Double getClaimAmount() {
        return claimAmount;
    }

    public void setClaimAmount(Double claimAmount) {
        this.claimAmount = claimAmount;
    }

    public String getGstin() {
        return gstin;
    }

    public void setGstin(String gstin) {
        this.gstin = gstin;
    }

    public Double getSgstRate() {
        return sgstRate;
    }

    public void setSgstRate(Double sgstRate) {
        this.sgstRate = sgstRate;
    }

    public Double getCgstRate() {
        return cgstRate;
    }

    public void setCgstRate(Double cgstRate) {
        this.cgstRate = cgstRate;
    }

    public Double getIgstRate() {
        return igstRate;
    }

    public void setIgstRate(Double igstRate) {
        this.igstRate = igstRate;
    }

    public Double getSgstAmount() {
        return sgstAmount;
    }

    public void setSgstAmount(Double sgstAmount) {
        this.sgstAmount = sgstAmount;
    }

    public Double getCgstAmount() {
        return cgstAmount;
    }

    public void setCgstAmount(Double cgstAmount) {
        this.cgstAmount = cgstAmount;
    }

    public Double getIgstAmount() {
        return igstAmount;
    }

    public void setIgstAmount(Double igstAmount) {
        this.igstAmount = igstAmount;
    }

    public Double getTaxableAmount() {
        return taxableAmount;
    }

    public void setTaxableAmount(Double taxableAmount) {
        this.taxableAmount = taxableAmount;
    }

    public MultipartFile getDocument1() {
        return document1;
    }

    public void setDocument1(MultipartFile document1) {
        this.document1 = document1;
    }

    public MultipartFile getDocument2() {
        return document2;
    }

    public void setDocument2(MultipartFile document2) {
        this.document2 = document2;
    }

    public MultipartFile getDocument3() {
        return document3;
    }

    public void setDocument3(MultipartFile document3) {
        this.document3 = document3;
    }

    public String getDocument1Identifier() {
        return document1Identifier;
    }

    public void setDocument1Identifier(String document1Identifier) {
        this.document1Identifier = document1Identifier;
    }

    public String getDocument2Identifier() {
        return document2Identifier;
    }

    public void setDocument2Identifier(String document2Identifier) {
        this.document2Identifier = document2Identifier;
    }

    public String getDocument3Identifier() {
        return document3Identifier;
    }

    public void setDocument3Identifier(String document3Identifier) {
        this.document3Identifier = document3Identifier;
    }

    public String getTransportDescriptor() {
        return transportDescriptor;
    }

    public void setTransportDescriptor(String transportDescriptor) {
        this.transportDescriptor = transportDescriptor;
    }

    public String getMobilityDescriptor() {
        return mobilityDescriptor;
    }

    public void setMobilityDescriptor(String mobilityDescriptor) {
        this.mobilityDescriptor = mobilityDescriptor;
    }

    public String getTravelDescriptor() {
        return travelDescriptor;
    }

    public void setTravelDescriptor(String travelDescriptor) {
        this.travelDescriptor = travelDescriptor;
    }
}
