package in.taxgenie.pay_expense_pvv.viewmodels.queue;

import com.querydsl.core.annotations.QueryProjection;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;

import java.time.LocalDate;

public class QueueRowDTO {
    private final String approverEmployeeCode;
    private final String approverFirstName;
    private final Integer level;
    private final String approverLastName;
    private final Long reportId;
    private final String documentIdentifier;
    private final LocalDate createdDate;
    private final LocalDate submitDate;
    private final LocalDate startDate;
    private final LocalDate endDate;
    private final String reportTitle;
    private final String description;
    private final String purpose;
    private final Double reportClaimAmount;
    private final ReportStatus reportStatus;
    private final Integer actionLevel;
    private final Boolean containsDeviation;
    private final String deviationRemarks;
    private final Double reportSgstAmount;
    private final Double reportCgstAmount;
    private final Double reportIgstAmount;
    private final Double reportTaxableAmount;
    private final String employeeFirstName;
    private final String employeeMiddleName;
    private final String employeeLastName;
    private final String employeeEmail;
    private final String employeeCode;
    private final String employeeGrade;
    private final Long employeeSystemId;
    private final String gender;
    private final Long expenseMetadataId;
    private final String expenseGroup;
    private final String expenseType;
    private final String sendBackRemarks;
    private final String rejectRemarks;
    private final String delegationRemarks;
    private final String defaultApproverRemarks;
    private final Boolean containsSentBack;
    private final LocalDate glPostingDate;
    private final LocalDate paymentDate;

    @QueryProjection
    public QueueRowDTO(
            String approverEmployeeCode,
            String approverFirstName,
            Integer level,
            String approverLastName,
            Long reportId,
            String documentIdentifier,
            LocalDate createdDate,
            LocalDate submitDate,
            LocalDate startDate,
            LocalDate endDate,
            String reportTitle,
            String description,
            String purpose,
            Double reportClaimAmount,
            ReportStatus reportStatus,
            Integer actionLevel,
            Boolean containsDeviation,
            String deviationRemarks,
            Double reportSgstAmount,
            Double reportCgstAmount,
            Double reportIgstAmount,
            Double reportTaxableAmount,
            String employeeFirstName,
            String employeeMiddleName,
            String employeeLastName,
            String employeeEmail,
            String employeeCode,
            String employeeGrade,
            Long employeeSystemId,
            String gender,
            Long expenseMetadataId,
            String expenseGroup,
            String expenseType,
            String sendBackRemarks,
            String rejectRemarks,
            String delegationRemarks,
            String defaultApproverRemarks,
            Boolean containsSentBack,
            LocalDate glPostingDate,
            LocalDate paymentDate
    ) {
        this.approverEmployeeCode = approverEmployeeCode;
        this.approverFirstName   = approverFirstName;
        this.level               = level;
        this.approverLastName    = approverLastName;
        this.reportId            = reportId;
        this.documentIdentifier  = documentIdentifier;
        this.createdDate         = createdDate;
        this.submitDate          = submitDate;
        this.startDate           = startDate;
        this.endDate             = endDate;
        this.reportTitle         = reportTitle;
        this.description         = description;
        this.purpose             = purpose;
        this.reportClaimAmount   = reportClaimAmount;
        this.reportStatus        = reportStatus;
        this.actionLevel         = actionLevel;
        this.containsDeviation   = containsDeviation;
        this.deviationRemarks    = deviationRemarks;
        this.reportSgstAmount    = reportSgstAmount;
        this.reportCgstAmount    = reportCgstAmount;
        this.reportIgstAmount    = reportIgstAmount;
        this.reportTaxableAmount = reportTaxableAmount;
        this.employeeFirstName   = employeeFirstName;
        this.employeeMiddleName  = employeeMiddleName;
        this.employeeLastName    = employeeLastName;
        this.employeeEmail       = employeeEmail;
        this.employeeCode        = employeeCode;
        this.employeeGrade       = employeeGrade;
        this.employeeSystemId    = employeeSystemId;
        this.gender              = gender;
        this.expenseMetadataId   = expenseMetadataId;
        this.expenseGroup        = expenseGroup;
        this.expenseType         = expenseType;
        this.sendBackRemarks     = sendBackRemarks;
        this.rejectRemarks       = rejectRemarks;
        this.delegationRemarks   = delegationRemarks;
        this.defaultApproverRemarks = defaultApproverRemarks;
        this.containsSentBack      = containsSentBack;
        this.glPostingDate         = glPostingDate;
        this.paymentDate           = paymentDate;
    }

    // getters only (no setters, since fields are final)
    public String getApproverEmployeeCode() { return approverEmployeeCode; }
    public String getApproverFirstName()   { return approverFirstName; }
    public Integer getLevel()              { return level; }
    public String getApproverLastName()    { return approverLastName; }
    public Long getReportId()              { return reportId; }
    public String getDocumentIdentifier()  { return documentIdentifier; }
    public LocalDate getCreatedDate()      { return createdDate; }
    public LocalDate getSubmitDate()       { return submitDate; }
    public LocalDate getStartDate()        { return startDate; }
    public LocalDate getEndDate()          { return endDate; }
    public String getReportTitle()         { return reportTitle; }
    public String getDescription()         { return description; }
    public String getPurpose()             { return purpose; }
    public Double getReportClaimAmount()   { return reportClaimAmount; }
    public ReportStatus getReportStatus()       { return reportStatus; }
    public Integer getActionLevel()        { return actionLevel; }
    public Boolean getContainsDeviation()  { return containsDeviation; }
    public String getDeviationRemarks()    { return deviationRemarks; }
    public Double getReportSgstAmount()    { return reportSgstAmount; }
    public Double getReportCgstAmount()    { return reportCgstAmount; }
    public Double getReportIgstAmount()    { return reportIgstAmount; }
    public Double getReportTaxableAmount() { return reportTaxableAmount; }
    public String getEmployeeFirstName()   { return employeeFirstName; }
    public String getEmployeeMiddleName()  { return employeeMiddleName; }
    public String getEmployeeLastName()    { return employeeLastName; }
    public String getEmployeeEmail()       { return employeeEmail; }
    public String getEmployeeCode()        { return employeeCode; }
    public String getEmployeeGrade()       { return employeeGrade; }
    public Long getEmployeeSystemId()      { return employeeSystemId; }
    public String getGender()              { return gender; }
    public Long getExpenseMetadataId()     { return expenseMetadataId; }
    public String getExpenseGroup()        { return expenseGroup; }
    public String getExpenseType()         { return expenseType; }
    public String getSendBackRemarks()     { return sendBackRemarks; }
    public String getRejectRemarks()       { return rejectRemarks; }
    public String getDelegationRemarks()   { return delegationRemarks; }
    public String getDefaultApproverRemarks() { return defaultApproverRemarks; }
    public Boolean getContainsSentBack()   { return containsSentBack; }
    public LocalDate getGlPostingDate()    { return glPostingDate; }
    public LocalDate getPaymentDate()      { return paymentDate; }
}