package in.taxgenie.pay_expense_pvv.viewmodels;

import javax.validation.constraints.Positive;

public class PaginationViewModel {

	private Integer total = 0;

	@Positive
	private Integer pages = 0;

	public Integer getPages() {
		return pages;
	}

	public void setPages(Integer pages) {
		this.pages = pages;
	}

	public Integer getTotal() {
		return total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

}
