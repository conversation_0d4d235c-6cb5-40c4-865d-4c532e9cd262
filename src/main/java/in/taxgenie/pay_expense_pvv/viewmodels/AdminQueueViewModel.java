package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.*;

import java.time.LocalDate;
import java.time.ZonedDateTime;

public interface AdminQueueViewModel {
    long getId();
    long getCompanyCode();
    String getDocumentIdentifier();
    LocalDate getCreatedDate();
    LocalDate getSubmitDate();
    LocalDate getStartDate();
    LocalDate getEndDate();
    String getReportTitle();
    String getDescription();
    String getPurpose();
    double getReportClaimAmount();
    ReportStatus getReportStatus();
    int getActionLevel();
    String getDeviationRemarks();
    boolean isContainsDeviation();
    boolean isContainsSentBack();
    String getExpenseType();
    double getReportSgstAmount();
    double getReportCgstAmount();
    double getReportIgstAmount();
    double getReportTaxableAmount();
    boolean isAcknowledgedByErp();
    ZonedDateTime getErpAcknowledgementTimestamp();
    boolean isPostedToGl();
    LocalDate getGlPostingDate();
    String getGlDocumentReference();
    PaidStatus getPaidStatus();
    double getTotalPaidAmount();
    double getTotalTdsAmount();
    String getCurrentApproverFirstName();
    String getCurrentApproverLastName();
    String getCurrentApproverEmployeeCode();
    String getFirstName();
    String getMiddleName();
    String getLastName();
    String getEmployeeEmail();
    String getEmployeeCode();
    Long getEmployeeSystemId();
    String getGender();
    LocalDate getPaymentDate();
    String getEmployeeHrmsCode();
    String getEmployeeType();
    String getEmployeeGrade();
    String getEmployeeBranch();
    String getEmployeeDepartment();
    String getEmployeeCostCenter();
    String getEmployeeGlMainAccountCode();
    String getEmployeeGlSubAccountCode();
    String getEmployeeProfitCenter();
    String getDimension01();
    String getDimension02();
    String getDimension03();
    String getDimension04();
    String getDimension05();
    String getDimension06();
    String getDimension07();
    String getDimension08();
    String getDimension09();
    String getDimension10();
    String getKey01();
    String getKey02();
    String getKey03();
    String getKey04();
    String getKey05();
    String getKey06();
    String getKey07();
    String getKey08();
    String getKey09();
    String getKey10();
    String getValue01();
    String getValue02();
    String getValue03();
    String getValue04();
    String getValue05();
    String getValue06();
    String getValue07();
    String getValue08();
    String getValue09();
    String getValue10();
    String getCurrentApproverSystemIdCode();
    String getSendBackRemarks();
    String getRejectRemarks();
    String getDelegationRemarks();
    String getDefaultApproverRemarks();
    String getExpenseGroup();

    long getCreatingUserId();

    Long getUpdatingUserId();

    long getExpenseMetadataId();
    ExpenseActionStatus getStatus();
    int getLevel();
}
