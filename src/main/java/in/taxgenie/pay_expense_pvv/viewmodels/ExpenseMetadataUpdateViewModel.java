package in.taxgenie.pay_expense_pvv.viewmodels;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

public class ExpenseMetadataUpdateViewModel {
    @Positive
    private long id;
    @Size(max = 100)
    private String description;
    @NotBlank
    @Size(max = 100)
    private String expenseType;
    @NotBlank
    @Size(max = 3)
    private String expenseTypePrefix;
    @NotBlank
    @Size(max = 100)
    private String expenseGroup;
    @NotBlank
    @Size(max = 3)
    private String expenseGroupPrefix;
    private boolean isFrozen;
    private boolean purposeRequired;

    private String applicableGender;
    private Integer limitDays;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getExpenseTypePrefix() {
        return expenseTypePrefix;
    }

    public void setExpenseTypePrefix(String expenseTypePrefix) {
        this.expenseTypePrefix = expenseTypePrefix;
    }

    public String getExpenseGroup() {
        return expenseGroup;
    }

    public void setExpenseGroup(String expenseGroup) {
        this.expenseGroup = expenseGroup;
    }

    public String getExpenseGroupPrefix() {
        return expenseGroupPrefix;
    }

    public void setExpenseGroupPrefix(String expenseGroupPrefix) {
        this.expenseGroupPrefix = expenseGroupPrefix;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public boolean isPurposeRequired() {
        return purposeRequired;
    }

    public void setPurposeRequired(boolean purposeRequired) {
        this.purposeRequired = purposeRequired;
    }

    public String getApplicableGender() {
        return applicableGender;
    }

    public void setApplicableGender(String applicableGender) {
        this.applicableGender = applicableGender;
    }

    public Integer getLimitDays() {
        return limitDays;
    }

    public void setLimitDays(Integer limitDays) {
        this.limitDays = limitDays;
    }
}
