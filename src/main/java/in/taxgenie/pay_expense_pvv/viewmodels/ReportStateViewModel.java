package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.StateChannel;

import java.time.LocalDate;
import java.time.ZonedDateTime;

public class ReportStateViewModel {
    private long id;
    private int level;
    private String approver;
    private LocalDate actionDate;
    private String remarks;
    private ExpenseActionStatus status;
    private ZonedDateTime delegationTimestamp;
    private StateChannel channel;
    private boolean isDelegated;
    private boolean triggeredByDefault;
    private String defaultTriggerRemarks;
    private String delegationRemarks;
    private long expenseReportId;
    private boolean isDeviationAssignment;
    private String deviationRemarks;

    private String approverFirstName;
    private String approverLastName;
    private String approverEmployeeCode;

    private boolean isChangeDesk;
    private String changeDeskRemarks;
    private ZonedDateTime changeDeskTimestamp;
    private ZonedDateTime createdTimestamp;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public LocalDate getActionDate() {
        return actionDate;
    }

    public void setActionDate(LocalDate actionDate) {
        this.actionDate = actionDate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public ExpenseActionStatus getStatus() {
        return status;
    }

    public void setStatus(ExpenseActionStatus status) {
        this.status = status;
    }

    public ZonedDateTime getDelegationTimestamp() {
        return delegationTimestamp;
    }

    public void setDelegationTimestamp(ZonedDateTime delegationTimestamp) {
        this.delegationTimestamp = delegationTimestamp;
    }

    public StateChannel getChannel() {
        return channel;
    }

    public void setChannel(StateChannel channel) {
        this.channel = channel;
    }

    public boolean isDelegated() {
        return isDelegated;
    }

    public void setDelegated(boolean delegated) {
        isDelegated = delegated;
    }

    public boolean isTriggeredByDefault() {
        return triggeredByDefault;
    }

    public void setTriggeredByDefault(boolean triggeredByDefault) {
        this.triggeredByDefault = triggeredByDefault;
    }

    public String getDefaultTriggerRemarks() {
        return defaultTriggerRemarks;
    }

    public void setDefaultTriggerRemarks(String defaultTriggerRemarks) {
        this.defaultTriggerRemarks = defaultTriggerRemarks;
    }

    public String getDelegationRemarks() {
        return delegationRemarks;
    }

    public void setDelegationRemarks(String delegationRemarks) {
        this.delegationRemarks = delegationRemarks;
    }

    public long getExpenseReportId() {
        return expenseReportId;
    }

    public void setExpenseReportId(long expenseReportId) {
        this.expenseReportId = expenseReportId;
    }

    public boolean isDeviationAssignment() {
        return isDeviationAssignment;
    }

    public void setDeviationAssignment(boolean deviationAssignment) {
        isDeviationAssignment = deviationAssignment;
    }

    public String getDeviationRemarks() {
        return deviationRemarks;
    }

    public void setDeviationRemarks(String deviationRemarks) {
        this.deviationRemarks = deviationRemarks;
    }

    public String getApproverFirstName() {
        return approverFirstName;
    }

    public void setApproverFirstName(String approverFirstName) {
        this.approverFirstName = approverFirstName;
    }

    public String getApproverLastName() {
        return approverLastName;
    }

    public void setApproverLastName(String approverLastName) {
        this.approverLastName = approverLastName;
    }

    public String getApproverEmployeeCode() {
        return approverEmployeeCode;
    }

    public void setApproverEmployeeCode(String approverEmployeeCode) {
        this.approverEmployeeCode = approverEmployeeCode;
    }

    public boolean isChangeDesk() {
        return isChangeDesk;
    }

    public void setChangeDesk(boolean isChangeDesk) {
        this.isChangeDesk = isChangeDesk;
    }

    public String getChangeDeskRemarks() {
        return changeDeskRemarks;
    }

    public void setChangeDeskRemarks(String changeDeskRemarks) {
        this.changeDeskRemarks = changeDeskRemarks;
    }

    public ZonedDateTime getChangeDeskTimestamp() {
        return changeDeskTimestamp;
    }

    public void setChangeDeskTimestamp(ZonedDateTime changeDeskTimestamp) {
        this.changeDeskTimestamp = changeDeskTimestamp;
    }

    public ZonedDateTime getCreatedTimestamp() {
        return createdTimestamp;
    }

    public void setCreatedTimestamp(ZonedDateTime createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }
}
