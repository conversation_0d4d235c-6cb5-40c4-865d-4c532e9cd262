package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.StateChannel;

public class ApprovalDefinitionViewModel {
    private long id;
    private int level;
    private double limitAmount;
    private String approvalMatcher;
    private String approvalTitle;
    private boolean shouldFetchFromEmployeeMaster;
    private boolean isFrozen;
    private long expenseMetadataId;
    private String expenseMetadataMarker;
    private StateChannel channel;
    private boolean isExternalToCem;
    private boolean forDeviation;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public String getApprovalMatcher() {
        return approvalMatcher;
    }

    public void setApprovalMatcher(String approvalMatcher) {
        this.approvalMatcher = approvalMatcher;
    }

    public String getApprovalTitle() {
        return approvalTitle;
    }

    public void setApprovalTitle(String approvalTitle) {
        this.approvalTitle = approvalTitle;
    }

    public boolean isShouldFetchFromEmployeeMaster() {
        return shouldFetchFromEmployeeMaster;
    }

    public void setShouldFetchFromEmployeeMaster(boolean shouldFetchFromEmployeeMaster) {
        this.shouldFetchFromEmployeeMaster = shouldFetchFromEmployeeMaster;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public String getExpenseMetadataMarker() {
        return expenseMetadataMarker;
    }

    public void setExpenseMetadataMarker(String expenseMetadataMarker) {
        this.expenseMetadataMarker = expenseMetadataMarker;
    }

    public StateChannel getChannel() {
        return channel;
    }

    public void setChannel(StateChannel channel) {
        this.channel = channel;
    }

    public boolean isExternalToCem() {
        return isExternalToCem;
    }

    public void setExternalToCem(boolean externalToCem) {
        isExternalToCem = externalToCem;
    }

    public boolean isForDeviation() {
        return forDeviation;
    }

    public void setForDeviation(boolean forDeviation) {
        this.forDeviation = forDeviation;
    }
}
