package in.taxgenie.pay_expense_pvv.viewmodels;

import java.time.LocalDate;

public class ErpPaymentLineViewModel {
    private long reportId;
    private long companyCode;
    private LocalDate paymentDate;
    private boolean isFullPayment;
    private String paymentReference;
    private double paidAmount;
    private double tdsAmount;
    private String remarks;
    private long erpCompCode;

    public long getReportId() {
        return reportId;
    }

    public void setReportId(long reportId) {
        this.reportId = reportId;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public boolean isFullPayment() {
        return isFullPayment;
    }

    public void setFullPayment(boolean fullPayment) {
        isFullPayment = fullPayment;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public double getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(double paidAmount) {
        this.paidAmount = paidAmount;
    }

    public double getTdsAmount() {
        return tdsAmount;
    }

    public void setTdsAmount(double tdsAmount) {
        this.tdsAmount = tdsAmount;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public long getErpCompCode() {
        return erpCompCode;
    }

    public void setErpCompCode(long erpCompCode) {
        this.erpCompCode = erpCompCode;
    }
}
