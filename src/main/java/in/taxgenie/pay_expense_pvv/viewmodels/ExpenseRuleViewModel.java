package in.taxgenie.pay_expense_pvv.viewmodels;

import java.time.LocalDate;

public class ExpenseRuleViewModel {
    private long id;
    private String branchCode;
    private String departmentCode;
    private String locationCategory;
    private String employeeType;
    private String employeeGrade;
    private String costCenterCode;

    private boolean isInvoiceRequired;
    private Double invoiceRequiredThreshold;

    private LocalDate startDate;
    private LocalDate endDate;

    //  regular specific
    private Double standardDeductionRate;

    //  Other
    private Double limitAmount;
    private Double maximumAmount;
    private boolean canExceedLimit;

    private boolean isPerDiemAllowed;
    private Double perDiemAmount;

    //  Unit rate specific
    private boolean isUnitRateApplicable;
    private String unitOfMeasure;
    private Double unitRate;
    private String unitRateType;

    //  Common
    private boolean isFrozen;
    private long expenseSubgroupId;
    private String expenseMetadataMarker;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(String locationCategory) {
        this.locationCategory = locationCategory;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public boolean isInvoiceRequired() {
        return isInvoiceRequired;
    }

    public void setInvoiceRequired(boolean invoiceRequired) {
        isInvoiceRequired = invoiceRequired;
    }

    public Double getInvoiceRequiredThreshold() {
        return invoiceRequiredThreshold;
    }

    public void setInvoiceRequiredThreshold(Double invoiceRequiredThreshold) {
        this.invoiceRequiredThreshold = invoiceRequiredThreshold;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Double getStandardDeductionRate() {
        return standardDeductionRate;
    }

    public void setStandardDeductionRate(Double standardDeductionRate) {
        this.standardDeductionRate = standardDeductionRate;
    }

    public Double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(Double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public Double getMaximumAmount() {
        return maximumAmount;
    }

    public void setMaximumAmount(Double maximumAmount) {
        this.maximumAmount = maximumAmount;
    }

    public boolean isCanExceedLimit() {
        return canExceedLimit;
    }

    public void setCanExceedLimit(boolean canExceedLimit) {
        this.canExceedLimit = canExceedLimit;
    }

    public boolean isPerDiemAllowed() {
        return isPerDiemAllowed;
    }

    public void setPerDiemAllowed(boolean perDiemAllowed) {
        isPerDiemAllowed = perDiemAllowed;
    }

    public Double getPerDiemAmount() {
        return perDiemAmount;
    }

    public void setPerDiemAmount(Double perDiemAmount) {
        this.perDiemAmount = perDiemAmount;
    }

    public boolean isUnitRateApplicable() {
        return isUnitRateApplicable;
    }

    public void setUnitRateApplicable(boolean unitRateApplicable) {
        isUnitRateApplicable = unitRateApplicable;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public Double getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(Double unitRate) {
        this.unitRate = unitRate;
    }

    public String getUnitRateType() {
        return unitRateType;
    }

    public void setUnitRateType(String unitRateType) {
        this.unitRateType = unitRateType;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public long getExpenseSubgroupId() {
        return expenseSubgroupId;
    }

    public void setExpenseSubgroupId(long expenseSubgroupId) {
        this.expenseSubgroupId = expenseSubgroupId;
    }

    public String getExpenseMetadataMarker() {
        return expenseMetadataMarker;
    }

    public void setExpenseMetadataMarker(String expenseMetadataMarker) {
        this.expenseMetadataMarker = expenseMetadataMarker;
    }
}
