package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.StateChannel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

public class ApproverUpdateViewModel {
    @Positive
    private long id;

    @NotBlank
    @Size(max = 50)
    private String approvalMatcher;
    @NotBlank
    @Size(max = 50)
    private String approvalMatchValue;
    @NotBlank
    @Size(max = 50)
    private String approvalTitle;
    @NotBlank
    @Size(max = 200)
    private String approver;
    @NotNull
    private StateChannel channel;

    private boolean isFrozen;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getApprovalMatcher() {
        return approvalMatcher;
    }

    public void setApprovalMatcher(String approvalMatcher) {
        this.approvalMatcher = approvalMatcher;
    }

    public String getApprovalMatchValue() {
        return approvalMatchValue;
    }

    public void setApprovalMatchValue(String approvalMatchValue) {
        this.approvalMatchValue = approvalMatchValue;
    }

    public String getApprovalTitle() {
        return approvalTitle;
    }

    public void setApprovalTitle(String approvalTitle) {
        this.approvalTitle = approvalTitle;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public StateChannel getChannel() {
        return channel;
    }

    public void setChannel(StateChannel channel) {
        this.channel = channel;
    }
}
