package in.taxgenie.pay_expense_pvv.viewmodels.pdfHandling;

public class SanitizationResult {
    private final byte[] bytes;
    private final boolean hadEmbeddedScripts;

    public SanitizationResult(byte[] bytes, boolean hadEmbeddedScripts) {
        this.bytes = bytes;
        this.hadEmbeddedScripts = hadEmbeddedScripts;
    }

    public byte[] getBytes() { return bytes; }
    public boolean hadEmbeddedScripts() { return hadEmbeddedScripts; }
}