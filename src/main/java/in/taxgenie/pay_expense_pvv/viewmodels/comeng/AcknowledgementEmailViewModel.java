package in.taxgenie.pay_expense_pvv.viewmodels.comeng;

public class AcknowledgementEmailViewModel {
    private String creator;
    private String documentNumber;
    private String lastApprover;
    private String nextApprover;
    private String metadataMarker;
    private String startDate;
    private String endDate;
    private String subgroup;
    private double totalClaimAmount;

    private String lineDate1;
    private String lineSubgroup1;
    private String lineDescription1;
    private String lineClaimAmount1;

    private String lineDate2;
    private String lineSubgroup2;
    private String lineDescription2;
    private String lineClaimAmount2;

    private String lineDate3;
    private String lineSubgroup3;
    private String lineDescription3;
    private String lineClaimAmount3;

    private String lineDate4;
    private String lineSubgroup4;
    private String lineDescription4;
    private String lineClaimAmount4;

    private String lineDate5;
    private String lineSubgroup5;
    private String lineDescription5;
    private String lineClaimAmount5;

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getDocumentNumber() {
        return documentNumber;
    }

    public void setDocumentNumber(String documentNumber) {
        this.documentNumber = documentNumber;
    }

    public String getLastApprover() {
        return lastApprover;
    }

    public void setLastApprover(String lastApprover) {
        this.lastApprover = lastApprover;
    }

    public String getNextApprover() {
        return nextApprover;
    }

    public void setNextApprover(String nextApprover) {
        this.nextApprover = nextApprover;
    }

    public String getMetadataMarker() {
        return metadataMarker;
    }

    public void setMetadataMarker(String metadataMarker) {
        this.metadataMarker = metadataMarker;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getSubgroup() {
        return subgroup;
    }

    public void setSubgroup(String subgroup) {
        this.subgroup = subgroup;
    }

    public double getTotalClaimAmount() {
        return totalClaimAmount;
    }

    public void setTotalClaimAmount(double totalClaimAmount) {
        this.totalClaimAmount = totalClaimAmount;
    }

    public String getLineDate1() {
        return lineDate1;
    }

    public void setLineDate1(String lineDate1) {
        this.lineDate1 = lineDate1;
    }

    public String getLineSubgroup1() {
        return lineSubgroup1;
    }

    public void setLineSubgroup1(String lineSubgroup1) {
        this.lineSubgroup1 = lineSubgroup1;
    }

    public String getLineDescription1() {
        return lineDescription1;
    }

    public void setLineDescription1(String lineDescription1) {
        this.lineDescription1 = lineDescription1;
    }

    public String getLineClaimAmount1() {
        return lineClaimAmount1;
    }

    public void setLineClaimAmount1(String lineClaimAmount1) {
        this.lineClaimAmount1 = lineClaimAmount1;
    }

    public String getLineDate2() {
        return lineDate2;
    }

    public void setLineDate2(String lineDate2) {
        this.lineDate2 = lineDate2;
    }

    public String getLineSubgroup2() {
        return lineSubgroup2;
    }

    public void setLineSubgroup2(String lineSubgroup2) {
        this.lineSubgroup2 = lineSubgroup2;
    }

    public String getLineDescription2() {
        return lineDescription2;
    }

    public void setLineDescription2(String lineDescription2) {
        this.lineDescription2 = lineDescription2;
    }

    public String getLineClaimAmount2() {
        return lineClaimAmount2;
    }

    public void setLineClaimAmount2(String lineClaimAmount2) {
        this.lineClaimAmount2 = lineClaimAmount2;
    }

    public String getLineDate3() {
        return lineDate3;
    }

    public void setLineDate3(String lineDate3) {
        this.lineDate3 = lineDate3;
    }

    public String getLineSubgroup3() {
        return lineSubgroup3;
    }

    public void setLineSubgroup3(String lineSubgroup3) {
        this.lineSubgroup3 = lineSubgroup3;
    }

    public String getLineDescription3() {
        return lineDescription3;
    }

    public void setLineDescription3(String lineDescription3) {
        this.lineDescription3 = lineDescription3;
    }

    public String getLineClaimAmount3() {
        return lineClaimAmount3;
    }

    public void setLineClaimAmount3(String lineClaimAmount3) {
        this.lineClaimAmount3 = lineClaimAmount3;
    }

    public String getLineDate4() {
        return lineDate4;
    }

    public void setLineDate4(String lineDate4) {
        this.lineDate4 = lineDate4;
    }

    public String getLineSubgroup4() {
        return lineSubgroup4;
    }

    public void setLineSubgroup4(String lineSubgroup4) {
        this.lineSubgroup4 = lineSubgroup4;
    }

    public String getLineDescription4() {
        return lineDescription4;
    }

    public void setLineDescription4(String lineDescription4) {
        this.lineDescription4 = lineDescription4;
    }

    public String getLineClaimAmount4() {
        return lineClaimAmount4;
    }

    public void setLineClaimAmount4(String lineClaimAmount4) {
        this.lineClaimAmount4 = lineClaimAmount4;
    }

    public String getLineDate5() {
        return lineDate5;
    }

    public void setLineDate5(String lineDate5) {
        this.lineDate5 = lineDate5;
    }

    public String getLineSubgroup5() {
        return lineSubgroup5;
    }

    public void setLineSubgroup5(String lineSubgroup5) {
        this.lineSubgroup5 = lineSubgroup5;
    }

    public String getLineDescription5() {
        return lineDescription5;
    }

    public void setLineDescription5(String lineDescription5) {
        this.lineDescription5 = lineDescription5;
    }

    public String getLineClaimAmount5() {
        return lineClaimAmount5;
    }

    public void setLineClaimAmount5(String lineClaimAmount5) {
        this.lineClaimAmount5 = lineClaimAmount5;
    }
}
