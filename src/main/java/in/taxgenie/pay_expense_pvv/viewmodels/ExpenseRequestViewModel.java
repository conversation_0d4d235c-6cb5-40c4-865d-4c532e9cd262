package in.taxgenie.pay_expense_pvv.viewmodels;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

public class ExpenseRequestViewModel {
    @NotBlank
    @Size(max = 50)
    private String expenseType;

    @NotBlank
    @Size(max = 50)
    private String expenseGroup;

    @NotBlank
    @Size(max = 50)
    private String expenseSubgroup;

    @Size(max = 50)
    private String frequency;

    private Long locationId;

    @Size(max = 50)
    private String title;

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getExpenseGroup() {
        return expenseGroup;
    }

    public void setExpenseGroup(String expenseGroup) {
        this.expenseGroup = expenseGroup;
    }

    public String getExpenseSubgroup() {
        return expenseSubgroup;
    }

    public void setExpenseSubgroup(String expenseSubgroup) {
        this.expenseSubgroup = expenseSubgroup;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }
}
