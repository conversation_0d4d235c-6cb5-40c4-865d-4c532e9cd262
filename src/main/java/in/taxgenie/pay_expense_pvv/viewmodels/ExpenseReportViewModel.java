package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;

import java.time.LocalDate;

public class ExpenseReportViewModel {
    private long id;

    private String documentIdentifier;

    private LocalDate createdDate;
    private LocalDate submitDate;
    private LocalDate startDate;
    private LocalDate endDate;

    private String reportTitle;
    private String description;
    private String purpose;
    private int level;

    private double reportClaimAmount;
    private ReportStatus reportStatus;
    private int actionLevel;
    private boolean containsDeviation;
    private String deviationRemarks;

    //  GST computation
    private Double reportSgstAmount;
    private Double reportCgstAmount;
    private Double reportIgstAmount;
    private Double reportTaxableAmount;

    //  Employee master data
    private String firstName;
    private String middleName;
    private String lastName;
    private String employeeEmail;
    private String employeeCode;

    private String employeeGrade;
    private Long employeeSystemId;
    private String gender;

    private long expenseMetadataId;
    private String expenseType;
    private String expenseGroup;
    private String sendBackRemarks;
    private String rejectRemarks;
    private String delegationRemarks;
    private String defaultApproverRemarks;
    private String currentApproverFirstName;
    private String currentApproverLastName;
    private String currentApproverSystemIdCode;
    private String currentApproverEmployeeCode;
    private boolean containsSentBack;

    //  gl related
    private LocalDate glPostingDate;
    private LocalDate paymentDate;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDate getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDate submitDate) {
        this.submitDate = submitDate;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public double getReportClaimAmount() {
        return reportClaimAmount;
    }

    public void setReportClaimAmount(double reportClaimAmount) {
        this.reportClaimAmount = reportClaimAmount;
    }

    public Double getReportSgstAmount() {
        return reportSgstAmount;
    }

    public void setReportSgstAmount(Double reportSgstAmount) {
        this.reportSgstAmount = reportSgstAmount;
    }

    public Double getReportCgstAmount() {
        return reportCgstAmount;
    }

    public void setReportCgstAmount(Double reportCgstAmount) {
        this.reportCgstAmount = reportCgstAmount;
    }

    public Double getReportIgstAmount() {
        return reportIgstAmount;
    }

    public void setReportIgstAmount(Double reportIgstAmount) {
        this.reportIgstAmount = reportIgstAmount;
    }

    public Double getReportTaxableAmount() {
        return reportTaxableAmount;
    }

    public void setReportTaxableAmount(Double reportTaxableAmount) {
        this.reportTaxableAmount = reportTaxableAmount;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmployeeEmail() {
        return employeeEmail;
    }

    public void setEmployeeEmail(String employeeEmail) {
        this.employeeEmail = employeeEmail;
    }

    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getExpenseGroup() {
        return expenseGroup;
    }

    public void setExpenseGroup(String expenseGroup) {
        this.expenseGroup = expenseGroup;
    }

    public String getSendBackRemarks() {
        return sendBackRemarks;
    }

    public void setSendBackRemarks(String sendBackRemarks) {
        this.sendBackRemarks = sendBackRemarks;
    }

    public String getRejectRemarks() {
        return rejectRemarks;
    }

    public void setRejectRemarks(String rejectRemarks) {
        this.rejectRemarks = rejectRemarks;
    }

    public String getDelegationRemarks() {
        return delegationRemarks;
    }

    public void setDelegationRemarks(String delegationRemarks) {
        this.delegationRemarks = delegationRemarks;
    }

    public ReportStatus getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(ReportStatus reportStatus) {
        this.reportStatus = reportStatus;
    }

    public int getActionLevel() {
        return actionLevel;
    }

    public void setActionLevel(int actionLevel) {
        this.actionLevel = actionLevel;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public Long getEmployeeSystemId() {
        return employeeSystemId;
    }

    public void setEmployeeSystemId(Long employeeSystemId) {
        this.employeeSystemId = employeeSystemId;
    }

    public String getDefaultApproverRemarks() {
        return defaultApproverRemarks;
    }

    public void setDefaultApproverRemarks(String defaultApproverRemarks) {
        this.defaultApproverRemarks = defaultApproverRemarks;
    }

    public String getDeviationRemarks() {
        return deviationRemarks;
    }

    public void setDeviationRemarks(String deviationRemarks) {
        this.deviationRemarks = deviationRemarks;
    }

    public boolean isContainsDeviation() {
        return containsDeviation;
    }

    public void setContainsDeviation(boolean containsDeviation) {
        this.containsDeviation = containsDeviation;
    }

    public String getCurrentApproverFirstName() {
        return currentApproverFirstName;
    }

    public void setCurrentApproverFirstName(String currentApproverFirstName) {
        this.currentApproverFirstName = currentApproverFirstName;
    }

    public String getCurrentApproverLastName() {
        return currentApproverLastName;
    }

    public void setCurrentApproverLastName(String currentApproverLastName) {
        this.currentApproverLastName = currentApproverLastName;
    }

    public String getCurrentApproverSystemIdCode() {
        return currentApproverSystemIdCode;
    }

    public void setCurrentApproverSystemIdCode(String currentApproverSystemIdCode) {
        this.currentApproverSystemIdCode = currentApproverSystemIdCode;
    }

    public String getCurrentApproverEmployeeCode() {
        return currentApproverEmployeeCode;
    }

    public void setCurrentApproverEmployeeCode(String currentApproverEmployeeCode) {
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public boolean isContainsSentBack() {
        return containsSentBack;
    }

    public void setContainsSentBack(boolean containsSentBack) {
        this.containsSentBack = containsSentBack;
    }

    public LocalDate getGlPostingDate() {
        return glPostingDate;
    }

    public void setGlPostingDate(LocalDate glPostingDate) {
        this.glPostingDate = glPostingDate;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}
}
