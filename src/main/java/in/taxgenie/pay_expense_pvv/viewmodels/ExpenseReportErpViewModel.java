package in.taxgenie.pay_expense_pvv.viewmodels;

import java.util.ArrayList;
import java.util.List;

public class ExpenseReportErpViewModel {
    private long reportId;
    private long companyCode;
    private String employeeGlMainAccountCode;
    private String employeeGlSubAccountCode;
    private String documentIdentifier;
    private String employeeIdCode;
    private String employeeType;
    private String employeeGrade;
    private String employeeBranch;
    private String employeeCostCenter;
    private String employeeDepartment;
    private String employeeProfitCenter;
    private double reportClaimAmount;
    private double reportSgstAmount = 0;
    private double reportCgstAmount = 0;
    private double reportIgstAmount = 0;
    private double reportTaxableAmount = 0;

    private String value03;
    private String dimension01;
    private String dimension02;
    private String dimension03;
    private String dimension04;
    private String dimension05;
    private String dimension06;
    private String dimension07;
    private String dimension08;
    private String dimension09;
    private String dimension10;

    private List<ExpenseLineErpViewModel> lineItems = new ArrayList<>();

    public long getReportId() {
        return reportId;
    }

    public void setReportId(long reportId) {
        this.reportId = reportId;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public String getEmployeeGlMainAccountCode() {
        return employeeGlMainAccountCode;
    }

    public void setEmployeeGlMainAccountCode(String employeeGlMainAccountCode) {
        this.employeeGlMainAccountCode = employeeGlMainAccountCode;
    }

    public String getEmployeeGlSubAccountCode() {
        return employeeGlSubAccountCode;
    }

    public void setEmployeeGlSubAccountCode(String employeeGlSubAccountCode) {
        this.employeeGlSubAccountCode = employeeGlSubAccountCode;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }
    public String getEmployeeIdCode() { return employeeIdCode; }
    public void setEmployeeIdCode(String employeeIdCode) { this.employeeIdCode = employeeIdCode; }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public String getEmployeeBranch() {
        return employeeBranch;
    }

    public void setEmployeeBranch(String employeeBranch) {
        this.employeeBranch = employeeBranch;
    }

    public String getEmployeeCostCenter() {
        return employeeCostCenter;
    }

    public void setEmployeeCostCenter(String employeeCostCenter) {
        this.employeeCostCenter = employeeCostCenter;
    }

    public String getEmployeeDepartment() {
        return employeeDepartment;
    }

    public void setEmployeeDepartment(String employeeDepartment) {
        this.employeeDepartment = employeeDepartment;
    }

    public String getEmployeeProfitCenter() {
        return employeeProfitCenter;
    }

    public void setEmployeeProfitCenter(String employeeProfitCenter) {
        this.employeeProfitCenter = employeeProfitCenter;
    }

    public double getReportClaimAmount() {
        return reportClaimAmount;
    }

    public void setReportClaimAmount(double reportClaimAmount) {
        this.reportClaimAmount = reportClaimAmount;
    }

    public double getReportSgstAmount() {
        return reportSgstAmount;
    }

    public void setReportSgstAmount(double reportSgstAmount) {
        this.reportSgstAmount = reportSgstAmount;
    }

    public double getReportCgstAmount() {
        return reportCgstAmount;
    }

    public void setReportCgstAmount(double reportCgstAmount) {
        this.reportCgstAmount = reportCgstAmount;
    }

    public double getReportIgstAmount() {
        return reportIgstAmount;
    }

    public void setReportIgstAmount(double reportIgstAmount) {
        this.reportIgstAmount = reportIgstAmount;
    }

    public double getReportTaxableAmount() {
        return reportTaxableAmount;
    }

    public void setReportTaxableAmount(double reportTaxableAmount) {
        this.reportTaxableAmount = reportTaxableAmount;
    }

    public String getValue03() {
        return value03;
    }

    public void setValue03(String value03) {
        this.value03 = value03;
    }

    public String getDimension01() {
        return dimension01;
    }

    public void setDimension01(String dimension01) {
        this.dimension01 = dimension01;
    }

    public String getDimension02() {
        return dimension02;
    }

    public void setDimension02(String dimension02) {
        this.dimension02 = dimension02;
    }

    public String getDimension03() {
        return dimension03;
    }

    public void setDimension03(String dimension03) {
        this.dimension03 = dimension03;
    }

    public String getDimension04() {
        return dimension04;
    }

    public void setDimension04(String dimension04) {
        this.dimension04 = dimension04;
    }

    public String getDimension05() {
        return dimension05;
    }

    public void setDimension05(String dimension05) {
        this.dimension05 = dimension05;
    }

    public String getDimension06() {
        return dimension06;
    }

    public void setDimension06(String dimension06) {
        this.dimension06 = dimension06;
    }

    public String getDimension07() {
        return dimension07;
    }

    public void setDimension07(String dimension07) {
        this.dimension07 = dimension07;
    }

    public String getDimension08() {
        return dimension08;
    }

    public void setDimension08(String dimension08) {
        this.dimension08 = dimension08;
    }

    public String getDimension09() {
        return dimension09;
    }

    public void setDimension09(String dimension09) {
        this.dimension09 = dimension09;
    }

    public String getDimension10() {
        return dimension10;
    }

    public void setDimension10(String dimension10) {
        this.dimension10 = dimension10;
    }

    public List<ExpenseLineErpViewModel> getLineItems() {
        return lineItems;
    }

    public void setLineItems(List<ExpenseLineErpViewModel> lineItems) {
        this.lineItems = lineItems;
    }
}
