package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.StateChannel;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

public class ApprovalDefinitionUpdateViewModel {
    @Positive
    private long id;

    @Positive
    private int level;

    @Positive
    private double limitAmount;

    @Size(max = 50)
    private String approvalMatcher;

    @Size(max = 50)
    private String approvalTitle;

    private boolean shouldFetchFromEmployeeMaster;

    private long expenseMetadataId;

    @NotNull
    private StateChannel channel;

    private boolean isExternalToCem;

    private boolean isFrozen;


    private boolean forDeviation;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public String getApprovalMatcher() {
        return approvalMatcher;
    }

    public void setApprovalMatcher(String approvalMatcher) {
        this.approvalMatcher = approvalMatcher;
    }

    public String getApprovalTitle() {
        return approvalTitle;
    }

    public void setApprovalTitle(String approvalTitle) {
        this.approvalTitle = approvalTitle;
    }

    public boolean isShouldFetchFromEmployeeMaster() {
        return shouldFetchFromEmployeeMaster;
    }

    public void setShouldFetchFromEmployeeMaster(boolean shouldFetchFromEmployeeMaster) {
        this.shouldFetchFromEmployeeMaster = shouldFetchFromEmployeeMaster;
    }

    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public StateChannel getChannel() {
        return channel;
    }

    public void setChannel(StateChannel channel) {
        this.channel = channel;
    }

    public boolean isExternalToCem() {
        return isExternalToCem;
    }

    public void setExternalToCem(boolean externalToCem) {
        isExternalToCem = externalToCem;
    }

    public boolean isForDeviation() {
        return forDeviation;
    }

    public void setForDeviation(boolean forDeviation) {
        this.forDeviation = forDeviation;
    }
}
