package in.taxgenie.pay_expense_pvv.viewmodels;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

public class LocationUpdateViewModel {
    @Positive
    private long id;

    @NotBlank
    @Size(max = 50)
    private String location;

    @NotBlank
    @Size(max = 50)
    private String category;

    @NotBlank
    @Size(min = 2, max = 3)
    private String countryCode;

    @Size(max = 200)
    private String description;

    private boolean isFrozen;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }
}
