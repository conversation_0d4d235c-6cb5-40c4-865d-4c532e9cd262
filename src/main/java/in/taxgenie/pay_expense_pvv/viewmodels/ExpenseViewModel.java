package in.taxgenie.pay_expense_pvv.viewmodels;

import java.time.LocalDate;

public class ExpenseViewModel {
    private long id;
    private LocalDate createdDate;
    private LocalDate expenseDate;
    private String invoiceNumber;
    private String merchantDetails;
    private String description;
    private Double quantity;
    private String unitOfMeasure;
    private Double unitRate;
    private String sourceLocation;
    private String destinationLocation;
    private String expenseIdentifier;
    private String frequency;
    private String locationCategory;
    private String location;
    private Double standardDeductionRate;
    private Double standardDeductionAmount;
    private double invoiceAmount;
    private double applicableAmount;
    private boolean isApplicableAmountLesser;
    private Double limitAmount;
    private Double claimAmount;
    private boolean isDeviated;
    private String deviationRemarks;

    private String document1UploadUrl;
    private String document1UploadContentType;
    private String document1UploadUuid;
    private String document1UploadMarker;

    private String document2UploadUrl;
    private String document2UploadContentType;
    private String document2UploadUuid;
    private String document2UploadMarker;

    private String document3UploadUrl;
    private String document3UploadContentType;
    private String document3UploadUuid;
    private String document3UploadMarker;

    //  Management
    private Long expenseRuleId;

    //  GST related
    private String gstin;
    private Double sgstRate;
    private Double cgstRate;
    private Double igstRate;
    private Double sgstAmount;
    private Double cgstAmount;
    private Double igstAmount;
    private Double taxableAmount;

    private long expenseReportId;
    private long expenseSubgroupId;
    private String expenseSubgroupMarker;

    //  Descriptors
    private String transportDescriptor;
    private String mobilityDescriptor;
    private String travelDescriptor;

    private LocalDate startDate;
    private LocalDate endDate;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDate getExpenseDate() {
        return expenseDate;
    }

    public void setExpenseDate(LocalDate expenseDate) {
        this.expenseDate = expenseDate;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getMerchantDetails() {
        return merchantDetails;
    }

    public void setMerchantDetails(String merchantDetails) {
        this.merchantDetails = merchantDetails;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public Double getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(Double unitRate) {
        this.unitRate = unitRate;
    }

    public String getSourceLocation() {
        return sourceLocation;
    }

    public void setSourceLocation(String sourceLocation) {
        this.sourceLocation = sourceLocation;
    }

    public String getDestinationLocation() {
        return destinationLocation;
    }

    public void setDestinationLocation(String destinationLocation) {
        this.destinationLocation = destinationLocation;
    }

    public String getExpenseIdentifier() {
        return expenseIdentifier;
    }

    public void setExpenseIdentifier(String expenseIdentifier) {
        this.expenseIdentifier = expenseIdentifier;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(String locationCategory) {
        this.locationCategory = locationCategory;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Double getStandardDeductionRate() {
        return standardDeductionRate;
    }

    public void setStandardDeductionRate(Double standardDeductionRate) {
        this.standardDeductionRate = standardDeductionRate;
    }

    public Double getStandardDeductionAmount() {
        return standardDeductionAmount;
    }

    public void setStandardDeductionAmount(Double standardDeductionAmount) {
        this.standardDeductionAmount = standardDeductionAmount;
    }

    public double getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(double invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public double getApplicableAmount() {
        return applicableAmount;
    }

    public void setApplicableAmount(double applicableAmount) {
        this.applicableAmount = applicableAmount;
    }

    public boolean isApplicableAmountLesser() {
        return isApplicableAmountLesser;
    }

    public void setApplicableAmountLesser(boolean applicableAmountLesser) {
        isApplicableAmountLesser = applicableAmountLesser;
    }

    public Double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(Double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public Double getClaimAmount() {
        return claimAmount;
    }

    public void setClaimAmount(Double claimAmount) {
        this.claimAmount = claimAmount;
    }

    public String getDocument1UploadUrl() {
        return document1UploadUrl;
    }

    public void setDocument1UploadUrl(String document1UploadUrl) {
        this.document1UploadUrl = document1UploadUrl;
    }

    public String getDocument1UploadContentType() {
        return document1UploadContentType;
    }

    public void setDocument1UploadContentType(String document1UploadContentType) {
        this.document1UploadContentType = document1UploadContentType;
    }

    public String getDocument1UploadUuid() {
        return document1UploadUuid;
    }

    public void setDocument1UploadUuid(String document1UploadUuid) {
        this.document1UploadUuid = document1UploadUuid;
    }

    public String getDocument1UploadMarker() {
        return document1UploadMarker;
    }

    public void setDocument1UploadMarker(String document1UploadMarker) {
        this.document1UploadMarker = document1UploadMarker;
    }

    public String getDocument2UploadUrl() {
        return document2UploadUrl;
    }

    public void setDocument2UploadUrl(String document2UploadUrl) {
        this.document2UploadUrl = document2UploadUrl;
    }

    public String getDocument2UploadContentType() {
        return document2UploadContentType;
    }

    public void setDocument2UploadContentType(String document2UploadContentType) {
        this.document2UploadContentType = document2UploadContentType;
    }

    public String getDocument2UploadUuid() {
        return document2UploadUuid;
    }

    public void setDocument2UploadUuid(String document2UploadUuid) {
        this.document2UploadUuid = document2UploadUuid;
    }

    public String getDocument2UploadMarker() {
        return document2UploadMarker;
    }

    public void setDocument2UploadMarker(String document2UploadMarker) {
        this.document2UploadMarker = document2UploadMarker;
    }

    public String getDocument3UploadUrl() {
        return document3UploadUrl;
    }

    public void setDocument3UploadUrl(String document3UploadUrl) {
        this.document3UploadUrl = document3UploadUrl;
    }

    public String getDocument3UploadContentType() {
        return document3UploadContentType;
    }

    public void setDocument3UploadContentType(String document3UploadContentType) {
        this.document3UploadContentType = document3UploadContentType;
    }

    public String getDocument3UploadUuid() {
        return document3UploadUuid;
    }

    public void setDocument3UploadUuid(String document3UploadUuid) {
        this.document3UploadUuid = document3UploadUuid;
    }

    public String getDocument3UploadMarker() {
        return document3UploadMarker;
    }

    public void setDocument3UploadMarker(String document3UploadMarker) {
        this.document3UploadMarker = document3UploadMarker;
    }

    public Long getExpenseRuleId() {
        return expenseRuleId;
    }

    public void setExpenseRuleId(Long expenseRuleId) {
        this.expenseRuleId = expenseRuleId;
    }

    public String getGstin() {
        return gstin;
    }

    public void setGstin(String gstin) {
        this.gstin = gstin;
    }

    public Double getSgstRate() {
        return sgstRate;
    }

    public void setSgstRate(Double sgstRate) {
        this.sgstRate = sgstRate;
    }

    public Double getCgstRate() {
        return cgstRate;
    }

    public void setCgstRate(Double cgstRate) {
        this.cgstRate = cgstRate;
    }

    public Double getIgstRate() {
        return igstRate;
    }

    public void setIgstRate(Double igstRate) {
        this.igstRate = igstRate;
    }

    public Double getSgstAmount() {
        return sgstAmount;
    }

    public void setSgstAmount(Double sgstAmount) {
        this.sgstAmount = sgstAmount;
    }

    public Double getCgstAmount() {
        return cgstAmount;
    }

    public void setCgstAmount(Double cgstAmount) {
        this.cgstAmount = cgstAmount;
    }

    public Double getIgstAmount() {
        return igstAmount;
    }

    public void setIgstAmount(Double igstAmount) {
        this.igstAmount = igstAmount;
    }

    public Double getTaxableAmount() {
        return taxableAmount;
    }

    public void setTaxableAmount(Double taxableAmount) {
        this.taxableAmount = taxableAmount;
    }

    public long getExpenseReportId() {
        return expenseReportId;
    }

    public void setExpenseReportId(long expenseReportId) {
        this.expenseReportId = expenseReportId;
    }

    public long getExpenseSubgroupId() {
        return expenseSubgroupId;
    }

    public void setExpenseSubgroupId(long expenseSubgroupId) {
        this.expenseSubgroupId = expenseSubgroupId;
    }

    public String getExpenseSubgroupMarker() {
        return expenseSubgroupMarker;
    }

    public void setExpenseSubgroupMarker(String expenseSubgroupMarker) {
        this.expenseSubgroupMarker = expenseSubgroupMarker;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public boolean isDeviated() {
        return isDeviated;
    }

    public void setDeviated(boolean deviated) {
        isDeviated = deviated;
    }

    public String getDeviationRemarks() {
        return deviationRemarks;
    }

    public void setDeviationRemarks(String deviationRemarks) {
        this.deviationRemarks = deviationRemarks;
    }

    public String getTransportDescriptor() {
        return transportDescriptor;
    }

    public void setTransportDescriptor(String transportDescriptor) {
        this.transportDescriptor = transportDescriptor;
    }

    public String getMobilityDescriptor() {
        return mobilityDescriptor;
    }

    public void setMobilityDescriptor(String mobilityDescriptor) {
        this.mobilityDescriptor = mobilityDescriptor;
    }

    public String getTravelDescriptor() {
        return travelDescriptor;
    }

    public void setTravelDescriptor(String travelDescriptor) {
        this.travelDescriptor = travelDescriptor;
    }
}
