package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.time.LocalDate;

public class ExpenseRuleCreateViewModel {
    @Size(max = 50)
    private String branchCode;
    @Size(max = 50)
    private String departmentCode;
    @Size(max = 50)
    private String locationCategory;
    @Size(max = 50)
    private String employeeType;
    @Size(max = 50)
    private String employeeGrade;
    @Size(max = 50)
    private String costCenterCode;

    private boolean isInvoiceRequired;
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private Double invoiceRequiredThreshold;

    @NotNull
    private LocalDate startDate;
    @NotNull
    private LocalDate endDate;

    //  regular specific
    @Positive
    @Max(100)
    private Double standardDeductionRate;

    //  Other
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private Double limitAmount;
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private Double maximumAmount;
    private boolean canExceedLimit;

    private boolean isPerDiemAllowed;
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private Double perDiemAmount;

    //  Unit rate specific
    private boolean isUnitRateApplicable;
    @Size(max = 50)
    private String unitOfMeasure;
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private Double unitRate;
    @Size(max = 50)
    private String unitRateType;

    private long expenseSubgroupId;

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(String locationCategory) {
        this.locationCategory = locationCategory;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public boolean isInvoiceRequired() {
        return isInvoiceRequired;
    }

    public void setInvoiceRequired(boolean invoiceRequired) {
        isInvoiceRequired = invoiceRequired;
    }

    public Double getInvoiceRequiredThreshold() {
        return invoiceRequiredThreshold;
    }

    public void setInvoiceRequiredThreshold(Double invoiceRequiredThreshold) {
        this.invoiceRequiredThreshold = invoiceRequiredThreshold;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Double getStandardDeductionRate() {
        return standardDeductionRate;
    }

    public void setStandardDeductionRate(Double standardDeductionRate) {
        this.standardDeductionRate = standardDeductionRate;
    }

    public Double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(Double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public Double getMaximumAmount() {
        return maximumAmount;
    }

    public void setMaximumAmount(Double maximumAmount) {
        this.maximumAmount = maximumAmount;
    }

    public boolean isCanExceedLimit() {
        return canExceedLimit;
    }

    public void setCanExceedLimit(boolean canExceedLimit) {
        this.canExceedLimit = canExceedLimit;
    }

    public boolean isPerDiemAllowed() {
        return isPerDiemAllowed;
    }

    public void setPerDiemAllowed(boolean perDiemAllowed) {
        isPerDiemAllowed = perDiemAllowed;
    }

    public Double getPerDiemAmount() {
        return perDiemAmount;
    }

    public void setPerDiemAmount(Double perDiemAmount) {
        this.perDiemAmount = perDiemAmount;
    }

    public boolean isUnitRateApplicable() {
        return isUnitRateApplicable;
    }

    public void setUnitRateApplicable(boolean unitRateApplicable) {
        isUnitRateApplicable = unitRateApplicable;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public Double getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(Double unitRate) {
        this.unitRate = unitRate;
    }

    public String getUnitRateType() {
        return unitRateType;
    }

    public void setUnitRateType(String unitRateType) {
        this.unitRateType = unitRateType;
    }

    public long getExpenseSubgroupId() {
        return expenseSubgroupId;
    }

    public void setExpenseSubgroupId(long expenseSubgroupId) {
        this.expenseSubgroupId = expenseSubgroupId;
    }
}
