package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.IntervalMarker;

public class ReportLimitConsumptionViewModel {
    private IntervalMarker interval;
    private Double limitAmount;
    private Double consumedAmount;

    public IntervalMarker getInterval() {
        return interval;
    }

    public void setInterval(IntervalMarker interval) {
        this.interval = interval;
    }

    public Double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(Double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public Double getConsumedAmount() {
        return consumedAmount;
    }

    public void setConsumedAmount(Double consumedAmount) {
        this.consumedAmount = consumedAmount;
    }
}
