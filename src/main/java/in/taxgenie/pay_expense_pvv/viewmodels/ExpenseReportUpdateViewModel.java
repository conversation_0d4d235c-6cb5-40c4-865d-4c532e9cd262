package in.taxgenie.pay_expense_pvv.viewmodels;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.time.LocalDate;

public class ExpenseReportUpdateViewModel {
    private long id;

    private String documentIdentifier;

    private LocalDate createdDate;
    private LocalDate startDate;
    private LocalDate endDate;

    @NotBlank
    @Size(max = 200)
    private String reportTitle;

    @Size(max = 200)
    private String description;

    @Size(max = 100)
    private String purpose;

    @Positive
    private double reportClaimAmount;


    //  GST computation
    @Positive
    private double reportSgstAmount;
    @Positive
    private double reportCgstAmount;
    @Positive
    private double reportIgstAmount;
    @Positive
    private double reportTaxableAmount;

    private long expenseMetadataId;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getReportClaimAmount() {
        return reportClaimAmount;
    }

    public void setReportClaimAmount(double reportClaimAmount) {
        this.reportClaimAmount = reportClaimAmount;
    }

    public double getReportSgstAmount() {
        return reportSgstAmount;
    }

    public void setReportSgstAmount(double reportSgstAmount) {
        this.reportSgstAmount = reportSgstAmount;
    }

    public double getReportCgstAmount() {
        return reportCgstAmount;
    }

    public void setReportCgstAmount(double reportCgstAmount) {
        this.reportCgstAmount = reportCgstAmount;
    }

    public double getReportIgstAmount() {
        return reportIgstAmount;
    }

    public void setReportIgstAmount(double reportIgstAmount) {
        this.reportIgstAmount = reportIgstAmount;
    }

    public double getReportTaxableAmount() {
        return reportTaxableAmount;
    }

    public void setReportTaxableAmount(double reportTaxableAmount) {
        this.reportTaxableAmount = reportTaxableAmount;
    }

    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }
}
