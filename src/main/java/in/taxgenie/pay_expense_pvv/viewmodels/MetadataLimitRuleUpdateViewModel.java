package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.IntervalMarker;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDate;

public class MetadataLimitRuleUpdateViewModel {
    private long id;
    @Positive
    private double limitAmount;
    @NotNull
    private IntervalMarker intervalMarker;
    private String branchCode;
    private String departmentCode;
    private String costCenterCode;
    private String employeeType;
    private String employeeGrade;
    private LocalDate startDate;
    private LocalDate endDate;
    private String locationCategory;

    @Min(0)
    @Max(1_000)
    private int expenseCountLimit;

    //  Common
    private boolean isFrozen;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public IntervalMarker getIntervalMarker() {
        return intervalMarker;
    }

    public void setIntervalMarker(IntervalMarker intervalMarker) {
        this.intervalMarker = intervalMarker;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public String getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(String locationCategory) {
        this.locationCategory = locationCategory;
    }

    public int getExpenseCountLimit() {
        return expenseCountLimit;
    }

    public void setExpenseCountLimit(int expenseCountLimit) {
        this.expenseCountLimit = expenseCountLimit;
    }
}
