package in.taxgenie.pay_expense_pvv.viewmodels;

public class ExpenseValidationStatusViewModel {
    private final boolean isValid;
    private final String validationErrors;

    public ExpenseValidationStatusViewModel(boolean isValid, String validationErrors) {
        this.isValid = isValid;
        this.validationErrors = validationErrors;
    }

    public boolean isValid() {
        return isValid;
    }

    public String getValidationErrors() {
        return validationErrors;
    }
}
