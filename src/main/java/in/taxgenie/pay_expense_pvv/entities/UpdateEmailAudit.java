package in.taxgenie.pay_expense_pvv.entities;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
public class UpdateEmailAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyId;
    private long currentId;
    private String fromEmail;
    private String toEmail;
    private String changedByEmail;
    private String changedById;

    public long getId() {
        return id;
    }
    public void setId(long id) {
        this.id = id;
    }
    public long getCompanyId() {
        return companyId;
    }
    public void setCompanyId(long companyId) {
        this.companyId = companyId;
    }
    public long getCurrentId() {
        return currentId;
    }
    public void setCurrentId(long currentId) {
        this.currentId = currentId;
    }
    public String getFromEmail() {
        return fromEmail;
    }
    public void setFromEmail(String fromEmail) {
        this.fromEmail = fromEmail;
    }
    public String getToEmail() {
        return toEmail;
    }
    public void setToEmail(String toEmail) {
        this.toEmail = toEmail;
    }
    public String getChangedByEmail() {
        return changedByEmail;
    }
    public void setChangedByEmail(String changedByEmail) {
        this.changedByEmail = changedByEmail;
    }
    public String getChangedById() {
        return changedById;
    }
    public void setChangedById(String changedById) {
        this.changedById = changedById;
    }
}
