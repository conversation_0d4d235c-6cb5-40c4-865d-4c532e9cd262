package in.taxgenie.pay_expense_pvv.entities;

import javax.persistence.*;

import java.time.LocalDate;
import java.util.Objects;

@Entity
public class ExpenseVouchersReport {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id", nullable = false)
    private int id;
    @Basic
    @Column(name = "company_code", nullable = true)
    private Long companyCode;
    @Basic
    @Column(name = "created_date", nullable = true)
    private LocalDate createdDate;
    @Basic
    @Column(name = "report_status", nullable = true)
    private ReportStatus reportStatus = ReportStatus.DRAFT;
    @Basic
    @Column(name = "document_identifier", nullable = true, length = 30)
    private String documentIdentifier;
    @Basic
    @Column(name = "report_claim_amount", nullable = true, precision = 0)
    private Double reportClaimAmount;
    @Basic
    @Column(name = "paid_status", nullable = true)
    private Integer paidStatus;
    @Basic
    @Column(name = "total_paid_amount", nullable = true, precision = 0)
    private Double totalPaidAmount;
    @Basic
    @Column(name = "gl_posting_date", nullable = true)
    private LocalDate glPostingDate;
    @Basic
    @Column(name = "expense_report_id", nullable = true)
    private Long expenseReportId;
    @Basic
    @Column(name = "employee_branch", nullable = true, length = 30)
    private String employeeBranch;
    @Basic
    @Column(name = "start_date", nullable = true)
    private LocalDate startDate;
    @Basic
    @Column(name = "end_date", nullable = true)
    private LocalDate endDate;
    @Basic
    @Column(name = "value03", nullable = true, length = 30)
    private String value03;
    @Basic
    @Column(name = "employee_grade", nullable = true, length = 30)
    private String employeeGrade;
    @Basic
    @Column(name = "submit_date", nullable = true)
    private LocalDate submitDate;
    @Basic
    @Column(name = "payment_date", nullable = true)
    private LocalDate paymentDate;
    @Basic
    @Column(name = "payment_reference", nullable = true, length = 30)
    private String paymentReference;
    @Basic
    @Column(name = "expense_type", nullable = true, length = 30)
    private String expenseType;
    @Basic
    @Column(name = "report_title", nullable = true, length = 300)
    private String reportTitle;
    @Basic
    @Column(name = "first_name", nullable = true, length = 30)
    private String firstName;
    @Basic
    @Column(name = "middle_name", nullable = true, length = 30)
    private String middleName;
    @Basic
    @Column(name = "last_name", nullable = true, length = 30)
    private String lastName;
    @Basic
    @Column(name = "employee_cost_center", nullable = true, length = 30)
    private String employeeCostCenter;
    @Basic
    @Column(name = "employee_department", nullable = true, length = 30)
    private String employeeDepartment;
    @Basic
    @Column(name = "sent_back_times", nullable = true)
    private Integer sentBackTimes;
    @Basic
    @Column(name = "sum_expense", nullable = true, precision = 0)
    private Double sumExpense;
    @Basic
    @Column(name = "sent_back_date", nullable = true)
    private LocalDate sentBackDate;
    @Basic
    @Column(name = "remarks", nullable = true, length = 300)
    private String remarks;
    @Basic
    @Column(name = "sent_back_by_code", nullable = true, length = 30)
    private String sentBackByCode;
    @Basic
    @Column(name = "sent_back_by", nullable = true, length = 50)
    private String sentBackBy;
    @Basic
    @Column(name = "current_approver_employee_code", nullable = true, length = 30)
    private String currentApproverEmployeeCode;
    @Basic
    @Column(name = "current_approver_name", nullable = true, length = 30)
    private String currentApproverName;
    @Basic
    @Column(name = "approved_date", nullable = true)
    private LocalDate approvedDate;
    @Basic
    @Column(name = "employee_code", nullable = true, length = 50)
    private String employeeCode;
    @Basic
    @Column(name = "pending_at", nullable = true, length = 50)
    private String pendingAt;
    @Basic
    @Column(name = "current_approver_employee_code2", nullable = true, length = 50)
    private String currentApproverEmployeeCode2;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(Long companyCode) {
        this.companyCode = companyCode;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public ReportStatus getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(ReportStatus reportStatus) {
        this.reportStatus = reportStatus;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public Double getReportClaimAmount() {
        return reportClaimAmount;
    }

    public void setReportClaimAmount(Double reportClaimAmount) {
        this.reportClaimAmount = reportClaimAmount;
    }

    public Integer getPaidStatus() {
        return paidStatus;
    }

    public void setPaidStatus(Integer paidStatus) {
        this.paidStatus = paidStatus;
    }

    public Double getTotalPaidAmount() {
        return totalPaidAmount;
    }

    public void setTotalPaidAmount(Double totalPaidAmount) {
        this.totalPaidAmount = totalPaidAmount;
    }

    public LocalDate getGlPostingDate() {
        return glPostingDate;
    }

    public void setGlPostingDate(LocalDate glPostingDate) {
        this.glPostingDate = glPostingDate;
    }

    public Long getExpenseReportId() {
        return expenseReportId;
    }

    public void setExpenseReportId(Long expenseReportId) {
        this.expenseReportId = expenseReportId;
    }

    public String getEmployeeBranch() {
        return employeeBranch;
    }

    public void setEmployeeBranch(String employeeBranch) {
        this.employeeBranch = employeeBranch;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getValue03() {
        return value03;
    }

    public void setValue03(String value03) {
        this.value03 = value03;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public LocalDate getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDate submitDate) {
        this.submitDate = submitDate;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmployeeCostCenter() {
        return employeeCostCenter;
    }

    public void setEmployeeCostCenter(String employeeCostCenter) {
        this.employeeCostCenter = employeeCostCenter;
    }

    public String getEmployeeDepartment() {
        return employeeDepartment;
    }

    public void setEmployeeDepartment(String employeeDepartment) {
        this.employeeDepartment = employeeDepartment;
    }

    public Integer getSentBackTimes() {
        return sentBackTimes;
    }

    public void setSentBackTimes(Integer sentBackTimes) {
        this.sentBackTimes = sentBackTimes;
    }

    public Double getSumExpense() {
        return sumExpense;
    }

    public void setSumExpense(Double sumExpense) {
        this.sumExpense = sumExpense;
    }

    public LocalDate getSentBackDate() {
        return sentBackDate;
    }

    public void setSentBackDate(LocalDate sentBackDate) {
        this.sentBackDate = sentBackDate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getSentBackByCode() {
        return sentBackByCode;
    }

    public void setSentBackByCode(String sentBackByCode) {
        this.sentBackByCode = sentBackByCode;
    }

    public String getSentBackBy() {
        return sentBackBy;
    }

    public void setSentBackBy(String sentBackBy) {
        this.sentBackBy = sentBackBy;
    }

    public String getCurrentApproverEmployeeCode() {
        return currentApproverEmployeeCode;
    }

    public void setCurrentApproverEmployeeCode(String currentApproverEmployeeCode) {
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
    }

    public String getCurrentApproverName() {
        return currentApproverName;
    }

    public void setCurrentApproverName(String currentApproverName) {
        this.currentApproverName = currentApproverName;
    }

    public LocalDate getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(LocalDate approvedDate) {
        this.approvedDate = approvedDate;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getPendingAt() {
        return pendingAt;
    }

    public void setPendingAt(String pendingAt) {
        this.pendingAt = pendingAt;
    }

    public String getCurrentApproverEmployeeCode2() {
        return currentApproverEmployeeCode2;
    }

    public void setCurrentApproverEmployeeCode2(String currentApproverEmployeeCode2) {
        this.currentApproverEmployeeCode2 = currentApproverEmployeeCode2;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExpenseVouchersReport that = (ExpenseVouchersReport) o;
        return id == that.id && Objects.equals(companyCode, that.companyCode) && Objects.equals(createdDate, that.createdDate) && Objects.equals(reportStatus, that.reportStatus) && Objects.equals(documentIdentifier, that.documentIdentifier) && Objects.equals(reportClaimAmount, that.reportClaimAmount) && Objects.equals(paidStatus, that.paidStatus) && Objects.equals(totalPaidAmount, that.totalPaidAmount) && Objects.equals(glPostingDate, that.glPostingDate) && Objects.equals(expenseReportId, that.expenseReportId) && Objects.equals(employeeBranch, that.employeeBranch) && Objects.equals(startDate, that.startDate) && Objects.equals(endDate, that.endDate) && Objects.equals(value03, that.value03) && Objects.equals(employeeGrade, that.employeeGrade) && Objects.equals(submitDate, that.submitDate) && Objects.equals(paymentDate, that.paymentDate) && Objects.equals(paymentReference, that.paymentReference) && Objects.equals(expenseType, that.expenseType) && Objects.equals(reportTitle, that.reportTitle) && Objects.equals(firstName, that.firstName) && Objects.equals(middleName, that.middleName) && Objects.equals(lastName, that.lastName) && Objects.equals(employeeCostCenter, that.employeeCostCenter) && Objects.equals(employeeDepartment, that.employeeDepartment) && Objects.equals(sentBackTimes, that.sentBackTimes) && Objects.equals(sumExpense, that.sumExpense) && Objects.equals(sentBackDate, that.sentBackDate) && Objects.equals(remarks, that.remarks) && Objects.equals(sentBackByCode, that.sentBackByCode) && Objects.equals(sentBackBy, that.sentBackBy) && Objects.equals(currentApproverEmployeeCode, that.currentApproverEmployeeCode) && Objects.equals(currentApproverName, that.currentApproverName) && Objects.equals(approvedDate, that.approvedDate) && Objects.equals(employeeCode, that.employeeCode) && Objects.equals(pendingAt, that.pendingAt) && Objects.equals(currentApproverEmployeeCode2, that.currentApproverEmployeeCode2);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, companyCode, createdDate, reportStatus, documentIdentifier, reportClaimAmount, paidStatus, totalPaidAmount, glPostingDate, expenseReportId, employeeBranch, startDate, endDate, value03, employeeGrade, submitDate, paymentDate, paymentReference, expenseType, reportTitle, firstName, middleName, lastName, employeeCostCenter, employeeDepartment, sentBackTimes, sumExpense, sentBackDate, remarks, sentBackByCode, sentBackBy, currentApproverEmployeeCode, currentApproverName, approvedDate, employeeCode, pendingAt, currentApproverEmployeeCode2);
    }
}
