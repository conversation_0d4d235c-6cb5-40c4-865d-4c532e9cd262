package in.taxgenie.pay_expense_pvv.entities;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
public class ExpenseReport {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyCode;

    //  Core fields
    @Column(length = 50)
    private String documentIdentifier;

    private LocalDate createdDate;
    private LocalDate submitDate;
    private LocalDate startDate;
    private LocalDate endDate;

    @Column(length = 200)
    private String reportTitle;
    @Column(length = 200)
    private String description;
    @Column(length = 100)
    private String purpose;

    private double reportClaimAmount = 0;


    private ReportStatus reportStatus = ReportStatus.DRAFT;
    private int actionLevel = 0;
    private String deviationRemarks;
    private boolean containsDeviation;
    private boolean containsSentBack;
    private String expenseType;

    //  GST computation
    private double reportSgstAmount = 0;
    private double reportCgstAmount = 0;
    private double reportIgstAmount = 0;
    private double reportTaxableAmount = 0;

    //  GL Transmission and Posting
    private boolean acknowledgedByErp;
    private ZonedDateTime erpAcknowledgementTimestamp;
    private boolean isPostedToGl;
    private LocalDate glPostingDate;
    private String glDocumentReference;


    private PaidStatus paidStatus;
    private double totalPaidAmount = 0;
    private double totalTdsAmount = 0;

    @Column(length = 100)
    private String currentApproverFirstName;
    @Column(length = 100)
    private String currentApproverLastName;
    @Column(length = 50)
    private String currentApproverEmployeeCode;


    //  Employee master data
    @Column(length = 100)
    private String firstName;
    @Column(length = 100)
    private String middleName;
    @Column(length = 100)
    private String lastName;

    @Column(length = 200)
    private String employeeEmail;
    @Column(length = 50)
    private String employeeCode;
    private Long employeeSystemId;

    private String gender;

    private LocalDate paymentDate;


    @Column(length = 50)
    private String employeeHrmsCode;
    @Column(length = 50)
    private String employeeType;
    @Column(length = 50)
    private String employeeGrade;
    @Column(length = 50)
    private String employeeBranch;
    @Column(length = 50)
    private String employeeDepartment;
    @Column(length = 50)
    private String employeeCostCenter;
    @Column(length = 50)
    private String employeeGlMainAccountCode;
    @Column(length = 50)
    private String employeeGlSubAccountCode;
    @Column(length = 50)
    private String employeeProfitCenter;


    //  Dimensions
    @Column(length = 50)
    private String dimension01;
    @Column(length = 50)
    private String dimension02;
    @Column(length = 50)
    private String dimension03;
    @Column(length = 50)
    private String dimension04;
    @Column(length = 50)
    private String dimension05;
    @Column(length = 50)
    private String dimension06;
    @Column(length = 50)
    private String dimension07;
    @Column(length = 50)
    private String dimension08;
    @Column(length = 50)
    private String dimension09;
    @Column(length = 50)
    private String dimension10;


    //  Key-value pairs
    @Column(length = 50)
    private String key01;
    @Column(length = 50)
    private String key02;
    @Column(length = 50)
    private String key03;
    @Column(length = 50)
    private String key04;
    @Column(length = 50)
    private String key05;
    @Column(length = 50)
    private String key06;
    @Column(length = 50)
    private String key07;
    @Column(length = 50)
    private String key08;
    @Column(length = 50)
    private String key09;
    @Column(length = 50)
    private String key10;

    @Column(length = 50)
    private String value01;
    @Column(length = 50)
    private String value02;
    @Column(length = 50)
    private String value03;
    @Column(length = 50)
    private String value04;
    @Column(length = 50)
    private String value05;
    @Column(length = 50)
    private String value06;
    @Column(length = 50)
    private String value07;
    @Column(length = 50)
    private String value08;
    @Column(length = 50)
    private String value09;
    @Column(length = 50)
    private String value10;

    private String currentApproverSystemIdCode;

    private String sendBackRemarks;
    private String rejectRemarks;
    private String delegationRemarks;
    private String defaultApproverRemarks;

    private ExpenseActionStatus actionStatus;

    //  Common
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    //  Optimistic locking
    @Version
    private Long version;

    //  Mapping
    @ManyToOne(optional = false)
    @JoinColumn(name = "expenseMetadataId", insertable = false, updatable = false)
    private ExpenseMetadata expenseMetadata;
    private long expenseMetadataId;

    @OneToMany(mappedBy = "expenseReport")
    private List<Expense> expenses = new ArrayList<>();

    @OneToMany(mappedBy = "expenseReport")
    private List<ReportState> reportStates = new ArrayList<>();

    @OneToMany(mappedBy = "expenseReport")
    private List<Payment> paymentAdvices = new ArrayList<>();

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDate getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDate submitDate) {
        this.submitDate = submitDate;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public double getReportClaimAmount() {
        return reportClaimAmount;
    }

    public void setReportClaimAmount(double reportClaimAmount) {
        this.reportClaimAmount = reportClaimAmount;
    }

    public ReportStatus getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(ReportStatus reportStatus) {
        this.reportStatus = reportStatus;
    }

    public int getActionLevel() {
        return actionLevel;
    }

    public void setActionLevel(int actionLevel) {
        this.actionLevel = actionLevel;
    }

    public double getReportSgstAmount() {
        return reportSgstAmount;
    }

    public void setReportSgstAmount(double reportSgstAmount) {
        this.reportSgstAmount = reportSgstAmount;
    }

    public double getReportCgstAmount() {
        return reportCgstAmount;
    }

    public void setReportCgstAmount(double reportCgstAmount) {
        this.reportCgstAmount = reportCgstAmount;
    }

    public double getReportIgstAmount() {
        return reportIgstAmount;
    }

    public void setReportIgstAmount(double reportIgstAmount) {
        this.reportIgstAmount = reportIgstAmount;
    }

    public double getReportTaxableAmount() {
        return reportTaxableAmount;
    }

    public void setReportTaxableAmount(double reportTaxableAmount) {
        this.reportTaxableAmount = reportTaxableAmount;
    }

    public boolean isAcknowledgedByErp() {
        return acknowledgedByErp;
    }

    public void setAcknowledgedByErp(boolean acknowledgedByErp) {
        this.acknowledgedByErp = acknowledgedByErp;
    }

    public ZonedDateTime getErpAcknowledgementTimestamp() {
        return erpAcknowledgementTimestamp;
    }

    public void setErpAcknowledgementTimestamp(ZonedDateTime erpAcknowledgementTimestamp) {
        this.erpAcknowledgementTimestamp = erpAcknowledgementTimestamp;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public boolean isPostedToGl() {
        return isPostedToGl;
    }

    public void setPostedToGl(boolean postedToGl) {
        isPostedToGl = postedToGl;
    }

    public LocalDate getGlPostingDate() {
        return glPostingDate;
    }

    public void setGlPostingDate(LocalDate glPostingDate) {
        this.glPostingDate = glPostingDate;
    }

    public String getGlDocumentReference() {
        return glDocumentReference;
    }

    public void setGlDocumentReference(String glDocumentReference) {
        this.glDocumentReference = glDocumentReference;
    }

    public PaidStatus getPaidStatus() {
        return paidStatus;
    }

    public void setPaidStatus(PaidStatus paidStatus) {
        this.paidStatus = paidStatus;
    }

    public Double getTotalPaidAmount() {
        return totalPaidAmount;
    }

    public void setTotalPaidAmount(Double totalPaidAmount) {
        this.totalPaidAmount = totalPaidAmount;
    }

    public Double getTotalTdsAmount() {
        return totalTdsAmount;
    }

    public void setTotalTdsAmount(Double totalTdsAmount) {
        this.totalTdsAmount = totalTdsAmount;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmployeeEmail() {
        return employeeEmail;
    }

    public void setEmployeeEmail(String employeeEmail) {
        this.employeeEmail = employeeEmail;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public Long getEmployeeSystemId() {
        return employeeSystemId;
    }

    public void setEmployeeSystemId(Long employeeSystemId) {
        this.employeeSystemId = employeeSystemId;
    }

    public String getEmployeeHrmsCode() {
        return employeeHrmsCode;
    }

    public void setEmployeeHrmsCode(String employeeHrmsCode) {
        this.employeeHrmsCode = employeeHrmsCode;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public String getEmployeeBranch() {
        return employeeBranch;
    }

    public void setEmployeeBranch(String employeeBranch) {
        this.employeeBranch = employeeBranch;
    }

    public String getEmployeeDepartment() {
        return employeeDepartment;
    }

    public void setEmployeeDepartment(String employeeDepartment) {
        this.employeeDepartment = employeeDepartment;
    }

    public String getEmployeeCostCenter() {
        return employeeCostCenter;
    }

    public void setEmployeeCostCenter(String employeeCostCenter) {
        this.employeeCostCenter = employeeCostCenter;
    }

    public String getDimension01() {
        return dimension01;
    }

    public void setDimension01(String dimension01) {
        this.dimension01 = dimension01;
    }

    public String getDimension02() {
        return dimension02;
    }

    public void setDimension02(String dimension02) {
        this.dimension02 = dimension02;
    }

    public String getDimension03() {
        return dimension03;
    }

    public void setDimension03(String dimension03) {
        this.dimension03 = dimension03;
    }

    public String getDimension04() {
        return dimension04;
    }

    public void setDimension04(String dimension04) {
        this.dimension04 = dimension04;
    }

    public String getDimension05() {
        return dimension05;
    }

    public void setDimension05(String dimension05) {
        this.dimension05 = dimension05;
    }

    public String getDimension06() {
        return dimension06;
    }

    public void setDimension06(String dimension06) {
        this.dimension06 = dimension06;
    }

    public String getDimension07() {
        return dimension07;
    }

    public void setDimension07(String dimension07) {
        this.dimension07 = dimension07;
    }

    public String getDimension08() {
        return dimension08;
    }

    public void setDimension08(String dimension08) {
        this.dimension08 = dimension08;
    }

    public String getDimension09() {
        return dimension09;
    }

    public void setDimension09(String dimension09) {
        this.dimension09 = dimension09;
    }

    public String getDimension10() {
        return dimension10;
    }

    public void setDimension10(String dimension10) {
        this.dimension10 = dimension10;
    }

    public String getKey01() {
        return key01;
    }

    public void setKey01(String key01) {
        this.key01 = key01;
    }

    public String getKey02() {
        return key02;
    }

    public void setKey02(String key02) {
        this.key02 = key02;
    }

    public String getKey03() {
        return key03;
    }

    public void setKey03(String key03) {
        this.key03 = key03;
    }

    public String getKey04() {
        return key04;
    }

    public void setKey04(String key04) {
        this.key04 = key04;
    }

    public String getKey05() {
        return key05;
    }

    public void setKey05(String key05) {
        this.key05 = key05;
    }

    public String getKey06() {
        return key06;
    }

    public void setKey06(String key06) {
        this.key06 = key06;
    }

    public String getKey07() {
        return key07;
    }

    public void setKey07(String key07) {
        this.key07 = key07;
    }

    public String getKey08() {
        return key08;
    }

    public void setKey08(String key08) {
        this.key08 = key08;
    }

    public String getKey09() {
        return key09;
    }

    public void setKey09(String key09) {
        this.key09 = key09;
    }

    public String getKey10() {
        return key10;
    }

    public void setKey10(String key10) {
        this.key10 = key10;
    }

    public String getValue01() {
        return value01;
    }

    public void setValue01(String value01) {
        this.value01 = value01;
    }

    public String getValue02() {
        return value02;
    }

    public void setValue02(String value02) {
        this.value02 = value02;
    }

    public String getValue03() {
        return value03;
    }

    public void setValue03(String value03) {
        this.value03 = value03;
    }

    public String getValue04() {
        return value04;
    }

    public void setValue04(String value04) {
        this.value04 = value04;
    }

    public String getValue05() {
        return value05;
    }

    public void setValue05(String value05) {
        this.value05 = value05;
    }

    public String getValue06() {
        return value06;
    }

    public void setValue06(String value06) {
        this.value06 = value06;
    }

    public String getValue07() {
        return value07;
    }

    public void setValue07(String value07) {
        this.value07 = value07;
    }

    public String getValue08() {
        return value08;
    }

    public void setValue08(String value08) {
        this.value08 = value08;
    }

    public String getValue09() {
        return value09;
    }

    public void setValue09(String value09) {
        this.value09 = value09;
    }

    public String getValue10() {
        return value10;
    }

    public void setValue10(String value10) {
        this.value10 = value10;
    }

    public long getCreatingUserId() {
        return creatingUserId;
    }

    public void setCreatingUserId(long creatingUserId) {
        this.creatingUserId = creatingUserId;
    }

    public ZonedDateTime getCreatedTimestamp() {
        return createdTimestamp;
    }

    public void setCreatedTimestamp(ZonedDateTime createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }

    public Long getUpdatingUserId() {
        return updatingUserId;
    }

    public void setUpdatingUserId(Long updatingUserId) {
        this.updatingUserId = updatingUserId;
    }

    public ZonedDateTime getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    public void setUpdatedTimestamp(ZonedDateTime updatedTimestamp) {
        this.updatedTimestamp = updatedTimestamp;
    }

    public ExpenseMetadata getExpenseMetadata() {
        return expenseMetadata;
    }

    public void setExpenseMetadata(ExpenseMetadata expenseMetadata) {
        this.expenseMetadata = expenseMetadata;
    }

    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public List<Expense> getExpenses() {
        return expenses;
    }

    public void setExpenses(List<Expense> expenses) {
        this.expenses = expenses;
    }

    public List<ReportState> getReportStates() {
        return reportStates;
    }

    public void setReportStates(List<ReportState> reportStates) {
        this.reportStates = reportStates;
    }

    public List<Payment> getPaymentAdvices() {
        return paymentAdvices;
    }

    public void setPaymentAdvices(List<Payment> paymentAdvices) {
        this.paymentAdvices = paymentAdvices;
    }

    public String getEmployeeGlMainAccountCode() {
        return employeeGlMainAccountCode;
    }

    public void setEmployeeGlMainAccountCode(String employeeGlMainAccountCode) {
        this.employeeGlMainAccountCode = employeeGlMainAccountCode;
    }

    public String getEmployeeGlSubAccountCode() {
        return employeeGlSubAccountCode;
    }

    public void setEmployeeGlSubAccountCode(String employeeGlSubAccountCode) {
        this.employeeGlSubAccountCode = employeeGlSubAccountCode;
    }

    public void setTotalPaidAmount(double totalPaidAmount) {
        this.totalPaidAmount = totalPaidAmount;
    }

    public void setTotalTdsAmount(double totalTdsAmount) {
        this.totalTdsAmount = totalTdsAmount;
    }

    public String getEmployeeProfitCenter() {
        return employeeProfitCenter;
    }

    public void setEmployeeProfitCenter(String employeeProfitCenter) {
        this.employeeProfitCenter = employeeProfitCenter;
    }

    public String getDeviationRemarks() {
        return deviationRemarks;
    }

    public void setDeviationRemarks(String deviationRemarks) {
        this.deviationRemarks = deviationRemarks;
    }

    public boolean isContainsDeviation() {
        return containsDeviation;
    }

    public void setContainsDeviation(boolean containsDeviation) {
        this.containsDeviation = containsDeviation;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public boolean isContainsSentBack() {
        return containsSentBack;
    }

    public void setContainsSentBack(boolean containsSentBack) {
        this.containsSentBack = containsSentBack;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getCurrentApproverFirstName() {
        return currentApproverFirstName;
    }

    public void setCurrentApproverFirstName(String currentApproverFirstName) {
        this.currentApproverFirstName = currentApproverFirstName;
    }

    public String getCurrentApproverLastName() {
        return currentApproverLastName;
    }

    public void setCurrentApproverLastName(String currentApproverLastName) {
        this.currentApproverLastName = currentApproverLastName;
    }

    public String getCurrentApproverSystemIdCode() {
        return currentApproverSystemIdCode;
    }

    public void setCurrentApproverSystemIdCode(String currentApproverSystemIdCode) {
        this.currentApproverSystemIdCode = currentApproverSystemIdCode;
    }

    public String getCurrentApproverEmployeeCode() {
        return currentApproverEmployeeCode;
    }

    public void setCurrentApproverEmployeeCode(String currentApproverEmployeeCode) {

        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
    }

    public String getSendBackRemarks() {
        return sendBackRemarks;
    }

    public void setSendBackRemarks(String sendBackRemarks) {
        this.sendBackRemarks = sendBackRemarks;
    }

    public String getRejectRemarks() {
        return rejectRemarks;
    }

    public void setRejectRemarks(String rejectRemarks) {
        this.rejectRemarks = rejectRemarks;
    }

    public String getDelegationRemarks() {
        return delegationRemarks;
    }

    public void setDelegationRemarks(String delegationRemarks) {
        this.delegationRemarks = delegationRemarks;
    }

    public String getDefaultApproverRemarks() {
        return defaultApproverRemarks;
    }

    public void setDefaultApproverRemarks(String defaultApproverRemarks) {
        this.defaultApproverRemarks = defaultApproverRemarks;
    }

    public ExpenseActionStatus getActionStatus() {
        return actionStatus;
    }

    public void setActionStatus(ExpenseActionStatus actionStatus) {
        this.actionStatus = actionStatus;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }
}
