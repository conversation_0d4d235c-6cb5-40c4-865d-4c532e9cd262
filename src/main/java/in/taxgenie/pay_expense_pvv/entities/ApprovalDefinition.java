package in.taxgenie.pay_expense_pvv.entities;

import javax.persistence.*;
import java.time.ZonedDateTime;

@Entity
public class ApprovalDefinition {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyCode;
    private int level;
    private double limitAmount;
    private String approvalMatcher;
    private String approvalTitle;
    private boolean shouldFetchFromEmployeeMaster;
    private StateChannel channel;
    private boolean isExternalToCem;
    private boolean isFrozen;
    private boolean forDeviation;

    //  Common
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    //  Mappings
    @ManyToOne(optional = false)
    @JoinColumn(name = "expenseMetadataId", insertable = false, updatable = false)
    private ExpenseMetadata expenseMetadata;
    private long expenseMetadataId;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public String getApprovalMatcher() {
        return approvalMatcher;
    }

    public void setApprovalMatcher(String approvalMatcher) {
        this.approvalMatcher = approvalMatcher;
    }

    public String getApprovalTitle() {
        return approvalTitle;
    }

    public void setApprovalTitle(String approvalTitle) {
        this.approvalTitle = approvalTitle;
    }

    public boolean isShouldFetchFromEmployeeMaster() {
        return shouldFetchFromEmployeeMaster;
    }

    public void setShouldFetchFromEmployeeMaster(boolean shouldFetchFromEmployeeMaster) {
        this.shouldFetchFromEmployeeMaster = shouldFetchFromEmployeeMaster;
    }

    public StateChannel getChannel() {
        return channel;
    }

    public void setChannel(StateChannel channel) {
        this.channel = channel;
    }

    public boolean isExternalToCem() {
        return isExternalToCem;
    }

    public void setExternalToCem(boolean externalToCem) {
        isExternalToCem = externalToCem;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public long getCreatingUserId() {
        return creatingUserId;
    }

    public void setCreatingUserId(long creatingUserId) {
        this.creatingUserId = creatingUserId;
    }

    public ZonedDateTime getCreatedTimestamp() {
        return createdTimestamp;
    }

    public void setCreatedTimestamp(ZonedDateTime createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }

    public Long getUpdatingUserId() {
        return updatingUserId;
    }

    public void setUpdatingUserId(Long updatingUserId) {
        this.updatingUserId = updatingUserId;
    }

    public ZonedDateTime getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    public void setUpdatedTimestamp(ZonedDateTime updatedTimestamp) {
        this.updatedTimestamp = updatedTimestamp;
    }

    public ExpenseMetadata getExpenseMetadata() {
        return expenseMetadata;
    }

    public void setExpenseMetadata(ExpenseMetadata expenseMetadata) {
        this.expenseMetadata = expenseMetadata;
    }

    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public boolean isForDeviation() {
        return forDeviation;
    }

    public void setForDeviation(boolean forDeviation) {
        this.forDeviation = forDeviation;
    }
}
