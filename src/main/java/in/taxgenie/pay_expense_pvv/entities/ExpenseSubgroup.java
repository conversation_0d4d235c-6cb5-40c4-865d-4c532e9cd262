package in.taxgenie.pay_expense_pvv.entities;

import javax.persistence.*;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
public class ExpenseSubgroup {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyCode;

    private String expenseCode;
    private String expenseSubgroup;
    private String expenseSubgroupPrefix;
    private String expenseGenre;
    private String expenseGenrePrefix;
    private boolean isLocationRequired;
    private String glAccountCode;
    private String description;

    private boolean merchantRequired;

    //  flags
    //  Should be moved to a separate entity called Expense Descriptor
    //  This is a tentative solution
    private boolean isSourceLocationApplicable;
    private boolean isDestinationLocationApplicable;
    private boolean isGstEntryAllowed;
    private boolean isStandardDeductionApplicable;
    private boolean isDateRangeApplicable;
    private boolean isExpenseIdentifierApplicable;
    private boolean isTransportDescriptorApplicable;
    private boolean isMobilityDescriptorApplicable;
    private boolean isTravelDescriptorApplicable;

    private int rulesCount;

    private String frequency;

    private String applicableGender;

    //  Common
    private boolean isFrozen;
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    //  Mapping
    @ManyToOne(optional = false)
    @JoinColumn(name = "expenseMetadataId", insertable = false, updatable = false)
    private ExpenseMetadata expenseMetadata;
    private long expenseMetadataId;

    @OneToMany(mappedBy = "expenseSubgroup")
    private List<ExpenseRule> rules = new ArrayList<>();

    @OneToMany(mappedBy = "expenseSubgroup")
    private List<Expense> expenses = new ArrayList<>();

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public String getExpenseCode() {
        return expenseCode;
    }

    public void setExpenseCode(String expenseCode) {
        this.expenseCode = expenseCode;
    }

    public String getExpenseSubgroup() {
        return expenseSubgroup;
    }

    public void setExpenseSubgroup(String expenseSubgroup) {
        this.expenseSubgroup = expenseSubgroup;
    }

    public String getExpenseSubgroupPrefix() {
        return expenseSubgroupPrefix;
    }

    public void setExpenseSubgroupPrefix(String expenseSubgroupPrefix) {
        this.expenseSubgroupPrefix = expenseSubgroupPrefix;
    }

    public String getExpenseGenre() {
        return expenseGenre;
    }

    public void setExpenseGenre(String expenseGenre) {
        this.expenseGenre = expenseGenre;
    }

    public String getExpenseGenrePrefix() {
        return expenseGenrePrefix;
    }

    public void setExpenseGenrePrefix(String expenseGenrePrefix) {
        this.expenseGenrePrefix = expenseGenrePrefix;
    }

    public boolean isLocationRequired() {
        return isLocationRequired;
    }

    public void setLocationRequired(boolean locationRequired) {
        isLocationRequired = locationRequired;
    }

    public String getGlAccountCode() {
        return glAccountCode;
    }

    public void setGlAccountCode(String glAccountCode) {
        this.glAccountCode = glAccountCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isSourceLocationApplicable() {
        return isSourceLocationApplicable;
    }

    public void setSourceLocationApplicable(boolean sourceLocationApplicable) {
        isSourceLocationApplicable = sourceLocationApplicable;
    }

    public boolean isDestinationLocationApplicable() {
        return isDestinationLocationApplicable;
    }

    public void setDestinationLocationApplicable(boolean destinationLocationApplicable) {
        isDestinationLocationApplicable = destinationLocationApplicable;
    }

    public boolean isGstEntryAllowed() {
        return isGstEntryAllowed;
    }

    public void setGstEntryAllowed(boolean gstEntryAllowed) {
        isGstEntryAllowed = gstEntryAllowed;
    }

    public boolean isStandardDeductionApplicable() {
        return isStandardDeductionApplicable;
    }

    public void setStandardDeductionApplicable(boolean standardDeductionApplicable) {
        isStandardDeductionApplicable = standardDeductionApplicable;
    }

    public boolean isDateRangeApplicable() {
        return isDateRangeApplicable;
    }

    public void setDateRangeApplicable(boolean dateRangeApplicable) {
        isDateRangeApplicable = dateRangeApplicable;
    }

    public boolean isExpenseIdentifierApplicable() {
        return isExpenseIdentifierApplicable;
    }

    public void setExpenseIdentifierApplicable(boolean expenseIdentifierApplicable) {
        isExpenseIdentifierApplicable = expenseIdentifierApplicable;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public long getCreatingUserId() {
        return creatingUserId;
    }

    public void setCreatingUserId(long creatingUserId) {
        this.creatingUserId = creatingUserId;
    }

    public ZonedDateTime getCreatedTimestamp() {
        return createdTimestamp;
    }

    public void setCreatedTimestamp(ZonedDateTime createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }

    public Long getUpdatingUserId() {
        return updatingUserId;
    }

    public void setUpdatingUserId(Long updatingUserId) {
        this.updatingUserId = updatingUserId;
    }

    public ZonedDateTime getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    public void setUpdatedTimestamp(ZonedDateTime updatedTimestamp) {
        this.updatedTimestamp = updatedTimestamp;
    }

    public ExpenseMetadata getExpenseMetadata() {
        return expenseMetadata;
    }

    public void setExpenseMetadata(ExpenseMetadata expenseMetadata) {
        this.expenseMetadata = expenseMetadata;
    }

    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public List<ExpenseRule> getRules() {
        return rules;
    }

    public void setRules(List<ExpenseRule> rules) {
        this.rules = rules;
    }

    public List<Expense> getExpenses() {
        return expenses;
    }

    public void setExpenses(List<Expense> expenses) {
        this.expenses = expenses;
    }

    public boolean isMerchantRequired() {
        return merchantRequired;
    }

    public void setMerchantRequired(boolean merchantRequired) {
        this.merchantRequired = merchantRequired;
    }

    public String getApplicableGender() {
        return applicableGender;
    }

    public void setApplicableGender(String applicableGender) {
        this.applicableGender = applicableGender;
    }

    public boolean isTransportDescriptorApplicable() {
        return isTransportDescriptorApplicable;
    }

    public void setTransportDescriptorApplicable(boolean transportDescriptorApplicable) {
        isTransportDescriptorApplicable = transportDescriptorApplicable;
    }

    public boolean isMobilityDescriptorApplicable() {
        return isMobilityDescriptorApplicable;
    }

    public void setMobilityDescriptorApplicable(boolean mobilityDescriptorApplicable) {
        isMobilityDescriptorApplicable = mobilityDescriptorApplicable;
    }

    public boolean isTravelDescriptorApplicable() {
        return isTravelDescriptorApplicable;
    }

    public void setTravelDescriptorApplicable(boolean travelDescriptorApplicable) {
        isTravelDescriptorApplicable = travelDescriptorApplicable;
    }

    public int getRulesCount() {
        return rulesCount;
    }

    public void setRulesCount(int rulesCount) {
        this.rulesCount = rulesCount;
    }
}
