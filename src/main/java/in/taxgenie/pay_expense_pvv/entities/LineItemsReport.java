package in.taxgenie.pay_expense_pvv.entities;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.Objects;

@Entity
public class LineItemsReport {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id", nullable = false)
    private int id;
    @Basic
    @Column(name = "company_code", nullable = true)
    private Long companyCode;
    @Basic
    @Column(name = "fiscal_year", nullable = true)
    private Integer fiscalYear;
    @Basic
    @Column(name = "document_identifier", nullable = true, length = 30)
    private String documentIdentifier;
    @Basic
    @Column(name = "report_created_date", nullable = true)
    private LocalDate reportCreatedDate;
    @Basic
    @Column(name = "report_title", nullable = true, length = 300)
    private String reportTitle;
    @Basic
    @Column(name = "expense_report_id", nullable = true)
    private Long expenseReportId;
    @Basic
    @Column(name = "expense_type", nullable = true, length = 30)
    private String expenseType;
    @Basic
    @Column(name = "expense_name", nullable = true, length = 255)
    private String expenseName;
    @Basic
    @Column(name = "expense_created_date", nullable = true)
    private LocalDate expenseCreatedDate;
    @Basic
    @Column(name = "source_location", nullable = true, length = 50)
    private String sourceLocation;
    @Basic
    @Column(name = "destination_location", nullable = true, length = 50)
    private String destinationLocation;
    @Basic
    @Column(name = "employee_code", nullable = true, length = 50)
    private String employeeCode;
    @Basic
    @Column(name = "employee_name", nullable = true, length = 510)
    private String employeeName;
    @Basic
    @Column(name = "employee_grade", nullable = true, length = 50)
    private String employeeGrade;
    @Basic
    @Column(name = "submit_date", nullable = true)
    private LocalDate submitDate;
    @Basic
    @Column(name = "voucher_amount", nullable = true, precision = 0)
    private Double voucherAmount;
    @Basic
    @Column(name = "invoice_amount", nullable = true, precision = 0)
    private Double invoiceAmount;
    @Basic
    @Column(name = "currency", nullable = true, length = 10)
    private String currency;
    @Basic
    @Column(name = "employee_gl_code", nullable = true, length = 50)
    private String employeeGlCode;
    @Basic
    @Column(name = "gl_document_reference", nullable = true, length = 255)
    private String glDocumentReference;
    @Basic
    @Column(name = "payment_date", nullable = true)
    private LocalDate paymentDate;
    @Basic
    @Column(name = "payment_reference", nullable = true, length = 255)
    private String paymentReference;
    @Basic
    @Column(name = "start_date", nullable = true)
    private LocalDate startDate;
    @Basic
    @Column(name = "end_date", nullable = true)
    private LocalDate endDate;
    @Basic
    @Column(name = "employee_cost_center", nullable = true, length = 50)
    private String employeeCostCenter;
    @Basic
    @Column(name = "employee_branch", nullable = true, length = 50)
    private String employeeBranch;
    @Basic
    @Column(name = "description", nullable = true, length = 200)
    private String description;
    @Basic
    @Column(name = "voucher_status", nullable = true)
    private ReportStatus voucherStatus;
    @Basic
    @Column(name = "entity", nullable = true, length = 50)
    private String entity;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(Long companyCode) {
        this.companyCode = companyCode;
    }

    public Integer getFiscalYear() {
        return fiscalYear;
    }

    public void setFiscalYear(Integer fiscalYear) {
        this.fiscalYear = fiscalYear;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public LocalDate getReportCreatedDate() {
        return reportCreatedDate;
    }

    public void setReportCreatedDate(LocalDate reportCreatedDate) {
        this.reportCreatedDate = reportCreatedDate;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public Long getExpenseReportId() {
        return expenseReportId;
    }

    public void setExpenseReportId(Long expenseReportId) {
        this.expenseReportId = expenseReportId;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getExpenseName() {
        return expenseName;
    }

    public void setExpenseName(String expenseName) {
        this.expenseName = expenseName;
    }

    public LocalDate getExpenseCreatedDate() {
        return expenseCreatedDate;
    }

    public void setExpenseCreatedDate(LocalDate expenseCreatedDate) {
        this.expenseCreatedDate = expenseCreatedDate;
    }

    public String getSourceLocation() {
        return sourceLocation;
    }

    public void setSourceLocation(String sourceLocation) {
        this.sourceLocation = sourceLocation;
    }

    public String getDestinationLocation() {
        return destinationLocation;
    }

    public void setDestinationLocation(String destinationLocation) {
        this.destinationLocation = destinationLocation;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public LocalDate getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDate submitDate) {
        this.submitDate = submitDate;
    }

    public Double getVoucherAmount() {
        return voucherAmount;
    }

    public void setVoucherAmount(Double voucherAmount) {
        this.voucherAmount = voucherAmount;
    }

    public Double getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(Double invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getEmployeeGlCode() {
        return employeeGlCode;
    }

    public void setEmployeeGlCode(String employeeGlCode) {
        this.employeeGlCode = employeeGlCode;
    }

    public String getGlDocumentReference() {
        return glDocumentReference;
    }

    public void setGlDocumentReference(String glDocumentReference) {
        this.glDocumentReference = glDocumentReference;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getEmployeeCostCenter() {
        return employeeCostCenter;
    }

    public void setEmployeeCostCenter(String employeeCostCenter) {
        this.employeeCostCenter = employeeCostCenter;
    }

    public String getEmployeeBranch() {
        return employeeBranch;
    }

    public void setEmployeeBranch(String employeeBranch) {
        this.employeeBranch = employeeBranch;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ReportStatus getVoucherStatus() {
        return voucherStatus;
    }

    public void setVoucherStatus(ReportStatus voucherStatus) {
        this.voucherStatus = voucherStatus;
    }

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LineItemsReport that = (LineItemsReport) o;
        return id == that.id && Objects.equals(companyCode, that.companyCode) && Objects.equals(fiscalYear, that.fiscalYear) && Objects.equals(documentIdentifier, that.documentIdentifier) && Objects.equals(reportCreatedDate, that.reportCreatedDate) && Objects.equals(reportTitle, that.reportTitle) && Objects.equals(expenseReportId, that.expenseReportId) && Objects.equals(expenseType, that.expenseType) && Objects.equals(expenseName, that.expenseName) && Objects.equals(expenseCreatedDate, that.expenseCreatedDate) && Objects.equals(sourceLocation, that.sourceLocation) && Objects.equals(destinationLocation, that.destinationLocation) && Objects.equals(employeeCode, that.employeeCode) && Objects.equals(employeeName, that.employeeName) && Objects.equals(employeeGrade, that.employeeGrade) && Objects.equals(submitDate, that.submitDate) && Objects.equals(voucherAmount, that.voucherAmount) && Objects.equals(invoiceAmount, that.invoiceAmount) && Objects.equals(currency, that.currency) && Objects.equals(employeeGlCode, that.employeeGlCode) && Objects.equals(glDocumentReference, that.glDocumentReference) && Objects.equals(paymentDate, that.paymentDate) && Objects.equals(paymentReference, that.paymentReference) && Objects.equals(startDate, that.startDate) && Objects.equals(endDate, that.endDate) && Objects.equals(employeeCostCenter, that.employeeCostCenter) && Objects.equals(employeeBranch, that.employeeBranch) && Objects.equals(description, that.description) && Objects.equals(voucherStatus, that.voucherStatus) && Objects.equals(entity, that.entity);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, companyCode, fiscalYear, documentIdentifier, reportCreatedDate, reportTitle, expenseReportId, expenseType, expenseName, expenseCreatedDate, sourceLocation, destinationLocation, employeeCode, employeeName, employeeGrade, submitDate, voucherAmount, invoiceAmount, currency, employeeGlCode, glDocumentReference, paymentDate, paymentReference, startDate, endDate, employeeCostCenter, employeeBranch, description, voucherStatus, entity);
    }
}
