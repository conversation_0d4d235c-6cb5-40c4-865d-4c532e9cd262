package in.taxgenie.pay_expense_pvv.entities;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Entity
public class MetadataLimitRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyCode;
    private double limitAmount;
    private IntervalMarker intervalMarker;
    private String branchCode;
    private String departmentCode;
    private String costCenterCode;
    private String employeeType;
    private String employeeGrade;
    private LocalDate startDate;
    private LocalDate endDate;
    @Column(length = 50)
    private String locationCategory;

    private int expenseCountLimit;

    //  Common
    private boolean isFrozen;
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    //  Mapping
    @ManyToOne(optional = false)
    @JoinColumn(name = "expenseMetadataId", insertable = false, updatable = false)
    private ExpenseMetadata expenseMetadata;
    private long expenseMetadataId;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public IntervalMarker getIntervalMarker() {
        return intervalMarker;
    }

    public void setIntervalMarker(IntervalMarker intervalMarker) {
        this.intervalMarker = intervalMarker;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public long getCreatingUserId() {
        return creatingUserId;
    }

    public void setCreatingUserId(long creatingUserId) {
        this.creatingUserId = creatingUserId;
    }

    public ZonedDateTime getCreatedTimestamp() {
        return createdTimestamp;
    }

    public void setCreatedTimestamp(ZonedDateTime createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }

    public Long getUpdatingUserId() {
        return updatingUserId;
    }

    public void setUpdatingUserId(Long updatingUserId) {
        this.updatingUserId = updatingUserId;
    }

    public ZonedDateTime getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    public void setUpdatedTimestamp(ZonedDateTime updatedTimestamp) {
        this.updatedTimestamp = updatedTimestamp;
    }

    public ExpenseMetadata getExpenseMetadata() {
        return expenseMetadata;
    }

    public void setExpenseMetadata(ExpenseMetadata expenseMetadata) {
        this.expenseMetadata = expenseMetadata;
    }

    public long getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(long expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public String getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(String locationCategory) {
        this.locationCategory = locationCategory;
    }

    public int getExpenseCountLimit() {
        return expenseCountLimit;
    }

    public void setExpenseCountLimit(int expenseCountLimit) {
        this.expenseCountLimit = expenseCountLimit;
    }
}
