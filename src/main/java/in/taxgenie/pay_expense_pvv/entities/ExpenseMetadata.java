package in.taxgenie.pay_expense_pvv.entities;

import javax.persistence.*;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
public class ExpenseMetadata {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyCode;
    private String description;
    private String expenseType;
    private String expenseTypePrefix;
    private String expenseGroup;
    private String expenseGroupPrefix;

    private boolean purposeRequired;
    private String applicableGender;
    private Integer limitDays;

    private int definitionsCount;
    private int subgroupsCount;
    private int rulesCount;

    private boolean isFrozen;
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    //  mappings
    @OneToMany(mappedBy = "expenseMetadata")
    private List<ApprovalDefinition> approvalDefinitions = new ArrayList<>();

    @OneToMany(mappedBy = "expenseMetadata")
    private List<ExpenseSubgroup> expenseSubgroups = new ArrayList<>();

    @OneToMany(mappedBy = "expenseMetadata")
    private List<ExpenseReport> expenseReports = new ArrayList<>();

    @OneToMany(mappedBy = "expenseMetadata")
    private List<MetadataLimitRule> limitRules = new ArrayList<>();

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getExpenseTypePrefix() {
        return expenseTypePrefix;
    }

    public void setExpenseTypePrefix(String expenseTypePrefix) {
        this.expenseTypePrefix = expenseTypePrefix;
    }

    public String getExpenseGroup() {
        return expenseGroup;
    }

    public void setExpenseGroup(String expenseGroup) {
        this.expenseGroup = expenseGroup;
    }

    public String getExpenseGroupPrefix() {
        return expenseGroupPrefix;
    }

    public void setExpenseGroupPrefix(String expenseGroupPrefix) {
        this.expenseGroupPrefix = expenseGroupPrefix;
    }

    public long getCreatingUserId() {
        return creatingUserId;
    }

    public void setCreatingUserId(long creatingUserId) {
        this.creatingUserId = creatingUserId;
    }

    public ZonedDateTime getCreatedTimestamp() {
        return createdTimestamp;
    }

    public void setCreatedTimestamp(ZonedDateTime createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }

    public Long getUpdatingUserId() {
        return updatingUserId;
    }

    public void setUpdatingUserId(Long updatingUserId) {
        this.updatingUserId = updatingUserId;
    }

    public ZonedDateTime getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    public void setUpdatedTimestamp(ZonedDateTime updatedTimestamp) {
        this.updatedTimestamp = updatedTimestamp;
    }

    public List<ApprovalDefinition> getApprovalDefinitions() {
        return approvalDefinitions;
    }

    public void setApprovalDefinitions(List<ApprovalDefinition> approvalDefinitions) {
        this.approvalDefinitions = approvalDefinitions;
    }

    public List<ExpenseSubgroup> getExpenseSubgroups() {
        return expenseSubgroups;
    }

    public void setExpenseSubgroups(List<ExpenseSubgroup> expenseSubgroups) {
        this.expenseSubgroups = expenseSubgroups;
    }

    public List<ExpenseReport> getExpenseReports() {
        return expenseReports;
    }

    public void setExpenseReports(List<ExpenseReport> expenseReports) {
        this.expenseReports = expenseReports;
    }

    public List<MetadataLimitRule> getLimitRules() {
        return limitRules;
    }

    public void setLimitRules(List<MetadataLimitRule> limitRules) {
        this.limitRules = limitRules;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public boolean isPurposeRequired() {
        return purposeRequired;
    }

    public void setPurposeRequired(boolean purposeRequired) {
        this.purposeRequired = purposeRequired;
    }

    public String getApplicableGender() {
        return applicableGender;
    }

    public void setApplicableGender(String applicableGender) {
        this.applicableGender = applicableGender;
    }

    public Integer getLimitDays() {
        return limitDays;
    }

    public void setLimitDays(Integer limitDays) {
        this.limitDays = limitDays;
    }

    public int getDefinitionsCount() {
        return definitionsCount;
    }

    public void setDefinitionsCount(int definitionsCount) {
        this.definitionsCount = definitionsCount;
    }

    public int getSubgroupsCount() {
        return subgroupsCount;
    }

    public void setSubgroupsCount(int subgroupsCount) {
        this.subgroupsCount = subgroupsCount;
    }

    public int getRulesCount() {
        return rulesCount;
    }

    public void setRulesCount(int rulesCount) {
        this.rulesCount = rulesCount;
    }
}
