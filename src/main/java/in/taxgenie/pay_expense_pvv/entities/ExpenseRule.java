package in.taxgenie.pay_expense_pvv.entities;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Entity
public class ExpenseRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyCode;
    private String branchCode;
    private String departmentCode;
    @Column(length = 50)
    private String locationCategory;
    private String employeeType;
    private String employeeGrade;
    private String costCenterCode;

    private boolean isInvoiceRequired;
    private Double invoiceRequiredThreshold;

    private LocalDate startDate;
    private LocalDate endDate;

    //  regular specific
    private Double standardDeductionRate;

    //  Other
    private Double limitAmount;
    private Double maximumAmount;
    private boolean canExceedLimit;

    private boolean isPerDiemAllowed;
    private Double perDiemAmount;

    //  Unit rate specific
    private boolean isUnitRateApplicable;
    private String unitOfMeasure;
    private Double unitRate;
    private String unitRateType;

    //  Common
    private boolean isFrozen;
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    //  Mappings
    @ManyToOne(optional = false)
    @JoinColumn(name = "expenseSubgroupId", insertable = false, updatable = false)
    private ExpenseSubgroup expenseSubgroup;
    private long expenseSubgroupId;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(String locationCategory) {
        this.locationCategory = locationCategory;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public boolean isInvoiceRequired() {
        return isInvoiceRequired;
    }

    public void setInvoiceRequired(boolean invoiceRequired) {
        isInvoiceRequired = invoiceRequired;
    }

    public Double getInvoiceRequiredThreshold() {
        return invoiceRequiredThreshold;
    }

    public void setInvoiceRequiredThreshold(Double invoiceRequiredThreshold) {
        this.invoiceRequiredThreshold = invoiceRequiredThreshold;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Double getStandardDeductionRate() {
        return standardDeductionRate;
    }

    public void setStandardDeductionRate(Double standardDeductionRate) {
        this.standardDeductionRate = standardDeductionRate;
    }

    public Double getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(Double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public Double getMaximumAmount() {
        return maximumAmount;
    }

    public void setMaximumAmount(Double maximumAmount) {
        this.maximumAmount = maximumAmount;
    }

    public boolean isCanExceedLimit() {
        return canExceedLimit;
    }

    public void setCanExceedLimit(boolean canExceedLimit) {
        this.canExceedLimit = canExceedLimit;
    }

    public boolean isPerDiemAllowed() {
        return isPerDiemAllowed;
    }

    public void setPerDiemAllowed(boolean perDiemAllowed) {
        isPerDiemAllowed = perDiemAllowed;
    }

    public Double getPerDiemAmount() {
        return perDiemAmount;
    }

    public void setPerDiemAmount(Double perDiemAmount) {
        this.perDiemAmount = perDiemAmount;
    }

    public boolean isUnitRateApplicable() {
        return isUnitRateApplicable;
    }

    public void setUnitRateApplicable(boolean unitRateApplicable) {
        isUnitRateApplicable = unitRateApplicable;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public Double getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(Double unitRate) {
        this.unitRate = unitRate;
    }

    public String getUnitRateType() {
        return unitRateType;
    }

    public void setUnitRateType(String unitRateType) {
        this.unitRateType = unitRateType;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public long getCreatingUserId() {
        return creatingUserId;
    }

    public void setCreatingUserId(long creatingUserId) {
        this.creatingUserId = creatingUserId;
    }

    public ZonedDateTime getCreatedTimestamp() {
        return createdTimestamp;
    }

    public void setCreatedTimestamp(ZonedDateTime createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }

    public Long getUpdatingUserId() {
        return updatingUserId;
    }

    public void setUpdatingUserId(Long updatingUserId) {
        this.updatingUserId = updatingUserId;
    }

    public ZonedDateTime getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    public void setUpdatedTimestamp(ZonedDateTime updatedTimestamp) {
        this.updatedTimestamp = updatedTimestamp;
    }

    public ExpenseSubgroup getExpenseSubgroup() {
        return expenseSubgroup;
    }

    public void setExpenseSubgroup(ExpenseSubgroup expenseSubgroup) {
        this.expenseSubgroup = expenseSubgroup;
    }

    public long getExpenseSubgroupId() {
        return expenseSubgroupId;
    }

    public void setExpenseSubgroupId(long expenseSubgroupId) {
        this.expenseSubgroupId = expenseSubgroupId;
    }
}
