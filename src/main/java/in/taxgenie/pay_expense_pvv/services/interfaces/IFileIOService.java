package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Expense;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface IFileIOService {
    void upload(MultipartFile file, String identifier, int index, Expense expense);
    ResponseEntity<Resource> getAttachment1(String identifier, long expenseId, IAuthContextViewModel auth);
    ResponseEntity<Resource> getAttachment2(String identifier, long expenseId, IAuthContextViewModel auth);
    ResponseEntity<Resource> getAttachment3(String identifier, long expenseId, IAuthContextViewModel auth);
    void delete(Expense expense);
    void delete(int index, Expense expense);
}
