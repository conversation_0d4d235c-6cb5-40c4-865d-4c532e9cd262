package in.taxgenie.pay_expense_pvv.services.interfaces;

import java.util.List;

public interface IEnvironmentDataProvider {
    String getCustomerId();
    String getCommunicationEngineUrl();
    String getProductId();
    String getSendBackTemplateId();
    String getSubmitToCreatorTemplateId();
    String getSubmitToApproverTemplateId();
    String getApprovedToCreatorTemplateId();
    String getApprovedToNextApproverTemplateId();
    String getPostedTemplateId();
    String getPaidTemplateId();
    String getSenderEmail();
    String getSendBackReminderTemplateId();
    String getApprovalReminderTemplateId();
    String getNewUserWelcomeTemplateId();
}
