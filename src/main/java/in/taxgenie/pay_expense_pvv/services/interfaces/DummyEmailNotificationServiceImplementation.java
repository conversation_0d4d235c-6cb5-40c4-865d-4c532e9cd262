package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.entities.ExpenseReport;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeCreationConfirmationViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.comeng.ApprovalPendingReminderEmailContainerViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

@Service
@Profile({"local", "tg-internal","abhfl-uat","dev", "uat", "test", "tg-internal-gcp"})
public class DummyEmailNotificationServiceImplementation implements IEmailNotificationService {
    private final Logger logger;

    public DummyEmailNotificationServiceImplementation() {
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void transmitSubmitNotification(ExpenseReport report) {
        this.logger.info("transmitSubmitNotification: Not a real implementation");
    }

    @Override
    public void transmitApprovedNotification(ExpenseReport report) {
        this.logger.info("transmitApprovedNotification: Not a real implementation");
    }

    @Override
    public void transmitPostedNotification(ExpenseReport report) {
        this.logger.info("transmitPostedNotification: Not a real implementation");
    }

    @Override
    public void transmitSendBackNotification(ExpenseReport report) {
        this.logger.info("transmitSendBackNotification: Not a real implementation");
    }

    @Override
    public void transmitPaidNotification(ExpenseReport report) {
        this.logger.info("transmitPaidNotification: Not a real implementation");
    }

    @Override
    public void transmitNewUserCreationNotification(EmployeeCreationConfirmationViewModel viewModel) {
        this.logger.info("transmitNewUserCreationNotification: Not a real implementation");
    }

    @Override
    public void transmitSentBackReminderNotification(ExpenseReport report) {
        this.logger.info("transmitSentBackReminderNotification: Not a real implementation");
    }

    @Override
    public void transmitApprovalReminderNotification(ApprovalPendingReminderEmailContainerViewModel viewModel) {
        this.logger.info("transmitApprovalReminderNotification: Not a real implementation");
    }
}
