package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ApproverPersonViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.EmployeeMasterLookupViewModel;

import java.util.List;

public interface IAuxiliaryService {
    EmployeeMasterLookupViewModel getEmployeeMasterLookup(IAuthContextViewModel auth);
    List<ApproverPersonViewModel> getApprovers(boolean includeEndOfService, IAuthContextViewModel auth);
}
