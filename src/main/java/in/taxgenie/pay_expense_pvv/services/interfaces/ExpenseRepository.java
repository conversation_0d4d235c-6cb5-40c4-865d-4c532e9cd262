package in.taxgenie.pay_expense_pvv.services.interfaces;

import java.time.LocalDate;
import java.util.List;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;


public interface ExpenseRepository {

	public List<Object[]> getByExpenseTypeForAllV2(long companyCode, String expenseTypeForSeletion,
			LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus, String entity);

	public List<Object[]> getByExpenseTypeV2(long companyCode, String expenseTypeForSeletion, LocalDate fromDate,
			LocalDate toDate, ReportStatus voucherStatus, String entity);
}
