package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.entities.ExpenseReport;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeCreationConfirmationViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.comeng.ApprovalPendingReminderEmailContainerViewModel;

public interface IEmailNotificationService {
    void transmitSubmitNotification(ExpenseReport report);
    void transmitApprovedNotification(ExpenseReport report);
    void transmitPostedNotification(ExpenseReport report);
    void transmitSendBackNotification(ExpenseReport report);
    void transmitPaidNotification(ExpenseReport report);
    void transmitNewUserCreationNotification(EmployeeCreationConfirmationViewModel viewModel);
    void transmitSentBackReminderNotification(ExpenseReport report);
    void transmitApprovalReminderNotification(ApprovalPendingReminderEmailContainerViewModel viewModel);
}
