package in.taxgenie.pay_expense_pvv.services.interfaces;

import java.util.List;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.BulkApproveContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportSendBackResultViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportViewModelV2;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ReportStateCreateViewModel;

public interface IExpenseReportApproverService {
    void approve(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);

    void approveBulk(BulkApproveContainerViewModel viewModel, IAuthContextViewModel auth);
    void reject(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);
    ExpenseReportSendBackResultViewModel approverSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);

    void checkerConsent(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);
    ExpenseReportSendBackResultViewModel checkerSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);

    List<ExpenseReportViewModel> getApproverQueue(IAuthContextViewModel auth);
    List<ExpenseReportViewModel> getCheckerQueue(IAuthContextViewModel auth);
    List<ExpenseReportViewModel> getAdminQueue(IAuthContextViewModel auth);
    ExpenseReportViewModelV2 getAdminQueueV2(IAuthContextViewModel auth, QueueFilterViewModel viewModel);
    List<ExpenseReportViewModel> getSimilarExpenses(long reportId, IAuthContextViewModel auth);

	ExpenseReportViewModelV2 getCheckerQueueV2(IAuthContextViewModel auth, QueueFilterViewModel viewModel);

	ExpenseReportViewModelV2 getApproverQueueV2(IAuthContextViewModel auth, QueueFilterViewModel viewModel);
}
