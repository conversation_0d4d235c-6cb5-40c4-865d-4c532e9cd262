package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseRuleCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseRuleUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseRuleViewModel;

import java.util.List;

public interface IExpenseRuleService {
    ExpenseRuleViewModel create(ExpenseRuleCreateViewModel viewModel, IAuthContextViewModel auth);
    ExpenseRuleViewModel update(long id, ExpenseRuleUpdateViewModel viewModel, IAuthContextViewModel auth);
    List<ExpenseRuleViewModel> getRulesBySubgroup(long subgroupId, IAuthContextViewModel auth);
    ExpenseRuleViewModel getRuleById(long id, IAuthContextViewModel auth);
}
