package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ReportLimitConsumptionViewModel;

import java.util.List;

public interface IExpenseService {
    ExpenseViewModel create(long reportId, long subgroupId, Long locationId, IAuthContextViewModel auth);
    ExpenseViewModel getById(long id, IAuthContextViewModel auth);
    void save(ExpenseUpdateViewModel viewModel, IAuthContextViewModel auth);
    void save(ExpenseUpdateViewModel viewModel, IAuthContextViewModel auth, boolean performValidations);
    void delete(long id, IAuthContextViewModel auth);
    List<ExpenseViewModel> getAllByReportId(long reportId, IAuthContextViewModel auth);
    List<ReportLimitConsumptionViewModel> getConsumption(long expenseId, IAuthContextViewModel auth);
}
