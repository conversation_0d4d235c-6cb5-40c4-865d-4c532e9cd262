package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseMetadataCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseMetadataUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseMetadataViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;

import java.util.List;

public interface IExpenseMetadataService {
    ExpenseMetadataViewModel create(ExpenseMetadataCreateViewModel viewModel, IAuthContextViewModel auth);
    ExpenseMetadataViewModel update(long id, ExpenseMetadataUpdateViewModel viewModel, IAuthContextViewModel auth);
    List<ExpenseMetadataViewModel> getAll(IAuthContextViewModel auth);
    ExpenseMetadataViewModel getById(long id, IAuthContextViewModel auth);
    List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(IAuthContextViewModel auth);

    //  exists
    boolean existsByTypeAndGroup(String type, String group, IAuthContextViewModel auth);
    boolean existsByPrefixesOfTypeAndGroup(String typePrefix, String groupPrefix, IAuthContextViewModel auth);
}
