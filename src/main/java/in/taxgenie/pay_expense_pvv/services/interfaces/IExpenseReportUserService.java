package in.taxgenie.pay_expense_pvv.services.interfaces;

import java.util.List;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportSubmitResultViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportViewModelV2;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;

public interface IExpenseReportUserService {
    ExpenseReportViewModel create(long metadataId, IAuthContextViewModel auth);
    ExpenseReportViewModel getById(long id, IAuthContextViewModel auth);
    void save(ExpenseReportUpdateViewModel viewModel, IAuthContextViewModel auth);
    ExpenseReportSubmitResultViewModel submit(long id, IAuthContextViewModel auth, long saveDate);
    void revoke(long id, IAuthContextViewModel auth);
    List<ExpenseReportViewModel> getQueue(IAuthContextViewModel auth);
	ExpenseReportViewModelV2 getQueueV2(IAuthContextViewModel auth, QueueFilterViewModel viewModel);
}
