package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ReportLimitConsumptionViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ReportStateViewModel;

import java.util.List;

public interface IExpenseReportCommonService {
    List<ReportStateViewModel> getStates(long reportId, IAuthContextViewModel auth);
    List<ReportLimitConsumptionViewModel> getConsumption(long reportId, IAuthContextViewModel auth);
}
