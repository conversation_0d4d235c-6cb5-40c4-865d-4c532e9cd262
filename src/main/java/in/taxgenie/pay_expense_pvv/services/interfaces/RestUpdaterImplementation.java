package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.exceptions.RestConsumptionFailedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.*;

@Service
public class RestUpdaterImplementation<T> implements IRestUpdater<T> {
    private final RestTemplate restTemplate;
    private final Logger logger;

    public RestUpdaterImplementation(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void update(HttpHeaders headers, String url, Class<T> classInfoModel, T body) {
        HttpEntity<T> httpEntity = new HttpEntity<>(body, headers);

        logger.trace("Making PUT request to: {}", url);
        this.restTemplate.exchange(url, HttpMethod.PUT, httpEntity, classInfoModel);
        logger.trace("PUT complete; returning");
    }
    @Override
    public T updateEntityWithResponse(HttpHeaders headers, String url, Class<T> classInfoModel, T body) {
        HttpEntity<T> httpEntity = new HttpEntity<>(body, headers);
        logger.trace("Making PUT request to: {}", url);

        try {
            ResponseEntity<T> response = this.restTemplate.exchange(url, HttpMethod.PUT, httpEntity, classInfoModel);

            if (response.getStatusCode().is2xxSuccessful()) {
                logger.trace("PUT complete; returning");
                return response.getBody(); // Optionally null-check if needed
            } else {
                logger.warn("Update email failed with status: {}", response.getStatusCode());
                throw new RestConsumptionFailedException("Failed to update entity. HTTP Status: " + response.getStatusCode());
            }

        } catch (HttpClientErrorException | HttpServerErrorException e) {
            logger.error("HTTP error during PUT email update: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RestConsumptionFailedException("HTTP error during PUT email update: " + e.getStatusCode());
        } catch (ResourceAccessException e) {
            logger.error("Resource access error during PUT: {}", e.getMessage(), e);
            throw new RestConsumptionFailedException("Connection error while updating email ");
        } catch (RestClientException e) {
            logger.error("REST client error during PUT", e);
            throw new RestConsumptionFailedException("Unexpected REST client error");
        }
    }
}
