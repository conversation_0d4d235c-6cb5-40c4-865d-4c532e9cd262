package in.taxgenie.pay_expense_pvv.services.interfaces;


import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.EmailUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ValidateEmailDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ValidateEmailViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.EmployeeDashboardViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.EmployeeStatisticsViewModel;

import java.time.LocalDate;

public interface IDashboardService {
    EmployeeDashboardViewModel getDashboard(IAuthContextViewModel auth);
    EmployeeDashboardViewModel getDashboard(long userId, String employeeEmail, IAuthContextViewModel auth);
    EmployeeDashboardViewModel getCreatorDashboard(String userId, String employeeEmail, IAuthContextViewModel auth);
    EmployeeStatisticsViewModel getEmployeeStatistics(LocalDate start, LocalDate end, IAuthContextViewModel auth);
    EmployeeStatisticsViewModel getEmployeeStatistics(LocalDate start, LocalDate end, long userId, IAuthContextViewModel auth);
    ValidateEmailViewModel getEmailValidatedExpenseReports(String employeeEmail, String employeeCode, IAuthContextViewModel auth);
    void updateUserEmailInSystem(EmailUpdateViewModel viewModel, IAuthContextViewModel auth);
}
