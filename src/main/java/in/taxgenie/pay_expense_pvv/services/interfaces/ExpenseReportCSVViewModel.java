package in.taxgenie.pay_expense_pvv.services.interfaces;

import java.time.LocalDate;
import java.time.ZonedDateTime;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.PaidStatus;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;

public interface ExpenseReportCSVViewModel {

	Long getCompanyCode();

	LocalDate getCreatedDate();

	ReportStatus getReportStatus();

	String getDocumentIdentifier();

	Double getReportClaimAmount();

	PaidStatus getPaidStatus();

	Double getTotalPaidAmount();

	LocalDate getGlPostingDate();

	Long getId();

	String getEmployeeBranch();

	LocalDate getStartDate();

	LocalDate getEndDate();

	String getValue03();

	String getEmployeeGrade();

	LocalDate getSubmitDate();

	LocalDate getPaymentDate();

	String getPaymentReference();

	String getExpenseType();

	String getReportTitle();

	String getFirstName();

	String getMiddleName();

	String getLastName();

	String getEmployeeCostCenter();

	String getEmployeeDepartment();

	ZonedDateTime getUpdatedTimestamp();

	ExpenseActionStatus getStatus();

	String getApproverEmployeeCode();

//	double getVoucherAmount();

//	String getCurrency();

//	long getDelay();

//	String getEmployeeCode();
//	String getEmployeeName();
//	String getMobileNumber();
//	String getLocation();
//	String getPaymentStatus();
//
//	long getExpenseMetadataId();
//
//	String getExpenseGroup();
//
//	String getLimitExceeded();
//
//	String getCurrentApproverName();
//
	String getCurrentApproverEmployeeCode();
//
//	boolean isContainsSentBack();
//
//	String getApproverCode();
//
//	String getApproverName();
//
	LocalDate getApprovedDate();
//
//	String getApproverLevel();
//
//	String getVoucherStatus();
//
//	String getPendingAt();
//
	Integer getSentBackTimes();
//
	String getSentBackByCode();
//
	LocalDate getSentBackOnDate();
//
//	String getSentBackOnLevel();
//
	String getSentBackBy();
//
	String getSentBackRemarks();
//
//
//
	Double getTotalExpenses();
//
//	String getEntity();

	String getCurrentApproverName();

}
