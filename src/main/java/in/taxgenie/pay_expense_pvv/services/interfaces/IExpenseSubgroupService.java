package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseSubgroupCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseSubgroupUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseSubgroupViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;

import java.util.List;

public interface IExpenseSubgroupService {
    ExpenseSubgroupViewModel create(ExpenseSubgroupCreateViewModel viewModel, IAuthContextViewModel auth);
    ExpenseSubgroupViewModel update(long id, ExpenseSubgroupUpdateViewModel viewModel, IAuthContextViewModel auth);
    List<ExpenseSubgroupViewModel> getAll(IAuthContextViewModel auth);
    List<ExpenseSubgroupViewModel> getAllByMetadata(long metadataId, IAuthContextViewModel auth);
    ExpenseSubgroupViewModel getById(long id, IAuthContextViewModel auth);
    List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(IAuthContextViewModel auth);
    List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(long metadataId, IAuthContextViewModel auth);

    //  exists
    boolean existsByExpenseCode(String expenseCode,long metadataId,  IAuthContextViewModel auth);
    boolean existsBySubgroup(String subgroup,long metadataId,  IAuthContextViewModel auth);
    boolean existsByPrefix(String prefix,long metadataId,  IAuthContextViewModel auth);
}
