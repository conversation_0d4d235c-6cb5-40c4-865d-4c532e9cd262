package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ErpGlPostingContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ErpInwardAcknowledgementContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ErpPaymentContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportErpViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeGlDetailUpdateContainerViewModel;

import java.util.List;

public interface IErpIntegrationService {
    List<ExpenseReportErpViewModel> getReportsForPosting(IAuthContextViewModel auth);
    void processTransmissionAcknowledgement(ErpInwardAcknowledgementContainerViewModel container, IAuthContextViewModel auth);
    void processGlPostingAcknowledgement(ErpGlPostingContainerViewModel container, IAuthContextViewModel auth);
    void processPayments(ErpPaymentContainerViewModel container, IAuthContextViewModel auth);
    void processEmployeeGlDetailUpdate(EmployeeGlDetailUpdateContainerViewModel container, IAuthContextViewModel auth);
}
