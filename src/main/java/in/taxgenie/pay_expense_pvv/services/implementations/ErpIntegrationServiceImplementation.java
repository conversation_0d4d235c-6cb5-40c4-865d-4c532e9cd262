package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseReportRepository;
import in.taxgenie.pay_expense_pvv.repositories.IPaymentRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmailNotificationService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IErpIntegrationService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeGlDetailUpdateContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeGlDetailUpdateViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ErpIntegrationServiceImplementation implements IErpIntegrationService {
    private final IExpenseReportRepository reportRepository;
    private final IPaymentRepository paymentAdviceRepository;
    private final IEmployeeMasterDataService employeeMasterDataService;
    private final IEmailNotificationService emailNotificationService;
    private final Logger logger;

    public ErpIntegrationServiceImplementation(
            IExpenseReportRepository reportRepository,
            IPaymentRepository paymentAdviceRepository,
            IEmployeeMasterDataService employeeMasterDataService,
            IEmailNotificationService emailNotificationService) {
        this.reportRepository = reportRepository;
        this.paymentAdviceRepository = paymentAdviceRepository;
        this.employeeMasterDataService = employeeMasterDataService;
        this.emailNotificationService = emailNotificationService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }


    @Override
    public List<ExpenseReportErpViewModel> getReportsForPosting(IAuthContextViewModel auth) {
        logger.trace("getReportsForPosting: Preparing all the accepted but pending (at ERP) reports");
        return reportRepository
                .getReportsForErpProcessing(auth.getCompanyCode(), ReportStatus.ACCEPTED)
                .stream()
                .filter(r -> {
                    boolean status = true;

                    for (Expense e : r.getExpenses()) {
                        if (StaticDataRegistry.isNullOrEmptyOrWhitespace(e.getExpenseSubgroup().getGlAccountCode())) {
                            logger.trace("getReportsForPosting: Excluding report id: {} because expense {} does not have GL code", r.getId(), e.getId());
                            status = false;
                        }
                    }

                    return status;
                })
                .map(r -> {
                    ExpenseReportErpViewModel viewModel = getReportErpViewModel(r);
                    viewModel.setLineItems(r.getExpenses().stream().map(this::getExpenseLineViewModel).collect(Collectors.toList()));
                    return viewModel;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void processTransmissionAcknowledgement(ErpInwardAcknowledgementContainerViewModel container, IAuthContextViewModel auth) {
        logger.trace("processTransmissionAcknowledgement: Processing inward acknowledgement from ERP");
        for (ErpInwardAcknowledgementLineViewModel line : container.getLines()) {
            Optional<ExpenseReport> reportOptional =
                    reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), line.getReportId());

            if (reportOptional.isPresent()) {
                logger.trace("processTransmissionAcknowledgement: Marking report: {} as acknowledged by the ERP", line.getReportId());
                ExpenseReport report = reportOptional.get();
                report.setAcknowledgedByErp(true);
                report.setErpAcknowledgementTimestamp(ZonedDateTime.now());

                reportRepository.saveAndFlush(report);
            }
        }
    }

    @Override
    public void processGlPostingAcknowledgement(ErpGlPostingContainerViewModel container, IAuthContextViewModel auth) {
        logger.trace("processGlPostingAcknowledgement: Processing GL posting at ERP");
        List<ReportStatus> reportStatus = new ArrayList<>();
        reportStatus.add(ReportStatus.PAID);
        reportStatus.add(ReportStatus.POSTED_TO_DESTINATION_SYSTEM);
        reportStatus.add(ReportStatus.DRAFT);

        for (ErpGlPostingLineViewModel line : container.getLines()) {
            Optional<ExpenseReport> reportOptional =
                    reportRepository.getReportsForErpPosting(auth.getCompanyCode(), line.getReportId(), reportStatus);

            if (reportOptional.isPresent()) {
                logger.trace("processGlPostingAcknowledgement: Marking report: {} as posted to GL at the ERP", line.getReportId());
                ExpenseReport report = reportOptional.get();
                report.setPostedToGl(true);
                report.setReportStatus(ReportStatus.POSTED_TO_DESTINATION_SYSTEM);
                report.setGlPostingDate(line.getPostingDate());
                report.setGlDocumentReference(line.getGlDocumentReference());

                reportRepository.saveAndFlush(report);

                emailNotificationService.transmitPostedNotification(report);
            }
        }
    }

    @Override
    public void processPayments(ErpPaymentContainerViewModel container, IAuthContextViewModel auth) {
        logger.trace("processPayments: Processing payment advices from ERP");
        boolean sendEmail = true;

        for (ErpPaymentLineViewModel line : container.getLines()) {
            Optional<ExpenseReport> reportOptional =
                    reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), line.getReportId());

            if (reportOptional.isPresent()) {
                logger.trace("processPayments: Payment advice added to report: {} for amount {} ", line.getReportId(), line.getPaidAmount());
                ExpenseReport report = reportOptional.get();

                Payment advice = new Payment();
                advice.setCompanyCode(auth.getCompanyCode());
                advice.setErpCompCode(line.getCompanyCode());
                advice.setExpenseReportId(line.getReportId());
                advice.setExpenseReport(report);
                advice.setPaymentDate(line.getPaymentDate());
                advice.setCreatedTimestamp(ZonedDateTime.now());
                advice.setRemarks(line.getRemarks());
                advice.setPaidAmount(line.getPaidAmount());
                advice.setTdsAmount(line.getTdsAmount());
                advice.setPaymentReference(line.getPaymentReference());

                if (line.isFullPayment()) {
                    logger.trace("processPayments: Report {} is fully paid", line.getReportId());
                    report.setPaidStatus(PaidStatus.FULLY_PAID);
                } else {
                    double paidAmount = report.getTotalPaidAmount();
                    double claimAmount = report.getReportClaimAmount();
                    double currentAmount = line.getPaidAmount();

                    if (Math.ceil(paidAmount + currentAmount) >= Math.ceil(claimAmount)) {
                        logger.trace("processPayments: Payment of report {} is now fully paid", line.getReportId());
                        report.setPaidStatus(PaidStatus.FULLY_PAID);
                    }

                    logger.trace("processPayments: Report {} is partially paid", line.getReportId());
                    report.setPaidStatus(PaidStatus.PARTIALLY_PAID);
                }

                Optional<Payment> paymentOptional = paymentAdviceRepository.findFirstByCompanyCodeAndExpenseReportIdOrderByCreatedTimestampDesc(auth.getCompanyCode(), line.getReportId());
                if(paymentOptional.isEmpty())   {
                    System.out.println(" In TRUE");
                    sendEmail = true;
                } else {
                    if(paymentOptional.get().getPaymentReference().equalsIgnoreCase(line.getPaymentReference()) &&
                            paymentOptional.get().getPaidAmount() == line.getPaidAmount() ) {
                        System.out.println(" FALSE");
                        sendEmail = false;
                    }
                }

                report.setTotalPaidAmount(report.getTotalPaidAmount() + line.getPaidAmount());
                report.setTotalTdsAmount(report.getTotalTdsAmount() + line.getTdsAmount());
                report.setReportStatus(ReportStatus.PAID);
                report.setPaymentDate(line.getPaymentDate());

                if(sendEmail) {
                    System.out.println(" IN SEND EMAiL");
                    paymentAdviceRepository.saveAndFlush(advice);
                    reportRepository.saveAndFlush(report);
                    emailNotificationService.transmitPaidNotification(report);
                }

            }
        }
    }

    @Override
    @Transactional
    public void processEmployeeGlDetailUpdate(EmployeeGlDetailUpdateContainerViewModel container, IAuthContextViewModel auth) {
        long start = System.nanoTime();
        // stable lock order: sort by employee code
        container.getList().sort(
                Comparator.comparing(EmployeeGlDetailUpdateViewModel::getEmployeeSystemIdCode)
        );

        for (EmployeeGlDetailUpdateViewModel vm : container.getList()) {
            reportRepository.bulkUpdateGlCodes(
                    auth.getCompanyCode(),
                    vm.getEmployeeSystemIdCode(),
                    vm.getGlVendorCode(),
                    vm.getProfitCenterCode(),
                    vm.getCostCenterCode(),
                    vm.getSegmentCode(),
                    vm.isModified());
        }
        long durationMs = (System.nanoTime() - start) / 1_000_000;
        logger.info("bulkUpdateGlCodes loop: processed {} items in {} ms",
                container.getList().size(), durationMs);
        employeeMasterDataService.updateEmployeeGlDetails(container, auth);
    }

    private ExpenseReportErpViewModel getReportErpViewModel(ExpenseReport report) {
        ExpenseReportErpViewModel viewModel = new ExpenseReportErpViewModel();
        viewModel.setDocumentIdentifier(report.getDocumentIdentifier());
        viewModel.setCompanyCode(report.getCompanyCode());
        viewModel.setEmployeeGlMainAccountCode(report.getEmployeeGlMainAccountCode());
        viewModel.setEmployeeGlSubAccountCode(report.getEmployeeGlSubAccountCode());
        viewModel.setReportCgstAmount(report.getReportCgstAmount());
        viewModel.setReportClaimAmount(report.getReportClaimAmount());
        viewModel.setReportSgstAmount(report.getReportSgstAmount());
        viewModel.setReportIgstAmount(report.getReportIgstAmount());
        viewModel.setReportTaxableAmount(report.getReportTaxableAmount());
        viewModel.setReportId(report.getId());
        viewModel.setEmployeeIdCode(report.getEmployeeCode());
        viewModel.setEmployeeBranch(report.getEmployeeBranch());
        viewModel.setEmployeeCostCenter(report.getEmployeeCostCenter());
        viewModel.setEmployeeDepartment(report.getEmployeeDepartment());
        viewModel.setEmployeeType(report.getEmployeeType());
        viewModel.setEmployeeGrade(report.getEmployeeGrade());
        viewModel.setEmployeeProfitCenter(report.getEmployeeProfitCenter());
        viewModel.setValue03(report.getValue03());
        viewModel.setDimension01(report.getDimension01());
        viewModel.setDimension02(report.getDimension02());
        viewModel.setDimension03(report.getDimension03());
        viewModel.setDimension04(report.getDimension04());
        viewModel.setDimension05(report.getDimension05());
        viewModel.setDimension06(report.getDimension06());
        viewModel.setDimension07(report.getDimension07());
        viewModel.setDimension08(report.getDimension08());
        viewModel.setDimension09(report.getDimension09());
        viewModel.setDimension10(report.getDimension10());

        return viewModel;
    }

    private ExpenseLineErpViewModel getExpenseLineViewModel(Expense expense) {
        ExpenseLineErpViewModel viewModel = new ExpenseLineErpViewModel();
        viewModel.setExpenseId(expense.getId());
        viewModel.setCgstAmount(expense.getCgstAmount());
        viewModel.setExpenseDate(expense.getExpenseDate());
        viewModel.setExpenseType(expense.getExpenseReport().getExpenseMetadata().getExpenseType());
        viewModel.setExpenseGroup(expense.getExpenseReport().getExpenseMetadata().getExpenseGroup());
        viewModel.setExpenseSubgroup(expense.getExpenseSubgroup().getExpenseSubgroup());
        viewModel.setReportId(expense.getExpenseReportId());
        viewModel.setGlAccountCode(expense.getExpenseSubgroup().getGlAccountCode());
        viewModel.setGstin(expense.getGstin());
        viewModel.setClaimAmount(expense.getClaimAmount());
        viewModel.setIgstRate(expense.getIgstRate());
        viewModel.setIgstAmount(expense.getIgstAmount());
        viewModel.setCgstRate(expense.getCgstRate());
        viewModel.setSgstRate(expense.getSgstRate());
        viewModel.setSgstAmount(expense.getSgstAmount());
        viewModel.setTaxableAmount(expense.getTaxableAmount());

        return viewModel;
    }
}
