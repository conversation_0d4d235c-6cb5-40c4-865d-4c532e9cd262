package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.services.interfaces.IPdfSanitizerService;
import in.taxgenie.pay_expense_pvv.viewmodels.pdfHandling.SanitizationResult;
import org.springframework.stereotype.Service;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDDocumentNameDictionary;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.interactive.action.PDPageAdditionalActions;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDDocumentOutline;


import java.io.ByteArrayOutputStream;
import java.io.IOException;


@Service
public class PdfSanitizerServiceImplementation implements IPdfSanitizerService {

    /**
     * Strips out ALL embedded JavaScript (document-level and page-level actions,
     * outlines) from the given PDF bytes and returns the cleaned PDF.
     */
    public byte[] sanitize(byte[] inputPdf) throws IOException {
        // loadPDF(byte[]) exists in PDFBox 3.x  [oai_citation:0?Javadoc](https://javadoc.io/static/org.apache.pdfbox/pdfbox/3.0.0/org/apache/pdfbox/Loader.html)
        try (PDDocument doc = Loader.loadPDF(inputPdf)) {
            PDDocumentCatalog catalog = doc.getDocumentCatalog();

            // 1) document-level JS name tree
            PDDocumentNameDictionary names = catalog.getNames();
            if (names != null && names.getJavaScript() != null) {
                // clear named JS entries
                names.setJavascript(null);  // note: lowercase 'j'  [oai_citation:1?Javadoc](https://javadoc.io/static/org.apache.pdfbox/pdfbox/3.0.0-alpha3/org/apache/pdfbox/pdmodel/PDDocumentNameDictionary.html?utm_source=chatgpt.com)
            }
            // clear any OpenAction
            catalog.setOpenAction(null);

            // 2) page-level additional actions
            for (PDPage page : doc.getPages()) {
                PDPageAdditionalActions actions = page.getActions();
                if (actions != null) {
                    actions.getCOSObject().clear();
                }
            }

            // 3) any JS in outlines/bookmarks
            PDDocumentOutline outline = catalog.getDocumentOutline();
            if (outline != null) {
                outline.getCOSObject().removeItem(COSName.AA);
            }

            // write out the cleaned PDF
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                doc.save(baos);
                return baos.toByteArray();
            }
        }
    }

    @Override
    public SanitizationResult sanitizeWithReport(byte[] inputPdf) throws IOException {
        boolean foundScripts = false;

        try (PDDocument doc = Loader.loadPDF(inputPdf)) {
            PDDocumentCatalog catalog = doc.getDocumentCatalog();

            // document-level JS
            PDDocumentNameDictionary names = catalog.getNames();
            if (names != null && names.getJavaScript() != null) {
                foundScripts = true;
                names.setJavascript(null);
            }
            if (catalog.getOpenAction() != null) {
                foundScripts = true;
                catalog.setOpenAction(null);
            }

            // page-level AA actions
            for (PDPage page : doc.getPages()) {
                PDPageAdditionalActions aa = page.getActions();
                if (aa != null && aa.getCOSObject().size() > 0) {
                    foundScripts = true;
                    aa.getCOSObject().clear();
                }
            }

            // outline JS
            PDDocumentOutline outline = catalog.getDocumentOutline();
            if (outline != null && outline.getCOSObject().containsKey(COSName.AA)) {
                foundScripts = true;
                outline.getCOSObject().removeItem(COSName.AA);
            }

            // save cleaned PDF
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            doc.save(baos);
            return new SanitizationResult(baos.toByteArray(), foundScripts);
        }
    }
}
