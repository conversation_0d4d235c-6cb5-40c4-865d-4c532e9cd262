package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ExpenseRule;
import in.taxgenie.pay_expense_pvv.entities.ExpenseSubgroup;
import in.taxgenie.pay_expense_pvv.entities.Location;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.MasterRecordNotFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseRuleRepository;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseSubgroupRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseRuleService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseRuleCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseRuleUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseRuleViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ExpenseRuleServiceImplementation implements IExpenseRuleService {
    private final IExpenseRuleRepository repository;
    private final IExpenseSubgroupRepository headerRepository;
    private final Logger logger;
    private Location location;

    public ExpenseRuleServiceImplementation(
            IExpenseRuleRepository repository,
            IExpenseSubgroupRepository headerRepository
    ) {
        this.repository = repository;
        this.headerRepository = headerRepository;
        this.logger = LoggerFactory.getLogger(ExpenseRuleServiceImplementation.class);
    }

    @Transactional
    @Override
    public ExpenseRuleViewModel create(ExpenseRuleCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Ensuring the existence of Expense Subgroup record for this request");
        ExpenseSubgroup headerEntity =
                headerRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseSubgroupId())
                        .orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", ExpenseSubgroup.class, viewModel.getExpenseSubgroupId(), auth.getCompanyCode())));

        logger.trace("create: Conducting preliminary screening");
        processPreliminaryScreeningForCreate(viewModel, auth, headerEntity);

        logger.trace("create: Validating the rule");
        validateRule(viewModel);

        logger.trace("create: Creating a new entity");
        ExpenseRule entity = new ExpenseRule();
        BeanUtils.copyProperties(viewModel, entity);


        logger.trace("create: Setting audit fields");
        entity.setCreatingUserId(auth.getUserId());
        entity.setCreatedTimestamp(ZonedDateTime.now());
        entity.setCompanyCode(headerEntity.getCompanyCode());

        headerEntity.setRulesCount(headerEntity.getRulesCount() + StaticDataRegistry.COUNT_INCREMENT_FACTOR);

        logger.trace("create: Mapping both Rule and Subgroup");
        entity.setExpenseSubgroup(headerEntity);
        headerEntity.getRules().add(entity);


        logger.trace("create: Saving entity and metadata");
        ExpenseRule savedEntity = repository.saveAndFlush(entity);
        headerRepository.saveAndFlush(headerEntity);
        logger.trace("create: Save successful");

        return getViewModel(savedEntity);
    }

    private void processPreliminaryScreeningForCreate(ExpenseRuleCreateViewModel viewModel, IAuthContextViewModel auth, ExpenseSubgroup headerEntity) {
        logger.trace("processPreliminaryScreeningForCreate: Checking if the location is required");
        if (headerEntity.isLocationRequired() && (viewModel.getLocationCategory() == null || !StaticDataRegistry.locationCategories.contains(viewModel.getLocationCategory()))) {
            logger.trace("processPreliminaryScreeningForCreate: This rule doesn't define a valid location");
            throw new DomainInvariantException("Valid location category is required for this rule");
        }

        logger.trace("processPreliminaryScreeningForCreate: Checking if standard deduction is applicable");
        if (headerEntity.isStandardDeductionApplicable() && viewModel.getStandardDeductionRate() == null) {
            logger.info("processPreliminaryScreeningForCreate: This rule doesn't define standard deduction rate");
            throw new DomainInvariantException("Standard deduction rate is required for this rule");
        }

        logger.trace("processPreliminaryScreeningForCreate: Setting the location field from location id");
        if (viewModel.getLocationCategory() != null && !StaticDataRegistry.locationCategories.contains(viewModel.getLocationCategory())) {
            throw new MasterRecordNotFoundException(String.format("Could not find location category %s", viewModel.getLocationCategory()));
        }

        logger.trace("processPreliminaryScreeningForCreate: Checking if rule already exists");
        if (isDuplicateRule(viewModel, headerEntity.getId(), auth.getCompanyCode())) {
            throw new DomainInvariantException("Duplicate rule; already exists");
        }


        logger.trace("processPreliminaryScreeningForCreate: Found valid");
    }

    private void processPreliminaryScreeningForUpdate(ExpenseRuleUpdateViewModel viewModel, IAuthContextViewModel auth, ExpenseSubgroup headerEntity) {
        logger.trace("processPreliminaryScreeningForUpdate: Checking if the location is required and defined");
        if (headerEntity.isLocationRequired() && (viewModel.getLocationCategory() == null || !StaticDataRegistry.locationCategories.contains(viewModel.getLocationCategory()))) {
            logger.trace("processPreliminaryScreeningForUpdate: This rule doesn't define a valid location");
            throw new DomainInvariantException("Valid location category is required for this rule");
        }

        logger.trace("processPreliminaryScreeningForUpdate: Checking if standard deduction rate is applicable");
        if (headerEntity.isStandardDeductionApplicable() && viewModel.getStandardDeductionRate() == null) {
            logger.trace("processPreliminaryScreeningForUpdate: This rule doesn't define standard deduction rate");
            throw new DomainInvariantException("Standard deduction rate is required for this rule");
        }

        logger.trace("processPreliminaryScreeningForUpdate: Checking if rule already exists");
        if (isDuplicateRule(viewModel, headerEntity.getId(), auth.getCompanyCode())) {
            throw new DomainInvariantException("Duplicate rule; already exists");
        }

        logger.trace("processPreliminaryScreeningForUpdate: Found valid");
    }

    @Override
    public ExpenseRuleViewModel update(long id, ExpenseRuleUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("update: Matching id with that of the view model");
        if (id != viewModel.getId()) {
            throw new DomainInvariantException("Passed id doesn't match with that of the view model");
        }

        logger.trace("update: Finding the source record");
        ExpenseRule entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", ExpenseRule.class, viewModel.getId(), auth.getCompanyCode())));

        processPreliminaryScreeningForUpdate(viewModel, auth, entity.getExpenseSubgroup());

        logger.trace("update: Validating the rule");
        validateRule(viewModel);

        logger.trace("update: Updating the source record");
        BeanUtils.copyProperties(viewModel, entity, "expenseMetadataId");

        logger.trace("update: Setting the audit fields");
        entity.setUpdatingUserId(auth.getUserId());
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("update: Saving the update");
        ExpenseRule savedEntity = repository.saveAndFlush(entity);
        logger.trace("update: Save successful");

        return getViewModel(savedEntity);
    }

    @Override
    public List<ExpenseRuleViewModel> getRulesBySubgroup(long subgroupId, IAuthContextViewModel auth) {
        logger.trace("getRulesBySubgroup: Returning list of view models");
        return repository
                .findByCompanyCodeAndExpenseSubgroupId(auth.getCompanyCode(), subgroupId)
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public ExpenseRuleViewModel getRuleById(long id, IAuthContextViewModel auth) {
        logger.trace("getRuleById: Finding the source record");
        ExpenseRule entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", ExpenseRule.class, id, auth.getCompanyCode())));

        return getViewModel(entity);
    }

    private boolean isDuplicateRule(ExpenseRuleUpdateViewModel viewModel, long expenseSubgroupId, long companyId) {
        return repository
                .findByCompanyCodeAndExpenseSubgroupId(companyId, expenseSubgroupId)
                .stream()
                .anyMatch(r -> doesRuleMatch(viewModel, r));
    }

    private boolean isDuplicateRule(ExpenseRuleCreateViewModel viewModel, long expenseSubgroupId, long companyId) {
        return repository
                .findByCompanyCodeAndExpenseSubgroupId(companyId, expenseSubgroupId)
                .stream()
                .anyMatch(r -> doesRuleMatch(viewModel, r));
    }

    private boolean doesRuleMatch(ExpenseRuleCreateViewModel viewModel, ExpenseRule rule) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(viewModel.getBranchCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(viewModel.getDepartmentCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(viewModel.getCostCenterCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(viewModel.getEmployeeType());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(viewModel.getEmployeeGrade());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
            result = rule.getLocationCategory().equals(viewModel.getLocationCategory());
            if (!result) return false;
        }

        return result;
    }

    private boolean doesRuleMatch(ExpenseRuleUpdateViewModel viewModel, ExpenseRule rule) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(viewModel.getBranchCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(viewModel.getDepartmentCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(viewModel.getCostCenterCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(viewModel.getEmployeeType());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(viewModel.getEmployeeGrade());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
            result = rule.getLocationCategory().equals(viewModel.getLocationCategory());
            if (!result) return false;
        }

        return result ? viewModel.getId() != rule.getId() : result;
    }

    private ExpenseRuleViewModel getViewModel(ExpenseRule entity) {
        logger.trace("getViewModel: Preparing view model");
        ExpenseRuleViewModel returnViewModel = new ExpenseRuleViewModel();

        logger.trace("getViewModel: Copying fields to view model");
        BeanUtils.copyProperties(entity, returnViewModel);

        returnViewModel.setExpenseMetadataMarker(StaticDataRegistry.getExpenseMetadataMarkerForRule(entity));

        logger.trace("getViewModel: Returning the view model");
        return returnViewModel;
    }

    private void validateRule(ExpenseRuleCreateViewModel viewModel) {
        if (viewModel.isInvoiceRequired() && viewModel.getInvoiceRequiredThreshold() == null) {
            throw new DomainInvariantException("Rule doesn't provide invoice required threshold");
        }

        if (viewModel.getStartDate().isAfter(viewModel.getEndDate())) {
            throw new DomainInvariantException("Rule's start and end dates are invalid");
        }

        if (viewModel.getMaximumAmount() < viewModel.getLimitAmount()) {
            throw new DomainInvariantException("Rule's maximum amount should be greater or equal to limit amount");
        }

        if (viewModel.isPerDiemAllowed() && viewModel.getPerDiemAmount() == null) {
            throw new DomainInvariantException("Rule doesn't provide per-diem amount");
        }

        if (viewModel.isUnitRateApplicable() && viewModel.getUnitRate() == null) {
            throw new DomainInvariantException("Rule doesn't provide unit rate");
        }

        if (viewModel.isUnitRateApplicable() && StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getUnitOfMeasure())) {
            throw new DomainInvariantException("Rule doesn't provide unit of measure");
        }

        if (viewModel.isUnitRateApplicable() && StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getUnitRateType())) {
            throw new DomainInvariantException("Rule doesn't provide unit rate type");
        }
    }

    private void validateRule(ExpenseRuleUpdateViewModel viewModel) {
        if (viewModel.isInvoiceRequired() && viewModel.getInvoiceRequiredThreshold() == null) {
            throw new DomainInvariantException("Rule doesn't provide invoice required threshold");
        }

        if (viewModel.getStartDate().isAfter(viewModel.getEndDate())) {
            throw new DomainInvariantException("Rule's start and end dates are invalid");
        }

        if (viewModel.getMaximumAmount() < viewModel.getLimitAmount()) {
            throw new DomainInvariantException("Rule's maximum amount should be greater or equal to limit amount");
        }

        if (viewModel.isPerDiemAllowed() && viewModel.getPerDiemAmount() == null) {
            throw new DomainInvariantException("Rule doesn't provide per-diem amount");
        }

        if (viewModel.isUnitRateApplicable() && viewModel.getUnitRate() == null) {
            throw new DomainInvariantException("Rule doesn't provide unit rate");
        }

        if (viewModel.isUnitRateApplicable() && StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getUnitOfMeasure())) {
            throw new DomainInvariantException("Rule doesn't provide unit of measure");
        }

        if (viewModel.isUnitRateApplicable() && StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getUnitRateType())) {
            throw new DomainInvariantException("Rule doesn't provide unit rate type");
        }
    }
}
