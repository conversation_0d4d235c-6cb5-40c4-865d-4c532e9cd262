package in.taxgenie.pay_expense_pvv.services.implementations;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.ExpenseReport;
import in.taxgenie.pay_expense_pvv.entities.ReportState;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseReportRepository;
import in.taxgenie.pay_expense_pvv.repositories.IReportStateRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmailNotificationService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEnvironmentDataProvider;
import in.taxgenie.pay_expense_pvv.services.interfaces.IRestCreator;
import in.taxgenie.pay_expense_pvv.utils.IEmailUtil;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeCreationConfirmationViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.comeng.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Service
@Profile({"ses-gcp"})
public class EmailNotificationServiceSASImplementation implements IEmailNotificationService {
    private final IExpenseReportRepository reportRepository;
    private final IReportStateRepository stateRepository;
    private final IRestCreator<ReportEmailWrapperViewModel> reportEmailCrator;
    private final IRestCreator<ApprovalEmailWrapperViewModel> approvalRestCreator;
    private final IRestCreator<AcknowledgementEmailWrapperViewModel> acknowledgementRestCreator;
    private final IEnvironmentDataProvider environmentDataProvider;
    private final IEmailUtil emailUtil;
    private final Logger logger;


    public EmailNotificationServiceSASImplementation(
            IExpenseReportRepository reportRepository,
            IReportStateRepository stateRepository,
            IRestCreator<ReportEmailWrapperViewModel> reportEmailCrator,
            IRestCreator<ApprovalEmailWrapperViewModel> approvalRestCreator,
            IRestCreator<AcknowledgementEmailWrapperViewModel> acknowledgementRestCreator,
            IEnvironmentDataProvider environmentDataProvider,
            IEmailUtil emailUtil) {
        this.reportRepository = reportRepository;
        this.stateRepository = stateRepository;
        this.reportEmailCrator = reportEmailCrator;
        this.approvalRestCreator = approvalRestCreator;
        this.acknowledgementRestCreator = acknowledgementRestCreator;
        this.environmentDataProvider = environmentDataProvider;
        this.emailUtil = emailUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void transmitSubmitNotification(ExpenseReport report) {
        ReportState firstState = getFirstState(report);

        String approverName = String.format("%s %s", firstState.getApproverFirstName(), firstState.getApproverLastName());
        String approverEmail = firstState.getApprover();
        String creatorEmail = report.getEmployeeEmail();

        ReportEmailWrapperViewModel wrapperViewModelForCreator = getEmailWrapper(report, approverName, "--NA--", creatorEmail, "--NA--", environmentDataProvider.getSubmitToCreatorTemplateId());
        ReportEmailWrapperViewModel wrapperViewModelForApprover = getEmailWrapper(report, approverName, "--NA--", approverEmail, "--NA--", environmentDataProvider.getSubmitToApproverTemplateId());

        HttpHeaders headers = new HttpHeaders();

        String url = StaticDataRegistry.getUrl(
                environmentDataProvider.getCommunicationEngineUrl(),
                "/",
                "publish"
        );

        reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModelForCreator));
        reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModelForApprover));
    }


    @Override
    public void transmitApprovedNotification(ExpenseReport report) {
        Optional<ReportState> latestState = getLatestState(report);
        Optional<ReportState> previousState = getPreviousState(report);

        String previousApproverName = previousState.isPresent() ? String.format("%s %s", previousState.get().getApproverFirstName(), previousState.get().getApproverLastName()) : "--NA--";
        String nextApproverName = latestState.isPresent() ? String.format("%s %s", latestState.get().getApproverFirstName(), latestState.get().getApproverLastName()) : "--NA--";
        String creatorEmail = report.getEmployeeEmail();
        String nextApproverEmail = latestState.map(ReportState::getApprover).orElse(null);

        ReportEmailWrapperViewModel wrapperViewModelForCreator = getEmailWrapper(report, nextApproverName, previousApproverName, creatorEmail, "--NA--", environmentDataProvider.getApprovedToCreatorTemplateId());


        HttpHeaders headers = new HttpHeaders();

        String url = StaticDataRegistry.getUrl(
                environmentDataProvider.getCommunicationEngineUrl(),
                "/",
                "publish"
        );

        reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModelForCreator));

        if (latestState.isPresent()) {
            ReportEmailWrapperViewModel wrapperViewModelForApprover = getEmailWrapper(report, nextApproverName, previousApproverName, nextApproverEmail, "--NA--", environmentDataProvider.getApprovedToNextApproverTemplateId());
            reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModelForApprover));
        }
    }

    @Override
    public void transmitPostedNotification(ExpenseReport report) {
        Optional<ReportState> previousState = getPreviousState(report);
        String previousApproverName = previousState.isPresent() ? String.format("%s %s", previousState.get().getApproverFirstName(), previousState.get().getApproverLastName()) : "--NA--";
        String creatorEmail = report.getEmployeeEmail();

        ReportEmailWrapperViewModel wrapperViewModel = getEmailWrapper(report, "--NA--", previousApproverName, creatorEmail, "--NA--", environmentDataProvider.getPostedTemplateId());

        HttpHeaders headers = new HttpHeaders();

        String url = StaticDataRegistry.getUrl(
                environmentDataProvider.getCommunicationEngineUrl(),
                "/",
                "publish"
        );

        reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModel));
    }

    @Override
    public void transmitSendBackNotification(ExpenseReport report) {
        String creatorEmail = report.getEmployeeEmail();

        Optional<ReportState> previousState = getPreviousState(report);
        String previousApproverName = previousState.isPresent() ? String.format("%s %s", previousState.get().getApproverFirstName(), previousState.get().getApproverLastName()) : "--NA--";

        Optional<ReportState> currentState = getCurrentStateForSentBack(report);
        String currentApproverName = currentState.isPresent() ? String.format("%s %s", currentState.get().getApproverFirstName(), currentState.get().getApproverLastName()) : "--NA--";
        String sentBackRemarks = currentState.isPresent() ? currentState.get().getRemarks() : "--NA--";

        ReportEmailWrapperViewModel wrapperViewModel = getEmailWrapper(report, currentApproverName, previousApproverName, creatorEmail, sentBackRemarks, environmentDataProvider.getSendBackTemplateId());

        HttpHeaders headers = new HttpHeaders();

        String url = StaticDataRegistry.getUrl(
                environmentDataProvider.getCommunicationEngineUrl(),
                "/",
                "publish"
        );

        reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModel));
    }

    @Override
    public void transmitPaidNotification(ExpenseReport report) {
        Optional<ReportState> previousState = getPreviousState(report);
        String previousApproverName = previousState.isPresent() ? String.format("%s %s", previousState.get().getApproverFirstName(), previousState.get().getApproverLastName()) : "--NA--";
        String creatorEmail = report.getEmployeeEmail();

        ReportEmailWrapperViewModel wrapperViewModel = getEmailWrapper(report, "--NA--", previousApproverName, creatorEmail, "--NA--", environmentDataProvider.getPaidTemplateId());

        HttpHeaders headers = new HttpHeaders();

        String url = StaticDataRegistry.getUrl(
                environmentDataProvider.getCommunicationEngineUrl(),
                "/",
                "publish"
        );

        reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModel));
    }

    @Override
    public void transmitNewUserCreationNotification(EmployeeCreationConfirmationViewModel viewModel) {
        ReportEmailWrapperViewModel wrapperViewModel = getEmailWrapper(viewModel, environmentDataProvider.getNewUserWelcomeTemplateId());

        HttpHeaders headers = new HttpHeaders();

        String url = StaticDataRegistry.getUrl(
                environmentDataProvider.getCommunicationEngineUrl(),
                "/",
                "publish"
        );

        reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModel));
    }

    @Override
    public void transmitSentBackReminderNotification(ExpenseReport report) {
        Optional<ReportState> previousState = getPreviousState(report);
        String previousApproverName = previousState.isPresent() ? String.format("%s %s", previousState.get().getApproverFirstName(), previousState.get().getApproverLastName()) : "--NA--";
        String creatorEmail = report.getEmployeeEmail();
        String sentBackRemarks = previousState.isPresent() ? previousState.get().getRemarks() : "--NA--";

        ReportEmailWrapperViewModel wrapperViewModel = getEmailWrapper(report, "--NA--", previousApproverName, creatorEmail, sentBackRemarks, environmentDataProvider.getSendBackReminderTemplateId());

        HttpHeaders headers = new HttpHeaders();

        String url = StaticDataRegistry.getUrl(
                environmentDataProvider.getCommunicationEngineUrl(),
                "/",
                "publish"
        );

        reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModel));
    }

    @Override
    public void transmitApprovalReminderNotification(ApprovalPendingReminderEmailContainerViewModel viewModel) {
        ReportEmailWrapperViewModel wrapperViewModel = getEmailWrapper(viewModel.getApproverName(), viewModel.getApproverEmail(), viewModel.getPendingReports(), environmentDataProvider.getApprovalReminderTemplateId());

        HttpHeaders headers = new HttpHeaders();

        String url = StaticDataRegistry.getUrl(
                environmentDataProvider.getCommunicationEngineUrl(),
                "/",
                "publish"
        );

        reportEmailCrator.exchange(headers, url, emailUtil.convertToFormData(wrapperViewModel));
    }

    private Optional<ReportState> getLatestState(ExpenseReport report) {
        return report
                .getReportStates()
                .stream()
                .filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED).min(Comparator.comparing(ReportState::getLevel));
    }

    private ReportState getFirstState(ExpenseReport report) {
        return report.getReportStates().get(0);
    }

    private Optional<ReportState> getPreviousState(ExpenseReport report) {
        return report.getReportStates()
                .stream()
                .filter(r -> r.getStatus() == ExpenseActionStatus.APPROVED)
                .max(Comparator.comparing(ReportState::getLevel));
    }

    private Optional<ReportState> getCurrentStateForSentBack(ExpenseReport report) {
        return report.getReportStates()
                .stream()
                .filter(r -> r.getStatus() == ExpenseActionStatus.SENT_BACK)
                .max(Comparator.comparing(ReportState::getUpdatedTimestamp));
    }

    private ReportEmailWrapperViewModel getEmailWrapper(ExpenseReport report, String currentApproverName, String lastApprover, String recipientEmail, String sentBackRemarks, String templateId) {
        ReportEmailViewModel viewModel = new ReportEmailViewModel();
        viewModel.setCurrentApprover(currentApproverName);
        viewModel.setEmployeeName(String.format("%s %s", report.getFirstName(), report.getLastName()));
        viewModel.setDocumentIdentifier(report.getDocumentIdentifier());
        switch (report.getValue03().toUpperCase().stripTrailing()) {
            case "ABIF":
                viewModel.setCompanyName("Aditya Birla Finance Limited Infrastructure Finance");
                break;
            case "ABMI":
                viewModel.setCompanyName("Aditya Birla Money Insurance Advisory Services Limited");
                break;
            case "ABFL" :
                viewModel.setCompanyName("Aditya Birla Finance Limited");
                break;
            default:
                viewModel.setCompanyName("Aditya Birla Finance Limited");
                break;
        }

        viewModel.setDescription(report.getDescription());
        viewModel.setDelegationRemarks(report.getDelegationRemarks());
        viewModel.setReportMarker(String.format("%s | %s", report.getExpenseMetadata().getExpenseType(), report.getExpenseMetadata().getExpenseGroup()));
        viewModel.setStartDate(report.getStartDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
        viewModel.setEndDate(report.getEndDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
        viewModel.setLastApprover(lastApprover);
        viewModel.setTotalClaim(String.format("%.2f", report.getReportClaimAmount()));
        viewModel.setSentBackRemarks(sentBackRemarks);

        ReportEmailWrapperViewModel wrapperViewModel = new ReportEmailWrapperViewModel();
        wrapperViewModel.setCustomerId(environmentDataProvider.getCustomerId());
        wrapperViewModel.setProductId(environmentDataProvider.getProductId());
        wrapperViewModel.setMsgFormat("HTML");
        wrapperViewModel.setTemplateId(templateId);
        wrapperViewModel.setMsgFrom(environmentDataProvider.getSenderEmail());
        wrapperViewModel.setMsgProps(viewModel);
        wrapperViewModel.setMsgToList(recipientEmail);

        return wrapperViewModel;
    }

    private ReportEmailWrapperViewModel getEmailWrapper(EmployeeCreationConfirmationViewModel confirmationViewModel, String templateId) {
        ReportEmailViewModel viewModel = new ReportEmailViewModel();
        viewModel.setEmployeeName(String.format("%s %s", confirmationViewModel.getFirstName(), confirmationViewModel.getLastName()));

        ReportEmailWrapperViewModel wrapperViewModel = new ReportEmailWrapperViewModel();
        wrapperViewModel.setCustomerId(environmentDataProvider.getCustomerId());
        wrapperViewModel.setProductId(environmentDataProvider.getProductId());
        wrapperViewModel.setMsgFormat("HTML");
        wrapperViewModel.setTemplateId(templateId);
        wrapperViewModel.setMsgFrom(environmentDataProvider.getSenderEmail());
        wrapperViewModel.setMsgProps(viewModel);
        wrapperViewModel.setMsgToList(confirmationViewModel.getEmail());

        return wrapperViewModel;
    }

    private ReportEmailWrapperViewModel getEmailWrapperTest() {
        ReportEmailViewModel viewModel = new ReportEmailViewModel();
        viewModel.setEmployeeName(String.format("%s %s", "VxTest1", "VxTest1"));

        ReportEmailWrapperViewModel wrapperViewModel = new ReportEmailWrapperViewModel();
        wrapperViewModel.setCustomerId("4423");
        wrapperViewModel.setProductId(environmentDataProvider.getProductId());
        wrapperViewModel.setMsgFormat("HTML");
        wrapperViewModel.setTemplateId(environmentDataProvider.getNewUserWelcomeTemplateId());
        wrapperViewModel.setMsgFrom(environmentDataProvider.getSenderEmail());
        wrapperViewModel.setMsgProps(viewModel);
        wrapperViewModel.setMsgToList("<EMAIL>");

        return wrapperViewModel;
    }

    private ReportEmailWrapperViewModel getEmailWrapper(String currentApproverName, String recipientEmail, List<ApprovalPendingLineViewModel> pendingList, String templateId) {
        ReportEmailViewModel viewModel = new ReportEmailViewModel();
        viewModel.setCurrentApprover(currentApproverName);
        viewModel.setTotalPending(pendingList.size());
        viewModel.setPendingLines(pendingList);

        ReportEmailWrapperViewModel wrapperViewModel = new ReportEmailWrapperViewModel();
        wrapperViewModel.setCustomerId(environmentDataProvider.getCustomerId());
        wrapperViewModel.setProductId(environmentDataProvider.getProductId());
        wrapperViewModel.setMsgFormat("HTML");
        wrapperViewModel.setTemplateId(templateId);
        wrapperViewModel.setMsgFrom(environmentDataProvider.getSenderEmail());
        wrapperViewModel.setMsgProps(viewModel);
        wrapperViewModel.setMsgToList(recipientEmail);

        return wrapperViewModel;
    }
}
