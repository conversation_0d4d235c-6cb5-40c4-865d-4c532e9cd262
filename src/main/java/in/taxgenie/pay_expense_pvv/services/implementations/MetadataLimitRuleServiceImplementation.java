package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ExpenseMetadata;
import in.taxgenie.pay_expense_pvv.entities.MetadataLimitRule;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseMetadataRepository;
import in.taxgenie.pay_expense_pvv.repositories.IMetadataLimitRuleRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IMetadataLimitRuleService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.MetadataLimitRuleCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.MetadataLimitRuleUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.MetadataLimitRuleViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MetadataLimitRuleServiceImplementation implements IMetadataLimitRuleService {
    private final IMetadataLimitRuleRepository repository;
    private final IExpenseMetadataRepository metadataRepository;
    private final Logger logger;

    public MetadataLimitRuleServiceImplementation(IMetadataLimitRuleRepository repository, IExpenseMetadataRepository metadataRepository) {
        this.repository = repository;
        this.metadataRepository = metadataRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Transactional
    @Override
    public MetadataLimitRuleViewModel create(MetadataLimitRuleCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Checking whether metadata exists");
        ExpenseMetadata metadata = metadataRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseMetadataId())
                .orElseThrow(() -> new DomainInvariantException("Could not find the metadata with id: " + viewModel.getExpenseMetadataId()));

        validateCreateUniqueness(viewModel, auth);

        logger.trace("create: Creating and copying entity");
        MetadataLimitRule entity = new MetadataLimitRule();
        BeanUtils.copyProperties(viewModel, entity);

        logger.trace("create: Setting audit fields");
        entity.setCreatingUserId(auth.getUserId());
        entity.setCreatedTimestamp(ZonedDateTime.now());
        entity.setCompanyCode(auth.getCompanyCode());

        metadata.setRulesCount(metadata.getRulesCount() + StaticDataRegistry.COUNT_INCREMENT_FACTOR);

        logger.trace("create: Setting bidirectional properties");
        metadata.getLimitRules().add(entity);
        entity.setExpenseMetadata(metadata);

        logger.trace("create: Saving both the entities");
        repository.saveAndFlush(entity);
        metadataRepository.saveAndFlush(metadata);
        logger.trace("create: Save successful; returning the view-model");

        return getViewModel(entity);
    }

    private void validateCreateUniqueness(MetadataLimitRuleCreateViewModel viewModel, IAuthContextViewModel auth) {
        Optional<MetadataLimitRule> conflictingRule = repository.findByCompanyCodeAndExpenseMetadataId(auth.getCompanyCode(), viewModel.getExpenseMetadataId())
                .stream()
                .filter(r -> doesRuleMatch(viewModel, r))
                .findFirst();

        if (conflictingRule.isPresent()) {
            throw new DomainInvariantException("Duplicate Rule");
        }
    }

    private void validateUpdateUniqueness(MetadataLimitRuleUpdateViewModel viewModel, long metadataId, IAuthContextViewModel auth) {
        Optional<MetadataLimitRule> conflictingRule = repository.findByCompanyCodeAndExpenseMetadataId(auth.getCompanyCode(), metadataId)
                .stream()
                .filter(r -> doesRuleMatch(viewModel, r))
                .findFirst();

        if (conflictingRule.isPresent()) {
            throw new DomainInvariantException("Duplicate Rule");
        }
    }

    private boolean doesRuleMatch(MetadataLimitRuleCreateViewModel viewModel, MetadataLimitRule rule) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(viewModel.getBranchCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(viewModel.getDepartmentCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(viewModel.getCostCenterCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(viewModel.getEmployeeType());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(viewModel.getEmployeeGrade());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
            result = rule.getLocationCategory().equals(viewModel.getLocationCategory());
            if (!result) return false;
        }

        return result;
    }

    private boolean doesRuleMatch(MetadataLimitRuleUpdateViewModel viewModel, MetadataLimitRule rule) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(viewModel.getBranchCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(viewModel.getDepartmentCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(viewModel.getCostCenterCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(viewModel.getEmployeeType());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(viewModel.getEmployeeGrade());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
            result = rule.getLocationCategory().equals(viewModel.getLocationCategory());
            if (!result) return false;
        }

        return result ? viewModel.getId() != rule.getId() : result;
    }

    @Override
    public MetadataLimitRuleViewModel update(long id, MetadataLimitRuleUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("update: Ensuring the congruence of the id and view-model");
        if (id != viewModel.getId()) {
            throw new DomainInvariantException("Id and view-model are not congruent");
        }

        logger.trace("update: Ensuring the existence of the rule");
        MetadataLimitRule entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new DomainInvariantException("Could not find the metadata limit rule by id: " + id));

//        logger.trace("update: Ensuring if a conflicting interval exists");
//        if (repository.getCountByInterval(auth.getCompanyCode(), viewModel.getIntervalMarker(), viewModel.getId()) > 0) {
//            throw new DomainInvariantException("This metadata limit rule conflicts with existing interval: " + viewModel.getIntervalMarker());
//        }

        validateUpdateUniqueness(viewModel, entity.getExpenseMetadataId(), auth);

        logger.trace("update: Updating the fields");
        BeanUtils.copyProperties(viewModel, entity, "expenseMetadataId");

        logger.trace("update: Saving and returning the view-model");
        return getViewModel(repository.saveAndFlush(entity));
    }

    @Override
    public List<MetadataLimitRuleViewModel> getRulesByMetadataId(long metadataId, IAuthContextViewModel auth) {
        return repository
                .findByCompanyCodeAndExpenseMetadataId(auth.getCompanyCode(), metadataId)
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public MetadataLimitRuleViewModel getRuleById(long id, IAuthContextViewModel auth) {
        logger.trace("update: Ensuring the existence of the rule");
        MetadataLimitRule entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new DomainInvariantException("Could not find the metadata limit rule by id: " + id));

        return getViewModel(entity);
    }


    private MetadataLimitRuleViewModel getViewModel(MetadataLimitRule entity) {
        logger.trace("getViewModel: Preparing view model");
        MetadataLimitRuleViewModel returnViewModel = new MetadataLimitRuleViewModel();

        logger.trace("getViewModel: Copying fields to view model");
        BeanUtils.copyProperties(entity, returnViewModel);

        returnViewModel.setMetadataMarker(StaticDataRegistry.getExpenseMetadataMarkerForRule(entity));

        logger.trace("getViewModel: Returning the view model");
        return returnViewModel;
    }
}
