package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ExpenseMetadata;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseMetadataRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseMetadataService;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseMetadataCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseMetadataUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseMetadataViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ExpenseMetadataServiceImplementation implements IExpenseMetadataService {
    private final IExpenseMetadataRepository repository;
    private final Logger logger;

    public ExpenseMetadataServiceImplementation(IExpenseMetadataRepository repository) {
        this.repository = repository;
        this.logger = LoggerFactory.getLogger(ExpenseMetadataServiceImplementation.class);
    }

    @Override
    public ExpenseMetadataViewModel create(ExpenseMetadataCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Ensuring uniqueness by type, group, and subgroup");
        if (repository.getCountOfTypeAndGroup(auth.getCompanyCode(), viewModel.getExpenseType(), viewModel.getExpenseGroup()) > 0) {
            throw new DomainInvariantException(String.format("Expense Metadata for Company Code: %d with expense type: %s, and group: %s already exists", auth.getCompanyCode(), viewModel.getExpenseType(), viewModel.getExpenseGroup()));
        }

        logger.trace("create: Ensuring uniqueness by prefixes for type, group, and subgroup");
        if (repository.getCountByPrefixes(auth.getCompanyCode(), viewModel.getExpenseTypePrefix(), viewModel.getExpenseGroupPrefix()) > 0) {
            throw new DomainInvariantException(String.format("Expense Metadata for Company Code: %d with prefixes for expense type: %s, and group: %s already exists", auth.getCompanyCode(), viewModel.getExpenseTypePrefix(), viewModel.getExpenseGroupPrefix()));
        }

        logger.trace("create: Creating new entity");
        ExpenseMetadata entity = new ExpenseMetadata();
        BeanUtils.copyProperties(viewModel, entity);

        logger.trace("create: Setting audit fields");
        entity.setCreatingUserId(auth.getUserId());
        entity.setCreatedTimestamp(ZonedDateTime.now());
        entity.setCompanyCode(auth.getCompanyCode());

        logger.trace("create: Saving entity");
        ExpenseMetadata savedEntity = repository.saveAndFlush(entity);
        logger.trace("create: Save successful");

        return getViewModel(savedEntity);
    }

    @Override
    public ExpenseMetadataViewModel update(long id, ExpenseMetadataUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("update: Matching the passed id with that of the view model");
        if (id != viewModel.getId()) {
            throw new DomainInvariantException("Passed id doesn't match with that of the view model");
        }

        logger.trace("update: Find the source record");
        ExpenseMetadata entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", ExpenseMetadata.class, viewModel.getId(), auth.getCompanyCode())));


        logger.trace("update: Ensuring uniqueness by type, group, and subgroup");
        if (!entity.getExpenseType().equals(viewModel.getExpenseType()) &&
                !entity.getExpenseGroup().equals(viewModel.getExpenseGroup()) &&
                repository.getCountOfTypeAndGroup(auth.getCompanyCode(), viewModel.getExpenseType(), viewModel.getExpenseGroup()) > 0) {
            throw new DomainInvariantException(String.format("Expense Metadata for Company Code: %d with expense type: %s, and group: %s already exists", auth.getCompanyCode(), viewModel.getExpenseType(), viewModel.getExpenseGroup()));
        }

        logger.trace("update: Ensuring uniqueness by prefixes for type, group, and subgroup");
        if (!entity.getExpenseTypePrefix().equals(viewModel.getExpenseTypePrefix()) &&
                !entity.getExpenseGroupPrefix().equals(viewModel.getExpenseGroupPrefix()) &&
                repository.getCountByPrefixes(auth.getCompanyCode(), viewModel.getExpenseTypePrefix(), viewModel.getExpenseGroupPrefix()) > 0) {
            throw new DomainInvariantException(String.format("Expense Metadata for Company Code: %d with prefixes for expense type: %s, and group: %s already exists", auth.getCompanyCode(), viewModel.getExpenseTypePrefix(), viewModel.getExpenseGroupPrefix()));
        }

        logger.trace("update: Source record found, applying the updates");
        BeanUtils.copyProperties(viewModel, entity);

        logger.trace("update: Setting the audit fields");
        entity.setUpdatingUserId(auth.getUserId());
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("update: Saving the updated entity");
        ExpenseMetadata savedEntity = repository.saveAndFlush(entity);
        logger.trace("update: Save successful");

        return getViewModel(savedEntity);
    }

    @Override
    public List<ExpenseMetadataViewModel> getAll(IAuthContextViewModel auth) {
        logger.trace("getAll: Returning view model list");
        return repository
                .findByCompanyCode(auth.getCompanyCode())
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public ExpenseMetadataViewModel getById(long id, IAuthContextViewModel auth) {
        logger.trace("getById: Find the source record");
        ExpenseMetadata entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", ExpenseMetadata.class, id, auth.getCompanyCode())));

        return getViewModel(entity);
    }

    @Override
    public List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(IAuthContextViewModel auth) {
        return repository
                .findByCompanyCode(auth.getCompanyCode())
                .stream()
                .filter(e -> !e.isFrozen())
                .map(e -> new KeyValuePairViewModel<>(
                        e.getId(),
                        String.format("%s > %s", e.getExpenseType(), e.getExpenseGroup())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByTypeAndGroup(String type, String group, IAuthContextViewModel auth) {
        return repository.getCountOfTypeAndGroup(auth.getCompanyCode(), type, group) > 0;
    }

    @Override
    public boolean existsByPrefixesOfTypeAndGroup(String typePrefix, String groupPrefix, IAuthContextViewModel auth) {
        return repository.getCountByPrefixes(auth.getCompanyCode(), typePrefix, groupPrefix) > 0;
    }

    private ExpenseMetadataViewModel getViewModel(ExpenseMetadata entity) {
        ExpenseMetadataViewModel viewModel = new ExpenseMetadataViewModel();
        BeanUtils.copyProperties(entity, viewModel);
//        viewModel.setDefinitionsCount(entity.getApprovalDefinitions().size());
//        viewModel.setSubgroupsCount(entity.getExpenseSubgroups().size());
//        viewModel.setRulesCount(entity.getLimitRules().size());
        return viewModel;
    }
}
