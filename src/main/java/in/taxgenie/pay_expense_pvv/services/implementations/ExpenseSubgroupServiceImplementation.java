package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ExpenseMetadata;
import in.taxgenie.pay_expense_pvv.entities.ExpenseSubgroup;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.MasterRecordNotFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseMetadataRepository;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseSubgroupRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseSubgroupService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseSubgroupCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseSubgroupUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseSubgroupViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ExpenseSubgroupServiceImplementation implements IExpenseSubgroupService {
    private final IExpenseSubgroupRepository repository;
    private final IExpenseMetadataRepository metadataRepository;
    private final Logger logger;

    public ExpenseSubgroupServiceImplementation(
            IExpenseSubgroupRepository repository,
            IExpenseMetadataRepository metadataRepository
    ) {
        this.repository = repository;
        this.metadataRepository = metadataRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Transactional
    @Override
    public ExpenseSubgroupViewModel create(ExpenseSubgroupCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Ensuring expense code uniqueness");
        if (repository.getCountByCode(auth.getCompanyCode(), viewModel.getExpenseCode(), viewModel.getExpenseMetadataId(), StaticDataRegistry.NEW_ENTITY_ID) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by code: %s", viewModel.getExpenseCode()));
        }

        logger.trace("create: Ensuring expense subgroup uniqueness");
        if (repository.getCountBySubgroup(auth.getCompanyCode(), viewModel.getExpenseSubgroup(), viewModel.getExpenseMetadataId(), StaticDataRegistry.NEW_ENTITY_ID) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by name: %s", viewModel.getExpenseSubgroup()));
        }

        logger.trace("create: Ensuring expense subgroup prefix uniqueness");
        if (repository.getCountByPrefix(auth.getCompanyCode(), viewModel.getExpenseSubgroupPrefix(), viewModel.getExpenseMetadataId(), StaticDataRegistry.NEW_ENTITY_ID) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by prefix: %s", viewModel.getExpenseSubgroupPrefix()));
        }

        logger.trace("create: Ensuring expense metadata exists");
        ExpenseMetadata metadata = metadataRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseMetadataId())
                .orElseThrow(() -> new MasterRecordNotFoundException(String.format("Expense metadata with id: %d is not found", viewModel.getExpenseMetadataId())));

        logger.trace("create: Creating new entity and copying fields");
        ExpenseSubgroup entity = new ExpenseSubgroup();
        BeanUtils.copyProperties(viewModel, entity);

        logger.trace("create: Setting audit fields");
        entity.setCreatingUserId(auth.getUserId());
        entity.setCreatedTimestamp(ZonedDateTime.now());
        entity.setCompanyCode(auth.getCompanyCode());

        metadata.setSubgroupsCount(metadata.getSubgroupsCount() + StaticDataRegistry.COUNT_INCREMENT_FACTOR);

        logger.trace("create: Setting bi-directional mapping");
        entity.setExpenseMetadata(metadata);
        metadata.getExpenseSubgroups().add(entity);

        logger.trace("create: Start saving");
        repository.saveAndFlush(entity);
        metadataRepository.saveAndFlush(metadata);
        logger.trace("create: Save complete");

        return getViewModel(entity);
    }

    @Override
    public ExpenseSubgroupViewModel update(long id, ExpenseSubgroupUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("update: Ensuring the id matches with viewmodel");
        if (id != viewModel.getId()) {
            throw new DomainInvariantException("ExpenseSubgroupViewModel update: id mismatch");
        }

        logger.trace("update: Ensuring the entity exists");
        ExpenseSubgroup entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find ExpenseSubgroup with id: %d", id)));


        logger.trace("update: Ensuring the expense code uniqueness");
        if (repository.getCountByCode(auth.getCompanyCode(), viewModel.getExpenseCode(), viewModel.getExpenseMetadataId(), viewModel.getId()) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by code: %s", viewModel.getExpenseCode()));
        }

        logger.trace("update: Ensuring subgroup uniqueness");
        if (repository.getCountBySubgroup(auth.getCompanyCode(), viewModel.getExpenseSubgroup(), viewModel.getExpenseMetadataId(), viewModel.getId()) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by name: %s", viewModel.getExpenseSubgroup()));
        }

        logger.trace("update: Ensuring prefix uniquensess");
        if (repository.getCountByPrefix(auth.getCompanyCode(), viewModel.getExpenseSubgroupPrefix(), viewModel.getExpenseMetadataId(), viewModel.getId()) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by prefix: %s", viewModel.getExpenseSubgroupPrefix()));
        }

        logger.trace("update: Copying fields");
        BeanUtils.copyProperties(viewModel, entity, "expenseMetadataId");

        logger.trace("update: Setting the audit fields");
        entity.setUpdatingUserId(auth.getUserId());
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("update: Start saving");
        repository.saveAndFlush(entity);
        logger.trace("update: Save complete");

        return getViewModel(entity);
    }

    @Override
    public List<ExpenseSubgroupViewModel> getAll(IAuthContextViewModel auth) {
        return repository
                .findAllByCompanyCode(auth.getCompanyCode())
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<ExpenseSubgroupViewModel> getAllByMetadata(long metadataId, IAuthContextViewModel auth) {
        return repository
                .findAllByCompanyCodeAndExpenseMetadataId(auth.getCompanyCode(), metadataId)
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public ExpenseSubgroupViewModel getById(long id, IAuthContextViewModel auth) {
        ExpenseSubgroup entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find ExpenseSubgroup with id: %d", id)));

        return getViewModel(entity);
    }

    @Override
    public List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(IAuthContextViewModel auth) {
        return repository
                .findAllByCompanyCode(auth.getCompanyCode())
                .stream()
                .filter(e -> !e.isFrozen())
                .map(e -> new KeyValuePairViewModel<>(
                        e.getId(),
                        String.format("%s > %s", e.getExpenseSubgroup(), e.getExpenseCode())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(long metadataId, IAuthContextViewModel auth) {
        return repository
                .findAllByCompanyCodeAndExpenseMetadataId(auth.getCompanyCode(), metadataId)
                .stream()
                .filter(e -> !e.isFrozen())
                .map(e -> new KeyValuePairViewModel<>(
                        e.getId(),
                        String.format("%s > %s", e.getExpenseSubgroup(), e.getExpenseCode())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByExpenseCode(String expenseCode, long metadataId, IAuthContextViewModel auth) {
        return repository.getCountByCode(auth.getCompanyCode(), expenseCode, metadataId, StaticDataRegistry.DEFAULT_ENTITY_ID) > 0;
    }

    @Override
    public boolean existsBySubgroup(String subgroup, long metadataId, IAuthContextViewModel auth) {
        return repository.getCountBySubgroup(auth.getCompanyCode(), subgroup, metadataId, StaticDataRegistry.DEFAULT_ENTITY_ID) > 0;
    }

    @Override
    public boolean existsByPrefix(String prefix, long metadataId, IAuthContextViewModel auth) {
        return repository.getCountByPrefix(auth.getCompanyCode(), prefix, metadataId, StaticDataRegistry.DEFAULT_ENTITY_ID) > 0;
    }

    private ExpenseSubgroupViewModel getViewModel(ExpenseSubgroup entity) {
        ExpenseSubgroupViewModel viewModel = new ExpenseSubgroupViewModel();
        BeanUtils.copyProperties(entity, viewModel);
        viewModel.setExpenseSubgroupMarker(StaticDataRegistry.getExpenseSubgroupMarker(entity));
//        viewModel.setRulesCount(entity.getRules().size());

        return viewModel;
    }
}
