package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Expense;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@Service
@Profile("LocalFS")
public class FileIOServiceImplementation implements IFileIOService {
    private final Logger logger;
    private final IExpenseRepository expenseRepository;

    public FileIOServiceImplementation(IExpenseRepository expenseRepository) {
        this.expenseRepository = expenseRepository;
        this.logger = LoggerFactory.getLogger(FileIOServiceImplementation.class);
    }

    @Override
    public void upload(MultipartFile file, String identifier, int index, Expense expense) {
        logger.trace("upload: Checking if the file is empty");
        if (file.isEmpty()) {
            logger.info("upload: File is empty");
            throw new DomainInvariantException("File is empty");
        }

        try {
            logger.trace("upload: Checking if the expense already has the document uploaded");

            if (index == 1) {
                if (expense.getDocument1UploadUrl() != null) {
                    Files.delete(Paths.get(expense.getDocument1UploadUrl()));
                }

                logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
                if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
                    throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
                }

                UUID uuid = UUID.randomUUID();

                logger.trace("upload: Writing file to file system on the server");
                byte[] fileBytes = file.getBytes();
                Path filePath = Paths.get("uploads", uuid.toString());
                Files.write(filePath, fileBytes);

                logger.trace("upload: Setting relevant fields on the expense");
                expense.setDocument1UploadMarker(identifier);
                expense.setDocument1UploadUuid(uuid.toString());
                expense.setDocument1UploadContentType(file.getContentType());
                expense.setDocument1UploadUrl(filePath.toString());

                logger.trace("upload: Saving the expense");
                expenseRepository.saveAndFlush(expense);

                logger.trace("upload: Save successful");
            }

            if (index == 2) {
                if (expense.getDocument2UploadUrl() != null) {
                    Files.delete(Paths.get(expense.getDocument2UploadUrl()));
                }

                logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
                if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
                    throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
                }

                UUID uuid = UUID.randomUUID();

                logger.trace("upload: Writing file to file system on the server");
                byte[] fileBytes = file.getBytes();
                Path filePath = Paths.get("uploads", uuid.toString());
                Files.write(filePath, fileBytes);

                logger.trace("upload: Setting relevant fields on the expense");
                expense.setDocument2UploadMarker(identifier);
                expense.setDocument2UploadUuid(uuid.toString());
                expense.setDocument2UploadContentType(file.getContentType());
                expense.setDocument2UploadUrl(filePath.toString());

                logger.trace("upload: Saving the expense");
                expenseRepository.saveAndFlush(expense);

                logger.trace("upload: Save successful");
            }

            if (index == 3) {
                if (expense.getDocument3UploadUrl() != null) {
                    Files.delete(Paths.get(expense.getDocument3UploadUrl()));
                }

                logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
                if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
                    throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
                }

                UUID uuid = UUID.randomUUID();

                logger.trace("upload: Writing file to file system on the server");
                byte[] fileBytes = file.getBytes();
                Path filePath = Paths.get("uploads", uuid.toString());
                Files.write(filePath, fileBytes);

                logger.trace("upload: Setting relevant fields on the expense");
                expense.setDocument3UploadMarker(identifier);
                expense.setDocument3UploadUuid(uuid.toString());
                expense.setDocument3UploadContentType(file.getContentType());
                expense.setDocument3UploadUrl(filePath.toString());

                logger.trace("upload: Saving the expense");
                expenseRepository.saveAndFlush(expense);

                logger.trace("upload: Save successful");
            }


        } catch (IOException ioException) {
            logger.error("upload: Exception caught: cannot write: {}", ioException.getMessage());
            throw new RuntimeException("Failed to upload the file");
        }
    }

    @Override
    public ResponseEntity<Resource> getAttachment1(String identifier, long expenseId, IAuthContextViewModel auth) {
        try {
            logger.trace("getFile: Checking if the expense exists");
            Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                    .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

            logger.trace("getFile: Checking if the document upload url exists");
            if (expense.getDocument1UploadUrl() == null) {
                logger.info("getFile: Document upload path is missing (no upload)");
                throw new DomainInvariantException("Expense doesn't have a document uploaded");
            }

            logger.trace("getFile: Reading the file");
            InputStreamResource resource = new InputStreamResource(new FileInputStream(expense.getDocument1UploadUrl()));

            logger.trace("getFile: Preparing the response");
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf(expense.getDocument1UploadContentType()))
                    .body(resource);
        } catch (FileNotFoundException fileNotFoundException) {
            logger.error("getFile: Caught exception: {}", fileNotFoundException.getMessage());
            throw new DomainInvariantException("Could not find the file");
        }
    }

    @Override
    public ResponseEntity<Resource> getAttachment2(String identifier, long expenseId, IAuthContextViewModel auth) {
        try {
            logger.trace("getFile: Checking if the expense exists");
            Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                    .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

            logger.trace("getFile: Checking if the document upload url exists");
            if (expense.getDocument2UploadUrl() == null) {
                logger.info("getFile: Document upload path is missing (no upload)");
                throw new DomainInvariantException("Expense doesn't have a document uploaded");
            }

            logger.trace("getFile: Reading the file");
            InputStreamResource resource = new InputStreamResource(new FileInputStream(expense.getDocument2UploadUrl()));

            logger.trace("getFile: Preparing the response");
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf(expense.getDocument2UploadContentType()))
                    .body(resource);
        } catch (FileNotFoundException fileNotFoundException) {
            logger.error("getFile: Caught exception: {}", fileNotFoundException.getMessage());
            throw new DomainInvariantException("Could not find the file");
        }
    }

    @Override
    public ResponseEntity<Resource> getAttachment3(String identifier, long expenseId, IAuthContextViewModel auth) {
        try {
            logger.trace("getFile: Checking if the expense exists");
            Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                    .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

            logger.trace("getFile: Checking if the document upload url exists");
            if (expense.getDocument3UploadUrl() == null) {
                logger.info("getFile: Document upload path is missing (no upload)");
                throw new DomainInvariantException("Expense doesn't have a document uploaded");
            }

            logger.trace("getFile: Reading the file");
            InputStreamResource resource = new InputStreamResource(new FileInputStream(expense.getDocument3UploadUrl()));

            logger.trace("getFile: Preparing the response");
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf(expense.getDocument3UploadContentType()))
                    .body(resource);
        } catch (FileNotFoundException fileNotFoundException) {
            logger.error("getFile: Caught exception: {}", fileNotFoundException.getMessage());
            throw new DomainInvariantException("Could not find the file");
        }
    }

    @Override
    public void delete(Expense expense) {
        try {
            logger.trace("delete: Checking existence of document 1 for deletion");
            if (expense.getDocument1UploadUrl() != null) {
                Files.delete(Paths.get(expense.getDocument1UploadUrl()));
            }

            logger.trace("delete: Checking existence of document 2 for deletion");
            if (expense.getDocument2UploadUrl() != null) {
                Files.delete(Paths.get(expense.getDocument2UploadUrl()));
            }

            logger.trace("delete: Checking existence of document 3 for deletion");
            if (expense.getDocument3UploadUrl() != null) {
                Files.delete(Paths.get(expense.getDocument3UploadUrl()));
            }
        } catch (IOException ioException) {
            logger.trace("delete: IOException thrown while deleting: {}", ioException.getMessage());
        }
    }

    @Override
    public void delete(int index, Expense expense) {
        try {
            logger.trace("delete: Checking existence of document 1 for deletion");
            if (index == 1 && expense.getDocument1UploadUrl() != null) {
                Files.delete(Paths.get(expense.getDocument1UploadUrl()));
            }

            logger.trace("delete: Checking existence of document 2 for deletion");
            if (index == 2 && expense.getDocument2UploadUrl() != null) {
                Files.delete(Paths.get(expense.getDocument2UploadUrl()));
            }

            logger.trace("delete: Checking existence of document 3 for deletion");
            if (index == 3 && expense.getDocument3UploadUrl() != null) {
                Files.delete(Paths.get(expense.getDocument3UploadUrl()));
            }
        } catch (IOException ioException) {
            logger.trace("delete: IOException thrown while deleting: {}", ioException.getMessage());
        }
    }
}
