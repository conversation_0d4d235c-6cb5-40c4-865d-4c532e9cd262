package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ExpenseReport;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.UpdateEmailAudit;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseReportRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDashboardService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IRestUpdater;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.EmailUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ValidateEmailDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ValidateEmailViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.EmployeeDashboardViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.EmployeeDetailViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.EmployeeMonthlyClaimLine;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.EmployeeStatisticsViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Service
public class DashboardServiceImplementation implements IDashboardService {
    private final IExpenseReportRepository reportRepository;
    private final IEmployeeMasterDataService employeeService;
    private final IRestUpdater<EmailUpdateViewModel> updateTemplate;
    private final Logger logger;

    @Value("${CEM_URL}")
    private String cemUrl;

    public DashboardServiceImplementation(
            IExpenseReportRepository reportRepository,
            IEmployeeMasterDataService employeeService,
            IRestUpdater<EmailUpdateViewModel> updateTemplate
    ) {
        this.reportRepository = reportRepository;
        this.employeeService = employeeService;
        this.updateTemplate = updateTemplate;

        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public EmployeeDashboardViewModel getDashboard(IAuthContextViewModel auth) {
        EmployeeDashboardViewModel dashboardViewModel = new EmployeeDashboardViewModel();

        LocalDate[] dateCoordinates = getYearDelimiters(LocalDate.now());

        dashboardViewModel.setStatistics(getStatistics(auth.getCompanyCode(), auth.getUserId(), dateCoordinates[0], dateCoordinates[1]));
        dashboardViewModel.setEmployeeDetail(getEmployeeDetails(auth.getUserEmail(), auth));
        dashboardViewModel.addMonthlyClaim(getMonthlyClaimLine(2, auth.getCompanyCode(), auth.getUserId()));
        dashboardViewModel.addMonthlyClaim(getMonthlyClaimLine(1, auth.getCompanyCode(), auth.getUserId()));
        dashboardViewModel.addMonthlyClaim(getMonthlyClaimLine(0, auth.getCompanyCode(), auth.getUserId()));

        return dashboardViewModel;
    }

    @Override
    public EmployeeDashboardViewModel getDashboard(long userId, String employeeEmail, IAuthContextViewModel auth) {
        EmployeeDashboardViewModel dashboardViewModel = new EmployeeDashboardViewModel();

        LocalDate[] dateCoordinates = getYearDelimiters(LocalDate.now());

        dashboardViewModel.setStatistics(getStatistics(auth.getCompanyCode(), userId, dateCoordinates[0], dateCoordinates[1]));
        dashboardViewModel.setEmployeeDetail(getEmployeeDetails(employeeEmail, auth));
        dashboardViewModel.addMonthlyClaim(getMonthlyClaimLine(2, auth.getCompanyCode(), userId));
        dashboardViewModel.addMonthlyClaim(getMonthlyClaimLine(1, auth.getCompanyCode(), userId));
        dashboardViewModel.addMonthlyClaim(getMonthlyClaimLine(0, auth.getCompanyCode(), userId));

        return dashboardViewModel;
    }

    @Override
    public EmployeeDashboardViewModel getCreatorDashboard(String userId, String employeeEmail, IAuthContextViewModel auth) {
        EmployeeDashboardViewModel dashboardViewModel = new EmployeeDashboardViewModel();

        dashboardViewModel.setEmployeeDetail(getEmployeeDetails(employeeEmail, auth));
        return dashboardViewModel;
    }

    @Override
    public EmployeeStatisticsViewModel getEmployeeStatistics(LocalDate start, LocalDate end, IAuthContextViewModel auth) {
        checkDateRangeSanity(start, end);

        if (end == null) {
            end = LocalDate.now();
        }

        return getStatistics(auth.getCompanyCode(), auth.getUserId(), start, end);
    }

    @Override
    public EmployeeStatisticsViewModel getEmployeeStatistics(LocalDate start, LocalDate end, long userId, IAuthContextViewModel auth) {
        checkDateRangeSanity(start, end);

        if (end == null) {
            end = LocalDate.now();
        }

        return getStatistics(auth.getCompanyCode(), userId, start, end);
    }

    @Override
    public ValidateEmailViewModel getEmailValidatedExpenseReports(String employeeEmail, String employeeCode, IAuthContextViewModel auth) {
        logger.trace("Validate Email and send response: {}", employeeEmail);
        List<ExpenseReport> reports = reportRepository.getApproverListByEmail(auth.getCompanyCode(), employeeCode, employeeEmail);
        if (reports == null || reports.isEmpty()) {
            return ValidateEmailViewModel.success();
        }
        return getEmailValidatedExpenseReportData(reports);
    }

    private ValidateEmailViewModel getEmailValidatedExpenseReportData(List<ExpenseReport> reports) {
        List<ValidateEmailDetailsViewModel> details = reports.stream()
                .map(this::mapToDetailViewModel)
                .collect(Collectors.toList());
        return new ValidateEmailViewModel("success",details);
    }

    private ValidateEmailDetailsViewModel mapToDetailViewModel(ExpenseReport report) {
        ValidateEmailDetailsViewModel viewModel = new ValidateEmailDetailsViewModel();
        BeanUtils.copyProperties(report, viewModel);
        return viewModel;
    }

    @Override
    public void updateUserEmailInSystem(EmailUpdateViewModel viewModel, IAuthContextViewModel auth) {
        //TODO: Need to check if this mail is present or not.
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                "employees",
                "update-email"
        );
        EmailUpdateViewModel emailUpdateViewModel = updateTemplate.updateEntityWithResponse(headers, url, EmailUpdateViewModel.class, viewModel);
        if(!emailUpdateViewModel.getStatus().equalsIgnoreCase(StaticDataRegistry.SUCCESS)) {
            logger.error("Email update: failed for email: {} message : {}",viewModel.getOldEmail(),emailUpdateViewModel.getMessage());
            throw new DomainInvariantException("Failed to update email: "+viewModel.getNewEmail());
        }


    }
    private void updateEmailAudit(EmailUpdateViewModel viewModel, IAuthContextViewModel auth) {
        UpdateEmailAudit audit = new UpdateEmailAudit();

    }

    private void checkDateRangeSanity(LocalDate start, LocalDate end) {
        if (start == null) {
            throw new DomainInvariantException("Start date cannot be null");
        }

        if (end != null && start.isAfter(end)) {
            throw new DomainInvariantException("Invalid date range");
        }

        if (start.isBefore(StaticDataRegistry.START_EPOCH)) {
            throw new DomainInvariantException("Start date must be after " + StaticDataRegistry.START_EPOCH);
        }

        if (end != null && end.isAfter(LocalDate.now())) {
            throw new DomainInvariantException("End date must not exceed today " + LocalDate.now());
        }
    }

    private EmployeeStatisticsViewModel getStatistics(long companyCode, long userId, LocalDate start, LocalDate end) {
        EmployeeStatisticsViewModel statisticsViewModel = new EmployeeStatisticsViewModel();
        statisticsViewModel.setStartDate(start);
        statisticsViewModel.setEndDate(end);

        statisticsViewModel.setPaidReports(
                reportRepository
                        .getReportCountByCriteria(
                                companyCode, userId, ReportStatus.PAID, start, end
                        )
                        .orElse(0L)
        );

        statisticsViewModel.setDiscardedReports(
                reportRepository
                        .getReportCountByCriteriaAndCreation(
                                companyCode, userId, ReportStatus.REVOKED, start, end
                        )
                        .orElse(0L)
        );

        statisticsViewModel.setAcceptedReports(
                reportRepository
                        .getReportCountByCriteria(
                                companyCode, userId, ReportStatus.ACCEPTED, start, end
                        )
                        .orElse(0L)
        );

        statisticsViewModel.setSentBackReports(
                reportRepository
                        .getReportCountByCriteria(
                                companyCode, userId, ReportStatus.SENT_BACK, start, end
                        )
                        .orElse(0L)
        );

        statisticsViewModel.setSubmittedReports(
                reportRepository
                        .getReportCountByCriteria(
                                companyCode, userId, ReportStatus.SUBMITTED, start, end
                        )
                        .orElse(0L)
        );

        statisticsViewModel.setDraftReports(
                reportRepository
                        .getReportCountByCriteriaAndCreation(
                                companyCode, userId, ReportStatus.DRAFT, start, end
                        )
                        .orElse(0L)
        );

        return statisticsViewModel;
    }

    private EmployeeDetailViewModel getEmployeeDetails(String employeeEmail, IAuthContextViewModel auth) {
        EmployeeDetailViewModel employeeDetailViewModel = new EmployeeDetailViewModel();
        BeanUtils.copyProperties(employeeService.getEmployeeMasterData(employeeEmail, auth), employeeDetailViewModel);

        return employeeDetailViewModel;
    }

    private EmployeeMonthlyClaimLine getMonthlyClaimLine(int monthIndex, long companyCode, long userId) {
        LocalDate start = LocalDate.now().minusMonths(monthIndex).withDayOfMonth(1);
        LocalDate end = LocalDate.now().minusMonths(monthIndex).withDayOfMonth(LocalDate.now().minusMonths(monthIndex).lengthOfMonth());

        logger.trace("getMonthlyClaimLine: From {} to {}", start, end);

        EmployeeMonthlyClaimLine claimLine = new EmployeeMonthlyClaimLine();
        claimLine.setTotalClaim(
                reportRepository
                        .getTotalClaimForPeriod(companyCode, userId, List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED), start, end)
                        .orElse(0.0)
        );
        claimLine.setMonth(start.getMonth().getValue());
        claimLine.setMonthCode(start.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH));
        claimLine.setYear(start.getYear());
        claimLine.setTotalPaid(
                reportRepository
                        .getTotalPaidForPeriod(companyCode, userId, start, end)
                        .orElse(0.0)
        );
        claimLine.setTotalTds(
                reportRepository
                        .getTotalTdsForPeriod(companyCode, userId, start, end)
                        .orElse(0.0)
        );

        return claimLine;
    }

    private LocalDate[] getYearDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case JANUARY:
            case FEBRUARY:
            case MARCH:
                return new LocalDate[]{LocalDate.of(markDate.getYear() - 1, Month.APRIL, 1), LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1), LocalDate.of(markDate.getYear() + 1, Month.MARCH, 31)};
        }
    }



}
