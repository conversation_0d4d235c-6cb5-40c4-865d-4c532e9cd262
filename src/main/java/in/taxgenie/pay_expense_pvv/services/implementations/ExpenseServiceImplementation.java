package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseValidationStatusViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ReportLimitConsumptionViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;
import java.time.Period;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ExpenseServiceImplementation implements IExpenseService {
	private final IExpenseRepository expenseRepository;
	private final IExpenseReportRepository reportRepository;
	private final IExpenseRuleRepository ruleRepository;
	private final IExpenseSubgroupRepository subgroupRepository;
	private final IEmployeeMasterDataService employeeService;
	private final ILocationRepository locationRepository;
	private final IMetadataLimitRuleRepository metadataLimitRuleRepository;
	private final IFileIOService fileIOService;
	private final Logger logger;

	public ExpenseServiceImplementation(IExpenseRepository expenseRepository, IExpenseReportRepository reportRepository,
			IExpenseRuleRepository ruleRepository, IExpenseSubgroupRepository subgroupRepository,
			IEmployeeMasterDataService employeeService, ILocationRepository locationRepository,
			IMetadataLimitRuleRepository metadataLimitRuleRepository, IFileIOService fileIOService) {
		this.expenseRepository = expenseRepository;
		this.reportRepository = reportRepository;
		this.ruleRepository = ruleRepository;
		this.subgroupRepository = subgroupRepository;
		this.employeeService = employeeService;
		this.locationRepository = locationRepository;
		this.metadataLimitRuleRepository = metadataLimitRuleRepository;
		this.fileIOService = fileIOService;
		this.logger = LoggerFactory.getLogger(this.getClass());
	}

	@Override
	public ExpenseViewModel create(long reportId, long subgroupId, Long locationId, IAuthContextViewModel auth) {
		ExpenseReport report = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), reportId).orElseThrow(
				() -> new RecordNotFoundException(String.format("Could not find report with id: %d", reportId)));

		if (!isParentExpenseReportSubmittable(report)) {
			throw new DomainInvariantException(
					"Parent expense report is neither in draft or sent-back state; hence not submittable");
		}

		ExpenseSubgroup subgroup = subgroupRepository.findByCompanyCodeAndId(auth.getCompanyCode(), subgroupId)
				.orElseThrow(() -> new RecordNotFoundException(
						String.format("Could not find subgroup with id: %d", subgroupId)));

		if (subgroup.isFrozen()) {
			throw new DomainInvariantException("Subgroup is frozen");
		}

		if (subgroup.isLocationRequired() && locationId == null) {
			throw new DomainInvariantException("Location id is required for this expense");
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(subgroup.getApplicableGender())
				&& !subgroup.getApplicableGender().equalsIgnoreCase(report.getGender())) {
			throw new DomainInvariantException("This expense is gender restricted");
		}

		logger.info("create: Creating a new Expense entity");
		Expense expense = new Expense();

		expense.setExpenseReport(report);

		if (!subgroup.isLocationRequired()
				&& (subgroup.isSourceLocationApplicable() || subgroup.isDestinationLocationApplicable())) {
			IEmployeeViewModel employeeDetails = employeeService.getEmployeeMasterData(auth.getUserEmail(), auth);
			Location homeLocation = locationRepository.findByCompanyCode(auth.getCompanyCode()).stream()
					.filter(l -> l.getLocation().equals(employeeDetails.getCityCode())).findFirst().orElseThrow(
							() -> new DomainInvariantException("Could not find the home location for this employee"));

			expense.setLocationCategory(homeLocation.getCategory());
			expense.setLocation(homeLocation.getLocation());
		}

		expense.setClaimAmount(0.0);
		expense.setInvoiceAmount(0.0);
		expense.setApplicableAmount(0.0);
		expense.setExpenseReportId(reportId);
		expense.setExpenseSubgroup(subgroup);
		expense.setExpenseSubgroupId(subgroupId);
		expense.setFrequency(subgroup.getFrequency());

		if (subgroup.isLocationRequired()) {
			Location location = locationRepository.findByCompanyCodeAndId(auth.getCompanyCode(), locationId)
					.orElseThrow(() -> new RecordNotFoundException(
							String.format("Location with id: %d could not be found", locationId)));

			expense.setLocationCategory(location.getCategory());
			expense.setLocation(location.getLocation());
		}

		logger.info("create: Finding the applicable Expense Rule");
		ExpenseRule applicableRule = getApplicableRule(expense);

		logger.info("create: Attaching the applicable Expense Rule as id");
		expense.setExpenseRuleId(applicableRule.getId());

		expense.setCreatingUserId(auth.getUserId());
		expense.setCreatedTimestamp(ZonedDateTime.now());
		expense.setCompanyCode(auth.getCompanyCode());
		expense.setEmployeeEmail(auth.getUserEmail());

		report.getExpenses().add(expense);
		subgroup.getExpenses().add(expense);

		logger.info("create: Saving both Expense and Expense Metadata");
		expenseRepository.saveAndFlush(expense);
		reportRepository.saveAndFlush(report);
		subgroupRepository.saveAndFlush(subgroup);
		logger.info("create: Save successful");

		return getViewModel(expense);
	}

	@Override
	public ExpenseViewModel getById(long id, IAuthContextViewModel auth) {
		return getViewModel(expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id).orElseThrow(
				() -> new RecordNotFoundException(String.format("Could not find expense with id: %d", id))));
	}

    @Override
    public void save(ExpenseUpdateViewModel viewModel, IAuthContextViewModel auth) {
		Expense entity = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
				.orElseThrow(() -> new RecordNotFoundException(
						String.format("Could not find expense with id: %d", viewModel.getId())));

		ExpenseReport report = reportRepository
				.findByCompanyCodeAndId(auth.getCompanyCode(), entity.getExpenseReportId())
				.orElseThrow(() -> new RecordNotFoundException(
						String.format("Could not find expense report with id: %d", entity.getExpenseReportId())));


		if (!isParentExpenseReportSubmittable(report)) {
			throw new DomainInvariantException(
					"Parent expense report is neither in draft or sent-back state; hence not submittable");
		}
        logger.info("save: Fetching the applicable rule");
        ExpenseRule rule = ruleRepository.findByCompanyCodeAndId(auth.getCompanyCode(), entity.getExpenseRuleId()).orElseThrow(() -> new RecordNotFoundException("Could not find the applicable Expense Rule by id: " + entity.getExpenseRuleId()));   //  temporary


		BeanUtils.copyProperties(viewModel, entity);

		if (viewModel.getDocument1() != null) {
			fileIOService.upload(viewModel.getDocument1(), viewModel.getDocument1Identifier(), 1, entity);
		}

		if (viewModel.getDocument2() != null) {
			fileIOService.upload(viewModel.getDocument2(), viewModel.getDocument2Identifier(), 2, entity);
		}

		if (viewModel.getDocument3() != null) {
			fileIOService.upload(viewModel.getDocument3(), viewModel.getDocument3Identifier(), 3, entity);
		}

		logger.info("save: Getting the validation status of the expense");
		ExpenseValidationStatusViewModel validation = getValidationStatus(entity, rule);

		logger.info("save: Checking if all validations are passing");
		if (!validation.isValid()) {
			logger.info("save: Validation errors present: {}", validation.getValidationErrors());
			throw new DomainInvariantException("Invalid expense, cannot save");
		}

		if (getDeviationStatus(entity, rule)) {
			entity.setDeviated(true);
			entity.setDeviationRemarks(String.format("Expense deviated from limit of %.2f by %.2f",
					rule.getLimitAmount(), entity.getClaimAmount() - rule.getLimitAmount()));
		} else {
			entity.setDeviated(false);
			entity.setDeviationRemarks(null);
		}

		logger.info("save: Saving the updated expense");
		expenseRepository.saveAndFlush(entity);
		logger.info("save: Save successful; exiting");
	}

	@Override
	public void save(ExpenseUpdateViewModel viewModel, IAuthContextViewModel auth, boolean performValidations) {
		Expense entity = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
				.orElseThrow(() -> new RecordNotFoundException(
						String.format("Could not find expense with id: %d", viewModel.getId())));

		// Before saving initialize the "hasBeenValidatedValue" to false
		entity.setHasBeenValidated(false);
		expenseRepository.saveAndFlush(entity);

		ExpenseReport report = reportRepository
				.findByCompanyCodeAndId(auth.getCompanyCode(), entity.getExpenseReportId())
				.orElseThrow(() -> new RecordNotFoundException(
						String.format("Could not find expense report with id: %d", entity.getExpenseReportId())));


		if (!isParentExpenseReportSubmittable(report)) {
			throw new DomainInvariantException(
					"Parent expense report is neither in draft or sent-back state; hence not submittable");
		}
		logger.info("save: Fetching the applicable rule");
		ExpenseRule rule = ruleRepository.findByCompanyCodeAndId(auth.getCompanyCode(), entity.getExpenseRuleId()).orElseThrow(() -> new RecordNotFoundException("Could not find the applicable Expense Rule by id: " + entity.getExpenseRuleId()));   //  temporary


		BeanUtils.copyProperties(viewModel, entity);

		if (viewModel.getDocument1() != null) {
			fileIOService.upload(viewModel.getDocument1(), viewModel.getDocument1Identifier(), 1, entity);
		}

		if (viewModel.getDocument2() != null) {
			fileIOService.upload(viewModel.getDocument2(), viewModel.getDocument2Identifier(), 2, entity);
		}

		if (viewModel.getDocument3() != null) {
			fileIOService.upload(viewModel.getDocument3(), viewModel.getDocument3Identifier(), 3, entity);
		}

		// If direct input into system then bypass initial validations
		if (performValidations) {
			logger.info("save: Getting the validation status of the expense");
			ExpenseValidationStatusViewModel validation = getValidationStatusUpi(entity, rule);

			logger.info("save: Checking if all validations are passing");
			if (!validation.isValid()) {
				logger.info("save: Validation errors present: {}", validation.getValidationErrors());
				throw new DomainInvariantException("Invalid expense, cannot save");
			}

			logger.info("save: All validations passing");
			entity.setHasBeenValidated(true);

		} else {
			logger.info(String.format("save: Bypassing validations of the expense %s", entity.getExpenseIdentifier()));
		}


		if (getDeviationStatus(entity, rule)) {
			entity.setDeviated(true);
			entity.setDeviationRemarks(String.format("Expense deviated from limit of %.2f by %.2f",
					rule.getLimitAmount(), entity.getClaimAmount() - rule.getLimitAmount()));
		} else {
			entity.setDeviated(false);
			entity.setDeviationRemarks(null);
		}

		logger.info("save: Saving the updated expense");
		expenseRepository.saveAndFlush(entity);
		logger.info("save: Save successful; exiting");
	}

	@Override
	public void delete(long id, IAuthContextViewModel auth) {
		Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id).orElseThrow(
				() -> new RecordNotFoundException(String.format("Could not find expense with id: %d", id)));

		ExpenseReport report = reportRepository
				.findByCompanyCodeAndId(auth.getCompanyCode(), expense.getExpenseReportId())
				.orElseThrow(() -> new RecordNotFoundException(
						String.format("Could not find expense report with id: %d", expense.getExpenseReportId())));

		if (!isParentExpenseReportSubmittable(report)) {
			throw new DomainInvariantException(
					"Parent expense report is neither in draft or sent-back state; hence not submittable");
		}

		fileIOService.delete(expense);
		expenseRepository.delete(expense);
	}

	@Override
	public List<ExpenseViewModel> getAllByReportId(long reportId, IAuthContextViewModel auth) {
		return expenseRepository.findAllByCompanyCodeAndExpenseReportId(auth.getCompanyCode(), reportId).stream()
				.map(this::getViewModel).collect(Collectors.toList());
	}

	@Override
	public List<ReportLimitConsumptionViewModel> getConsumption(long expenseId, IAuthContextViewModel auth) {
		Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId).orElseThrow(
				() -> new RecordNotFoundException(String.format("Could not find expense with id: %d", expenseId)));

		ExpenseReport report = expense.getExpenseReport();

		List<MetadataLimitRule> rules = getApplicableRule(report);
		List<ReportLimitConsumptionViewModel> consumptions = new ArrayList<>();

		for (MetadataLimitRule rule : rules) {
			switch (rule.getIntervalMarker()) {
			case DAILY:
				consumptions.add(getDailyLimitConsumption(expense, rule));
				break;
			case MONTHLY:
				consumptions.add(getMonthlyLimitConsumption(expense, rule));
				break;
			case QUARTERLY:
				consumptions.add(getQuarterlyLimitConsumption(expense, rule));
				break;
			case HALF_YEARLY:
				consumptions.add(getHalfYearlyLimitConsumption(expense, rule));
				break;
			case YEARLY:
				consumptions.add(getYearlyLimitConsumption(expense, rule));
				break;
			}
		}

		return consumptions;
	}

	private boolean getDeviationStatus(Expense expense, ExpenseRule rule) {
		if (!rule.isCanExceedLimit()) {
			return false;
		}

		return expense.getClaimAmount() > rule.getLimitAmount();
	}

	private ExpenseViewModel getViewModel(Expense entity) {
		ExpenseViewModel viewModel = new ExpenseViewModel();
		BeanUtils.copyProperties(entity, viewModel);
		viewModel.setExpenseSubgroupMarker(StaticDataRegistry.getExpenseSubgroupMarker(entity.getExpenseSubgroup()));
		return viewModel;
	}

	private ExpenseRule getApplicableRule(Expense expense) {
		logger.trace("getApplicableRule: Finding the Expense with id: {}", expense.getId());

		ExpenseSubgroup subgroup = expense.getExpenseSubgroup();

		Optional<ExpenseRule> rule = subgroup.getRules().stream().filter(r -> !r.isFrozen())
				.filter(r -> isRuleMatch(r, expense)).findFirst();

		return rule.orElseThrow(() -> new RecordNotFoundException("Could not find applicable rule for expense"));
	}

	private boolean isRuleMatch(ExpenseRule rule, Expense expense) {
		boolean result = true;

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
			result = rule.getBranchCode().equals(expense.getExpenseReport().getEmployeeBranch());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
			result = rule.getDepartmentCode().equals(expense.getExpenseReport().getEmployeeDepartment());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
			result = rule.getCostCenterCode().equals(expense.getExpenseReport().getEmployeeCostCenter());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
			result = rule.getEmployeeType().equals(expense.getExpenseReport().getEmployeeType());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
			result = rule.getEmployeeGrade().equals(expense.getExpenseReport().getEmployeeGrade());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
			result = rule.getLocationCategory().equals(expense.getLocationCategory());
			if (!result)
				return false;
		}

		return result;
	}

	private ExpenseValidationStatusViewModel getValidationStatus(Expense expense, ExpenseRule rule) {
		logger.trace("getValidationStatus: Running validations on the expense");
		boolean status = true;
		StringBuilder stringBuilder = new StringBuilder();

		try {
			logger.trace("getValidationStatus: Validating travel descriptor");
			ExpenseValidationStatusViewModel travelDescriptor = validateTravelDescriptor(expense);
			logger.trace("getValidationStatus: Valid: {}", travelDescriptor.isValid());
			if (!travelDescriptor.isValid()) {
				status = false;
				stringBuilder.append(travelDescriptor.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating mobility descriptor");
			ExpenseValidationStatusViewModel mobilityDescriptor = validateMobilityDescriptor(expense);
			logger.trace("getValidationStatus: Valid: {}", mobilityDescriptor.isValid());
			if (!mobilityDescriptor.isValid()) {
				status = false;
				stringBuilder.append(mobilityDescriptor.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating transport descriptor");
			ExpenseValidationStatusViewModel transportDescriptor = validateTransportDescriptor(expense);
			logger.trace("getValidationStatus: Valid: {}", transportDescriptor.isValid());
			if (!transportDescriptor.isValid()) {
				status = false;
				stringBuilder.append(transportDescriptor.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating claim and applicable amounts");
			ExpenseValidationStatusViewModel applicableAmount = validateClaimAndApplicableAmounts(expense);
			logger.trace("getValidationStatus: Valid: {}", applicableAmount.isValid());
			if (!applicableAmount.isValid()) {
				status = false;
				stringBuilder.append(applicableAmount.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating invoice and applicable amounts");
			ExpenseValidationStatusViewModel invoiceAndApplicableAmount = validateInvoiceAndApplicableAmounts(expense);
			logger.trace("getValidationStatus: Valid: {}", invoiceAndApplicableAmount.isValid());
			if (!invoiceAndApplicableAmount.isValid()) {
				status = false;
				stringBuilder.append(invoiceAndApplicableAmount.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating standard deduction computation");
			ExpenseValidationStatusViewModel standardDeduction = validateStandardDeduction(expense, rule);
			logger.trace("getValidationStatus: Valid: {}", standardDeduction.isValid());
			if (!standardDeduction.isValid()) {
				status = false;
				stringBuilder.append(standardDeduction.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating per diem computation");
			ExpenseValidationStatusViewModel perDiem = validatePerDiem(expense, rule);
			logger.trace("getValidationStatus: Valid: {}", perDiem.isValid());
			if (!perDiem.isValid()) {
				status = false;
				stringBuilder.append(perDiem.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating merchant details");
			ExpenseValidationStatusViewModel merchantDetails = validateMerchantDetails(expense);
			logger.trace("getValidationStatus: Valid: {}", merchantDetails.isValid());
			if (!merchantDetails.isValid()) {
				status = false;
				stringBuilder.append(merchantDetails.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating source location");
			ExpenseValidationStatusViewModel sourceLocation = validateSourceLocation(expense);
			logger.trace("getValidationStatus: Valid: {}", sourceLocation.isValid());
			if (!sourceLocation.isValid()) {
				status = false;
				stringBuilder.append(sourceLocation.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating destination location");
			ExpenseValidationStatusViewModel destinationLocation = validateDestinationLocation(expense);
			logger.trace("getValidationStatus: Valid: {}", destinationLocation.isValid());
			if (!destinationLocation.isValid()) {
				status = false;
				stringBuilder.append(destinationLocation.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating date range");
			ExpenseValidationStatusViewModel dateRange = validateDateRange(expense);
			logger.trace("getValidationStatus: Valid: {}", dateRange.isValid());
			if (!dateRange.isValid()) {
				status = false;
				stringBuilder.append(dateRange.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating expense identifier");
			ExpenseValidationStatusViewModel expenseIdentifier = validateExpenseIdentifier(expense);
			logger.trace("getValidationStatus: Valid: {}", expenseIdentifier.isValid());
			if (!expenseIdentifier.isValid()) {
				status = false;
				stringBuilder.append(expenseIdentifier.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating unit rate computation");
			ExpenseValidationStatusViewModel unitRateComputation = validateUnitRateComputation(expense, rule);
			logger.trace("getValidationStatus: Valid: {}", unitRateComputation.isValid());
			if (!unitRateComputation.isValid()) {
				status = false;
				stringBuilder.append(unitRateComputation.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating bill amount");
			ExpenseValidationStatusViewModel billAmount = validateBillAmount(expense, rule);
			logger.trace("getValidationStatus: Valid: {}", billAmount.isValid());
			if (!billAmount.isValid()) {
				status = false;
				stringBuilder.append(billAmount.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating date range overlap");
			ExpenseValidationStatusViewModel dateRangeOverlap = validateDateRangeOverlap(expense, rule);
			logger.trace("getValidationStatus: Valid: {}", dateRangeOverlap.isValid());
			if (!dateRangeOverlap.isValid()) {
				status = false;
				stringBuilder.append(dateRangeOverlap.getValidationErrors());
			}

			logger.trace("getValidationStatus: Validating Invoice attachment");
			ExpenseValidationStatusViewModel invoiceAttachment = validateAttachments(expense, rule);
			logger.trace("getValidationStatus: Valid: {}", invoiceAttachment.isValid());
			if (!invoiceAttachment.isValid()) {
				status = false;
				stringBuilder.append(invoiceAttachment.getValidationErrors());
			}
		} catch (NullPointerException nullPointerException) {
			logger.error("getValidationStatus: One of the required fields set to null; exception raised");
			throw new DomainInvariantException(
					String.format("Error accessing necessary objects: %s", nullPointerException.getMessage()));
		}

		logger.info("getValidationStatus: Returning; Final validity: {}", status);
		return new ExpenseValidationStatusViewModel(status, stringBuilder.toString());
	}

	private ExpenseValidationStatusViewModel getValidationStatusUpi(Expense expense, ExpenseRule rule) {
		ExpenseValidationStatusViewModel validation = getValidationStatus(expense, rule);
		// Perform addition validations for UPI transactions (sans-interface)
		logger.trace("getValidationStatusUpi: Running validations on the upi expense");
		boolean status = true;
		StringBuilder stringBuilder = new StringBuilder();
		try {
			logger.trace("getValidationStatusUpi: Validating matching metadata and subgroup");
			ExpenseValidationStatusViewModel subgroup = validateSubgroup(expense, rule);
			if (!subgroup.isValid()) {
				status = false;
				stringBuilder.append(subgroup.getValidationErrors());
			}
		} catch (NullPointerException nullPointerException) {
			logger.error("getValidationStatusUpi: One of the required fields set to null; exception raised");
			throw new DomainInvariantException(
					String.format("Error accessing necessary objects: %s", nullPointerException.getMessage()));
		}
		stringBuilder.append(validation.getValidationErrors());
		logger.info("getValidationStatusUpi: Returning; Final validity: {}", status);
		return new ExpenseValidationStatusViewModel(status, stringBuilder.toString());

	}

	// validation subordinates
	private ExpenseValidationStatusViewModel validateSubgroup(Expense expense, ExpenseRule rule) {
		Long expenseMetadataId = expense.getExpenseSubgroup().getExpenseMetadataId();
		Long expenseReportMetadataId = expense.getExpenseReport().getExpenseMetadataId();

		if (!expenseMetadataId.equals(expenseReportMetadataId)) {
			return new ExpenseValidationStatusViewModel(false, "Expense Subgroup doesn't belong to the Expense Report's Metadata");
		}
		logger.trace("validateSubgroup: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateMobilityDescriptor(Expense expense) {
		if (!expense.getExpenseSubgroup().isMobilityDescriptorApplicable()) {
			logger.info("ExpenseValidationStatusViewModel: Mobility descriptor is not applicable to this expense");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (expense.getExpenseSubgroup().isMobilityDescriptorApplicable()
				&& StaticDataRegistry.isNullOrEmptyOrWhitespace(expense.getMobilityDescriptor())) {
			logger.info("ExpenseValidationStatusViewModel: Mobility descriptor is not found in this expense");
			return new ExpenseValidationStatusViewModel(false, "Mobility descriptor is missing");
		}

		if (expense.getExpenseSubgroup().isMobilityDescriptorApplicable()
				&& !StaticDataRegistry.MOBILITY_DESCRIPTOR_VALUE.contains(expense.getMobilityDescriptor())) {
			logger.info("ExpenseValidationStatusViewModel: Mobility descriptor {} is not allowed",
					expense.getMobilityDescriptor());
			return new ExpenseValidationStatusViewModel(false,
					"Invalid mobility descriptor: " + expense.getMobilityDescriptor());
		}

		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateTransportDescriptor(Expense expense) {
		if (!expense.getExpenseSubgroup().isTransportDescriptorApplicable()) {
			logger.info("ExpenseValidationStatusViewModel: Transport descriptor is not applicable to this expense");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (expense.getExpenseSubgroup().isTransportDescriptorApplicable()
				&& StaticDataRegistry.isNullOrEmptyOrWhitespace(expense.getTransportDescriptor())) {
			logger.info("ExpenseValidationStatusViewModel: Transport descriptor is not found in this expense");
			return new ExpenseValidationStatusViewModel(false, "Transport descriptor is missing");
		}

		if (expense.getExpenseSubgroup().isTransportDescriptorApplicable()
				&& !StaticDataRegistry.TRANSPORT_DESCRIPTOR_VALUE.contains(expense.getTransportDescriptor())) {
			logger.info("ExpenseValidationStatusViewModel: Transport descriptor {} is not allowed",
					expense.getMobilityDescriptor());
			return new ExpenseValidationStatusViewModel(false,
					"Invalid transport descriptor: " + expense.getTransportDescriptor());
		}

		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateTravelDescriptor(Expense expense) {
		if (!expense.getExpenseSubgroup().isTravelDescriptorApplicable()) {
			logger.info("ExpenseValidationStatusViewModel: Travel descriptor is not applicable to this expense");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (expense.getExpenseSubgroup().isTravelDescriptorApplicable()
				&& StaticDataRegistry.isNullOrEmptyOrWhitespace(expense.getTravelDescriptor())) {
			logger.info("ExpenseValidationStatusViewModel: Travel descriptor is not found in this expense");
			return new ExpenseValidationStatusViewModel(false, "Travel descriptor is missing");
		}

		if (expense.getExpenseSubgroup().isTravelDescriptorApplicable()
				&& !StaticDataRegistry.TRAVEL_DESCRIPTOR_VALUE.contains(expense.getTravelDescriptor())) {
			logger.info("ExpenseValidationStatusViewModel: Travel descriptor {} is not allowed",
					expense.getTravelDescriptor());
			return new ExpenseValidationStatusViewModel(false,
					"Invalid travel descriptor: " + expense.getTravelDescriptor());
		}

		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateClaimAndApplicableAmounts(Expense expense) {
		if (expense.getClaimAmount() > expense.getApplicableAmount()) {
			logger.info("validateClaimAndApplicableAmounts: Claim amount is more than bill amount");
			return new ExpenseValidationStatusViewModel(false, "Claim amount should not exceed the bill amount");
		}

		logger.trace("validateClaimAndApplicableAmounts: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateStandardDeduction(Expense expense, ExpenseRule rule) {
		if (!expense.getExpenseSubgroup().isStandardDeductionApplicable()) {
			logger.trace(
					"validateStandardDeduction: Standard deduction is not applicable, clearing related fields if any");
			expense.setStandardDeductionRate(null);
			expense.setStandardDeductionAmount(null);
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (!expense.getStandardDeductionRate().equals(rule.getStandardDeductionRate())) {
			logger.info("validateStandardDeduction: Rate in expense and rule are not matching");
			return new ExpenseValidationStatusViewModel(false, "Standard deduction rate on expense is fudged");
		}

		double rate = expense.getStandardDeductionRate();
		double billAmount = expense.getApplicableAmount();
		double standardDeductionAmount = expense.getStandardDeductionAmount();

		if (Math.round(rate * billAmount / 100.00) != Math.round(standardDeductionAmount)) {
			logger.info("validateStandardDeduction: Computation of standard deduction amount is wrong {} vs {}",
					Math.round(rate * billAmount / 100.00), Math.round(standardDeductionAmount));
			return new ExpenseValidationStatusViewModel(false, "Standard deduction amount calculation is wrong");
		}

		double target = rule.isCanExceedLimit() ? rule.getMaximumAmount() : rule.getLimitAmount();

		double authorized = Math.ceil(expense.getApplicableAmount() - expense.getStandardDeductionAmount()) < Math
				.ceil(target) ? expense.getApplicableAmount() - expense.getStandardDeductionAmount() : target;

		if (Math.ceil(expense.getClaimAmount()) != Math.ceil(authorized)) {
			logger.info("validateStandardDeduction: Claim amount is not the same as computed: {} vs {}",
					Math.ceil(expense.getClaimAmount()), Math.ceil(authorized));
			return new ExpenseValidationStatusViewModel(false,
					"Claim amount calculation (after standard deduction) is wrong");
		}

		logger.trace("validateStandardDeduction: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validatePerDiem(Expense expense, ExpenseRule rule) {
		if (!rule.isPerDiemAllowed()) {
			logger.trace("validatePerDiem: Per diem doesn't apply to this expense");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (expense.getStartDate() != null && expense.getEndDate() != null) {
			Period period = Period.between(expense.getStartDate(), expense.getEndDate());
			int nDays = period.getDays() + 1;
			logger.trace("validatePerDiem: Per diem should be calculated for {} days at {} / day", nDays,
					rule.getPerDiemAmount());
			if (Math.ceil(expense.getClaimAmount()) == Math.ceil(rule.getPerDiemAmount() * nDays)) {
				logger.trace("validatePerDiem: Per diem for {} days found correct", nDays);
				return new ExpenseValidationStatusViewModel(true, null);
			} else {
				return new ExpenseValidationStatusViewModel(false,
						"Claim amount should be calculated according to per diem");
			}
		}

		if (expense.getApplicableAmount() != expense.getClaimAmount()
				|| !rule.getPerDiemAmount().equals(expense.getClaimAmount())) {
			logger.info("validatePerDiem: Mismatch in amounts; bill: {}, claim: {}, per-diem: {}",
					expense.getApplicableAmount(), expense.getClaimAmount(), rule.getPerDiemAmount());
			return new ExpenseValidationStatusViewModel(false, "Claim amount should be the per diem amount");
		}

		logger.trace("validatePerDiem: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateMerchantDetails(Expense expense) {
		if (!expense.getExpenseSubgroup().isMerchantRequired()) {
			logger.trace(
					"validateMerchantDetails: Merchant details doesn't apply to this expense; clearing related fields if any");
			expense.setMerchantDetails(null);
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (StaticDataRegistry.isNullOrEmptyOrWhitespace(expense.getMerchantDetails())) {
			logger.info("validateSourceLocation: Null, empty, or whitespace provided for merchant details");
			return new ExpenseValidationStatusViewModel(false, "Merchant detail is missing or invalid");
		}

		logger.trace("validateSourceLocation: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateSourceLocation(Expense expense) {
		if (!expense.getExpenseSubgroup().isSourceLocationApplicable()) {
			logger.trace(
					"validateSourceLocation: Source location doesn't apply to this expense; clearing related fields if any");
			expense.setSourceLocation(null);
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (StaticDataRegistry.isNullOrEmptyOrWhitespace(expense.getSourceLocation())) {
			logger.info("validateSourceLocation: Null, empty, or whitespace provided for source location");
			return new ExpenseValidationStatusViewModel(false, "Source location is missing or invalid");
		}

		logger.trace("validateSourceLocation: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateDestinationLocation(Expense expense) {
		if (!expense.getExpenseSubgroup().isDestinationLocationApplicable()) {
			logger.trace(
					"validateDestinationLocation: Destination location doesn't apply to this expense; clearing related fields if any");
			expense.setDestinationLocation(null);
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (StaticDataRegistry.isNullOrEmptyOrWhitespace(expense.getDestinationLocation())) {
			logger.info("validateDestinationLocation: Null, empty, or whitespace provided for destination location");
			return new ExpenseValidationStatusViewModel(false, "Destination location is missing or invalid");
		}

		logger.trace("validateDestinationLocation: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateDateRange(Expense expense) {
		if (!expense.getExpenseSubgroup().isDateRangeApplicable()) {
			logger.trace(
					"validateDateRange: Date range doesn't apply to this expense; clearing the related fields if any");
			expense.setStartDate(null);
			expense.setEndDate(null);
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (expense.getStartDate() == null || expense.getEndDate() == null) {
			logger.info("validateDateRange: Null value detected; start: {}, end: {}", expense.getStartDate(),
					expense.getEndDate());
			return new ExpenseValidationStatusViewModel(false, "Start of end date is missing");
		}

		if (expense.getEndDate().isBefore(expense.getStartDate())) {
			logger.info("validateDateRange: End date falls before the start date");
			return new ExpenseValidationStatusViewModel(false, "End date must fall on or after start date");
		}

		if (expense.getExpenseDate().isBefore(expense.getStartDate())) {
			logger.info(
					"validateDateRange: Expense date dosn't fit into the provided date range; start: {}, expense: {}, end: {}",
					expense.getStartDate(), expense.getExpenseDate(), expense.getEndDate());
			return new ExpenseValidationStatusViewModel(false,
					"Expense date must lie in the range of start and end dates");
		}

		logger.trace("validateDateRange: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateExpenseIdentifier(Expense expense) {
		if (!expense.getExpenseSubgroup().isExpenseIdentifierApplicable()) {
			logger.trace(
					"validateExpenseIdentifier: Expense identifier doesn't apply to this expense; clearing related fields if any");
			expense.setExpenseIdentifier(null);
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (StaticDataRegistry.isNullOrEmptyOrWhitespace(expense.getExpenseIdentifier())) {
			logger.info("validateExpenseIdentifier: Null, empty, or whitespace provided for expense identifier");
			return new ExpenseValidationStatusViewModel(false, "Expense identifier is missing or invalid");
		}

		logger.trace("validateExpenseIdentifier: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateInvoiceAndApplicableAmounts(Expense expense) {
		if (expense.isApplicableAmountLesser() && expense.getApplicableAmount() >= expense.getInvoiceAmount()) {
			return new ExpenseValidationStatusViewModel(false, "Applicable amount is not invalid");
		}

		if (!expense.isApplicableAmountLesser() && expense.getApplicableAmount() != expense.getInvoiceAmount()) {
			return new ExpenseValidationStatusViewModel(false,
					"Applicable amount should be same as the invoice amount");
		}

		logger.trace("validateInvoiceAndApplicableAmounts: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateAttachments(Expense expense, ExpenseRule rule) {
		if (!rule.isInvoiceRequired()) {
			logger.trace("validateAttachments: Found valid");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (expense.getClaimAmount() >= rule.getInvoiceRequiredThreshold()
				&& StaticDataRegistry.isNullOrEmptyOrWhitespace(expense.getDocument1UploadUrl())) {
			return new ExpenseValidationStatusViewModel(false, "Invoice attachment is missing for this expense");
		}

		logger.trace("validateAttachments: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateUnitRateComputation(Expense expense, ExpenseRule rule) {
		if (!rule.isUnitRateApplicable()) {
			logger.trace(
					"validateUnitRateComputation: Unit rate doesn't apply to this expense; clearing related fields if any");
			expense.setUnitOfMeasure(null);
			expense.setUnitRate(null);
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (!rule.getUnitRate().equals(expense.getUnitRate())) {
			logger.info("validateUnitRateComputation: Unit rate in expense doesn't match with rule; {} vs {}",
					expense.getUnitRate(), rule.getUnitRate());
			return new ExpenseValidationStatusViewModel(false, "Unit rate in the expense is fudged");
		}

		if (!rule.getUnitOfMeasure().equals(expense.getUnitOfMeasure())) {
			logger.info("validateUnitRateComputation: Unit of measure in expense doesn't match with rule; {} vs {}",
					expense.getUnitOfMeasure(), rule.getUnitOfMeasure());
			return new ExpenseValidationStatusViewModel(false, "Unit of measure in the expense is fudged");
		}

		if (expense.getQuantity() == null) {
			logger.info("validateUnitRateComputation: Quantity is missing");
			return new ExpenseValidationStatusViewModel(false,
					"Expense does not have quantity (required for unit rate calculation)");
		}

		// Math.round because of floating point comparison
		double amount = Math.round(expense.getUnitRate() * expense.getQuantity());

		if (Math.round(expense.getApplicableAmount()) > amount) {
			logger.info("validateUnitRateComputation: Amount computation is not right; {} vs {}",
					Math.round(expense.getApplicableAmount()), amount);
			return new ExpenseValidationStatusViewModel(false,
					"Expense amount doesn't match with computed unit rate value");
		}

		logger.trace("validateUnitRateComputation: Found valid");
		return new ExpenseValidationStatusViewModel(true, null);
	}

	private ExpenseValidationStatusViewModel validateBillAmount(Expense expense, ExpenseRule rule) {
		if (rule.isPerDiemAllowed()) {
			logger.trace(
					"validateBillAmount: Falls into the purview of per diem calculation; bypassing limit checking");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (expense.getClaimAmount() <= rule.getLimitAmount()) {
			logger.trace("validateBillAmount: Claim amount is within the limit; found valid");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		if (rule.isCanExceedLimit() && expense.getClaimAmount() <= rule.getMaximumAmount()) {
			logger.trace("validateBillAmount: Exceed allowed; claim is within the maximum amount; found valid");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		logger.info(
				"validateBillAmount: Bill amount conflicts with limit and maximum; bill: {}, limit: {}, max: {}, exceed allowed: {}",
				expense.getApplicableAmount(), rule.getLimitAmount(), rule.getMaximumAmount(), rule.isCanExceedLimit());
		return new ExpenseValidationStatusViewModel(false, "Bill amount must abide by the rule (limit and maximum)");
	}

	private ExpenseValidationStatusViewModel validateDateRangeOverlap(Expense expense, ExpenseRule rule) {
		logger.trace("validateDateRangeOverlap: Checking if the date range overlaps with earlier expense");
		if (!expense.getExpenseSubgroup().isDateRangeApplicable()) {
			logger.trace("validateDateRangeOverlap: Date range doesn't apply; found valid");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		Optional<Expense> lastExpenseOptional = expenseRepository
				.getByDateRange(expense.getCompanyCode(), expense.getExpenseSubgroupId(), true,
						List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK), expense.getEmployeeEmail())
				.stream().filter(e -> isDateRangeOverlap(expense.getStartDate(), expense.getEndDate(), e.getStartDate(),
						e.getEndDate()))
				.findFirst();

		if (lastExpenseOptional.isEmpty()) {
			logger.trace("validateDateRangeOverlap: Date range doesn't overlap; found valid");
			return new ExpenseValidationStatusViewModel(true, null);
		}

		logger.info("validateInvoice: Overlap found; invalid");
		return new ExpenseValidationStatusViewModel(false,
				"Similar expense exists with id: " + lastExpenseOptional.get().getId());
	}

	private boolean isBetween(LocalDate date, LocalDate start, LocalDate end) {
		return (date.isAfter(start) && date.isBefore(end));
	}

	private boolean isSameDateRange(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
		return (start1.isEqual(start2) && end1.isEqual(end2));
	}

	public boolean isDateRangeOverlap(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
		boolean areSame = isSameDateRange(start1, end1, start2, end2);
		if (areSame)
			return true;

		return isBetween(start1, start2, end2) || isBetween(end1, start2, end2) || isBetween(start2, start1, end1)
				|| isBetween(end2, start1, end1);
	}

	private LocalDate[] getQuarterDelimiters(LocalDate markDate) {
		switch (markDate.getMonth()) {
		case JANUARY:
		case FEBRUARY:
		case MARCH:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.JANUARY, 1),
					LocalDate.of(markDate.getYear(), Month.MARCH, 31) };
		case APRIL:
		case MAY:
		case JUNE:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.APRIL, 1),
					LocalDate.of(markDate.getYear(), Month.JUNE, 30) };
		case JULY:
		case AUGUST:
		case SEPTEMBER:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.JULY, 1),
					LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30) };
		default:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.OCTOBER, 1),
					LocalDate.of(markDate.getYear(), Month.DECEMBER, 31) };
		}
	}

	private LocalDate[] getHalfYearDelimiters(LocalDate markDate) {
		switch (markDate.getMonth()) {
		case APRIL:
		case MAY:
		case JUNE:
		case JULY:
		case AUGUST:
		case SEPTEMBER:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.APRIL, 1),
					LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30) };
		default:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.OCTOBER, 1),
					LocalDate.of(markDate.getYear(), Month.MARCH, 31) };
		}
	}

	private LocalDate[] getYearDelimiters(LocalDate markDate) {
		switch (markDate.getMonth()) {
		case JANUARY:
		case FEBRUARY:
		case MARCH:
			return new LocalDate[] { LocalDate.of(markDate.getYear() - 1, Month.APRIL, 1),
					LocalDate.of(markDate.getYear(), Month.MARCH, 31) };
		default:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.APRIL, 1),
					LocalDate.of(markDate.getYear() + 1, Month.MARCH, 31) };
		}
	}

	private List<MetadataLimitRule> getApplicableRule(ExpenseReport report) {
		logger.trace("getApplicableRule: Finding the Metadata Limit Rules for Report with id: {}", report.getId());

		return metadataLimitRuleRepository
				.findByCompanyCodeAndExpenseMetadataId(report.getCompanyCode(), report.getExpenseMetadataId()).stream()
				.filter(r -> isRuleMatch(r, report)).collect(Collectors.toList());
	}

	private boolean isRuleMatch(MetadataLimitRule rule, ExpenseReport report) {
		boolean result = true;

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
			result = rule.getBranchCode().equals(report.getEmployeeBranch());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
			result = rule.getDepartmentCode().equals(report.getEmployeeDepartment());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
			result = rule.getCostCenterCode().equals(report.getEmployeeCostCenter());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
			result = rule.getEmployeeType().equals(report.getEmployeeType());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
			result = rule.getEmployeeGrade().equals(report.getEmployeeGrade());
			if (!result)
				return false;
		}

		return result;
	}

	private ReportLimitConsumptionViewModel getDailyLimitConsumption(Expense expense, MetadataLimitRule rule) {
		ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
		viewModel.setInterval(IntervalMarker.DAILY);
		viewModel.setLimitAmount(rule.getLimitAmount());
		viewModel.setConsumedAmount(
				expenseRepository.getExpensesOnDay(expense.getCompanyCode(), expense.getCreatingUserId(),
						expense.getExpenseReport().getExpenseMetadataId(), expense.getExpenseDate()));

		return viewModel;
	}

	private ReportLimitConsumptionViewModel getMonthlyLimitConsumption(Expense expense, MetadataLimitRule rule) {
		LocalDate start = expense.getExpenseDate().withDayOfMonth(1);
		LocalDate end = expense.getExpenseDate().withDayOfMonth(expense.getExpenseDate().lengthOfMonth());

		ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
		viewModel.setInterval(IntervalMarker.MONTHLY);
		viewModel.setLimitAmount(rule.getLimitAmount());
		viewModel.setConsumedAmount(expenseRepository.getExpensesBetween(expense.getCompanyCode(),
				expense.getCreatingUserId(), expense.getExpenseReport().getExpenseMetadataId(),
				List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED, ReportStatus.PAID,
						ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
				start, end));

		return viewModel;
	}

	private ReportLimitConsumptionViewModel getQuarterlyLimitConsumption(Expense expense, MetadataLimitRule rule) {
		LocalDate[] delimiters = getQuarterDelimiters(expense.getExpenseDate());
		ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
		viewModel.setInterval(IntervalMarker.QUARTERLY);
		viewModel.setLimitAmount(rule.getLimitAmount());
		viewModel
				.setConsumedAmount(expenseRepository.getExpensesBetween(expense.getCompanyCode(),
						expense.getCreatingUserId(), expense.getExpenseReport().getExpenseMetadataId(),
						List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED,
								ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
						delimiters[0], delimiters[1]));

		return viewModel;
	}

	private ReportLimitConsumptionViewModel getHalfYearlyLimitConsumption(Expense expense, MetadataLimitRule rule) {
		LocalDate[] delimiters = getHalfYearDelimiters(expense.getExpenseDate());
		ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
		viewModel.setInterval(IntervalMarker.HALF_YEARLY);
		viewModel.setLimitAmount(rule.getLimitAmount());
		viewModel
				.setConsumedAmount(expenseRepository.getExpensesBetween(expense.getCompanyCode(),
						expense.getCreatingUserId(), expense.getExpenseReport().getExpenseMetadataId(),
						List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED,
								ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
						delimiters[0], delimiters[1]));

		return viewModel;
	}

	private ReportLimitConsumptionViewModel getYearlyLimitConsumption(Expense expense, MetadataLimitRule rule) {
		LocalDate[] delimiters = getYearDelimiters(expense.getExpenseDate());
		ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
		viewModel.setInterval(IntervalMarker.YEARLY);
		viewModel.setLimitAmount(rule.getLimitAmount());
		viewModel
				.setConsumedAmount(expenseRepository.getExpensesBetween(expense.getCompanyCode(),
						expense.getCreatingUserId(), expense.getExpenseReport().getExpenseMetadataId(),
						List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED,
								ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
						delimiters[0], delimiters[1]));

		return viewModel;
	}

	private boolean isParentExpenseReportSubmittable(ExpenseReport report) {
		return report.getReportStatus() == ReportStatus.DRAFT || report.getReportStatus() == ReportStatus.SENT_BACK;
	}
}
