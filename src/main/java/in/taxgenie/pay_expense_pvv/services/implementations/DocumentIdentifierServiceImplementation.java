package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.DocumentIdentifier;
import in.taxgenie.pay_expense_pvv.entities.ExpenseMetadata;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentIdentifierRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentIdentifierService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.Optional;

@Service
public class DocumentIdentifierServiceImplementation implements IDocumentIdentifierService {
    private final IDocumentIdentifierRepository repository;
    private final Logger logger;

    public DocumentIdentifierServiceImplementation(
            IDocumentIdentifierRepository repository
    ) {
        this.repository = repository;
        this.logger = LoggerFactory.getLogger(DocumentIdentifierServiceImplementation.class);
    }

    @Override
    public String processAndGetIdentifier(
            ExpenseMetadata metadata,
            int year,
            int month,
            IAuthContextViewModel auth
    ) {
        logger.trace("processAndGetIdentifier: Document identifier creation");
        Optional<DocumentIdentifier> result
                = repository
                .findFirstByCompanyCodeAndExpenseTypeAndYearAndMonth(
                        auth.getCompanyCode(),
                        metadata.getExpenseType(),
                        year,
                        month
                );

        return result.isEmpty() ? handleFreshEntry(metadata, auth, year, month) : handleFoundEntry(result.get());
    }

    private String handleFreshEntry(ExpenseMetadata metadata, IAuthContextViewModel auth, int year, int month) {
        logger.trace("processAndGetIdentifier: Document identifier with Company: {}, Expense Type: {}, Expense Group: {}, Year: {}, and Month: {} is not found", auth.getCompanyCode(), metadata.getExpenseType(), metadata.getExpenseGroup(), year, month);
        //  insert and return
        DocumentIdentifier entity = new DocumentIdentifier();
        entity.setCompanyCode(auth.getCompanyCode());
        entity.setCurrentIndex(StaticDataRegistry.START_CURRENT_NUMBER);
        entity.setExpenseType(metadata.getExpenseType());
        entity.setExpenseTypePrefix(metadata.getExpenseTypePrefix());
        entity.setExpenseGroup(metadata.getExpenseGroup());
        entity.setExpenseGroupPrefix(metadata.getExpenseGroupPrefix());
        entity.setYear(year);
        entity.setMonth(month);
        entity.setCreatedTimestamp(ZonedDateTime.now());

        logger.trace("processAndGetIdentifier: Saving newly created document identifier");
        DocumentIdentifier savedEntity =
                repository.saveAndFlush(entity);
        logger.trace("processAndGetIdentifier: Save successful -> Id: {}, Company: {}, Expense Type: {}, Expense Group: {}, Year: {}, and Month: {}", savedEntity.getId(), auth.getCompanyCode(), metadata.getExpenseType(), metadata.getExpenseGroup(), year, month);

        return StaticDataRegistry.getDocumentIdentifier(savedEntity);
    }

    private String handleFoundEntry(DocumentIdentifier entity) {
        logger.trace("processAndGetIdentifier: Found the identifier");
        logger.trace("processAndGetIdentifier: Incrementing current index");
        entity.setCurrentIndex(entity.getCurrentIndex() + StaticDataRegistry.CURRENT_NUMBER_INCREMENT_FACTOR);
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("processAndGetIdentifier: Saving the entity");
        DocumentIdentifier savedEntity = repository.saveAndFlush(entity);

        return StaticDataRegistry.getDocumentIdentifier(savedEntity);
    }
}
