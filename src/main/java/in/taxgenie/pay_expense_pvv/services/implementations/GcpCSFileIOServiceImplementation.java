package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cloud.GcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.cloud.GcpCSProvider;
import in.taxgenie.pay_expense_pvv.entities.Expense;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IPdfSanitizerService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.pdfHandling.SanitizationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;
import java.util.function.BiConsumer;

@Service
@Profile({"local", "tg-internal-gcp", "ses-gcp"})
public class GcpCSFileIOServiceImplementation implements IFileIOService {
    private final GcpCSFileIOProvider gsFileIOProvider;
    private final GcpCSProvider gsProvider;

    private final IExpenseRepository expenseRepository;
    private final IPdfSanitizerService pdfSanitizerService;
    private final Logger logger;

    public GcpCSFileIOServiceImplementation(GcpCSFileIOProvider gsFileIOProvider, GcpCSProvider gsProvider, IExpenseRepository expenseRepository, IPdfSanitizerService pdfSanitizerService) {
        this.gsFileIOProvider = gsFileIOProvider;
        this.gsProvider = gsProvider;
        this.expenseRepository = expenseRepository;
        this.pdfSanitizerService = pdfSanitizerService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }


    @Override
    public void upload(MultipartFile file, String identifier, int index, Expense expense) {
        logger.debug("upload: Starting upload for expenseId={} index={}", expense.getId(), index);
        if (file.isEmpty()) {
            logger.warn("upload: Empty file for expenseId={} index={}", expense.getId(), index);
            throw new DomainInvariantException("File is empty");
        }

        switch (index) {
            case 1:
                handleDocumentUpload(file, identifier, expense, (e, req) -> {
                    e.setDocument1UploadMarker(identifier);
                    e.setDocument1UploadUuid(req.uuid);
                    e.setDocument1UploadContentType(req.contentType);
                    e.setDocument1UploadUrl(req.key);
                });
                break;
            case 2:
                handleDocumentUpload(file, identifier, expense, (e, req) -> {
                    e.setDocument2UploadMarker(identifier);
                    e.setDocument2UploadUuid(req.uuid);
                    e.setDocument2UploadContentType(req.contentType);
                    e.setDocument2UploadUrl(req.key);
                });
                break;
            case 3:
                handleDocumentUpload(file, identifier, expense, (e, req) -> {
                    e.setDocument3UploadMarker(identifier);
                    e.setDocument3UploadUuid(req.uuid);
                    e.setDocument3UploadContentType(req.contentType);
                    e.setDocument3UploadUrl(req.key);
                });
                break;
            default:
                logger.error("upload: Invalid document index {} for expense {}", index, expense.getId());
                throw new DomainInvariantException("Invalid document index: " + index);
        }
    }

    private void handleDocumentUpload(
            MultipartFile file,
            String identifier,
            Expense expense,
            BiConsumer<Expense, UploadRequest> applyMetadata
    ) {
        // Delete old version if any
        String existingUrl;
        switch (identifier) {
            case "document1":
                existingUrl = expense.getDocument1UploadUrl();
                break;
            case "document2":
                existingUrl = expense.getDocument2UploadUrl();
                break;
            case "document3":
                existingUrl = expense.getDocument3UploadUrl();
                break;
            default:
                existingUrl = null;
        }

        if (existingUrl != null) {
            gsFileIOProvider.deleteFile(existingUrl);
            logger.debug("upload: Deleted old file {} for expenseId={}", existingUrl, expense.getId());
        }

        // Prepare upload request
        UploadRequest req;
        try {
            req = buildUploadRequest(file, expense);
        } catch (IOException ex) {
            logger.error("upload: Failed to prepare bytes for expenseId={} marker={}: {}",
                    expense.getId(), identifier, ex.getMessage(), ex);
            throw new RuntimeException("Failed to prepare file for upload", ex);
        }

        // build the sanitized MultipartFile
        MultipartFile sanitized = new MockMultipartFile(
                // field name (can be anything, not used by your provider)
                "file",
                // preserve the original filename if you like:
                file.getOriginalFilename(),
                // use the contentType you determined
                req.contentType,
                // and the sanitized bytes
                req.bytes
        );

        // Perform upload
        logger.debug("upload: Uploading {} to bucket {}", req.key, gsProvider.getBucketName());
        try {
            gsFileIOProvider.uploadFile(req.key, sanitized);
        } catch (IOException ex) {
            logger.error("upload: GCP upload failed for key {}: {}", req.key, ex.getMessage(), ex);
            throw new RuntimeException("Failed to upload file to GCP", ex);
        }

        // Apply metadata to entity
        applyMetadata.accept(expense, req);

        // Persist once
        expenseRepository.saveAndFlush(expense);
        logger.debug("upload: Saved expenseId={} with new document", expense.getId());
    }

    private UploadRequest buildUploadRequest(MultipartFile file, Expense expense) throws IOException {
        String ct = file.getContentType();
        if (ct == null || (!ct.toLowerCase().startsWith("application/pdf")
                && !StaticDataRegistry.uploadFileContentTypeSet.contains(ct))) {
            throw new DomainInvariantException("Unsupported media type: " + ct);
        }

        byte[] raw = file.getBytes();
        byte[] bytes = raw;
        boolean stripped = false;

        if (ct.toLowerCase().startsWith("application/pdf")) {
            SanitizationResult result = pdfSanitizerService.sanitizeWithReport(raw);
            bytes   = result.getBytes();
            stripped = result.hadEmbeddedScripts();
        }

        if (stripped) {
            logger.warn("\n\nWARNING: THERE WERE EMBEDDED SCRIPTS IN PDF for expenseId={}!!!\n", expense.getId());
            // you could also write an audit record here if you wish
        }


        UUID uuid = UUID.randomUUID();
        String key = String.format("%s/%d/%d/%d/%s",
                gsProvider.getBucketName(),
                expense.getCompanyCode(),
                expense.getExpenseReportId(),
                expense.getId(),
                uuid);

        return new UploadRequest(key, bytes, ct, uuid.toString());
    }

    private static class UploadRequest {
        final String key;
        final byte[] bytes;
        final String contentType;
        final String uuid;

        UploadRequest(String key, byte[] bytes, String contentType, String uuid) {
            this.key = key;
            this.bytes = bytes;
            this.contentType = contentType;
            this.uuid = uuid;
        }
    }


    @Override
    public ResponseEntity<Resource> getAttachment1(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (expense.getDocument1UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        ByteArrayResource resource = gsFileIOProvider.downloadFile(expense.getDocument1UploadUrl());

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(expense.getDocument1UploadContentType()))
                .body(resource);
    }

    @Override
    public ResponseEntity<Resource> getAttachment2(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (expense.getDocument2UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        ByteArrayResource resource = gsFileIOProvider.downloadFile(expense.getDocument2UploadUrl());

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(expense.getDocument2UploadContentType()))
                .body(resource);
    }

    @Override
    public ResponseEntity<Resource> getAttachment3(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (expense.getDocument3UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        ByteArrayResource resource = gsFileIOProvider.downloadFile(expense.getDocument3UploadUrl());

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(expense.getDocument3UploadContentType()))
                .body(resource);
    }

    @Override
    public void delete(Expense expense) {
        if (expense.getDocument1UploadUrl() != null) {
            gsFileIOProvider.deleteFile(expense.getDocument1UploadUrl());
        }

        if (expense.getDocument2UploadUrl() != null) {
            gsFileIOProvider.deleteFile(expense.getDocument2UploadUrl());
        }

        if (expense.getDocument3UploadUrl() != null) {
            gsFileIOProvider.deleteFile(expense.getDocument3UploadUrl());
        }
    }

    @Override
    public void delete(int index, Expense expense) {
        if (index == 1 && expense.getDocument1UploadUrl() != null) {
            gsFileIOProvider.deleteFile(expense.getDocument1UploadUrl());
        }

        if (index == 2 && expense.getDocument2UploadUrl() != null) {
            gsFileIOProvider.deleteFile(expense.getDocument2UploadUrl());
        }

        if (index == 3 && expense.getDocument3UploadUrl() != null) {
            gsFileIOProvider.deleteFile(expense.getDocument3UploadUrl());
        }
    }

}
