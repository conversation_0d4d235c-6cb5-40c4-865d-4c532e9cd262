package in.taxgenie.pay_expense_pvv.services.implementations;

import com.amazonaws.services.s3.model.ObjectMetadata;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cloud.AwsS3FileIOProvider;
import in.taxgenie.pay_expense_pvv.cloud.AwsS3Provider;
import in.taxgenie.pay_expense_pvv.entities.Expense;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

@Service
@Profile({"tg-internal", "ses"})
public class AwsS3FileIOServiceImplementation implements IFileIOService {
    private final AwsS3FileIOProvider s3FileIOProvider;
    private final AwsS3Provider s3Provider;
    private final IExpenseRepository expenseRepository;
    private final Logger logger;

    public AwsS3FileIOServiceImplementation(AwsS3FileIOProvider s3FileIOProvider, AwsS3Provider s3Provider, IExpenseRepository expenseRepository) {
        this.s3FileIOProvider = s3FileIOProvider;
        this.s3Provider = s3Provider;
        this.expenseRepository = expenseRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void upload(MultipartFile file, String identifier, int index, Expense expense) {
        logger.trace("upload: Checking if the file is empty");
        if (file.isEmpty()) {
            logger.info("upload: File is empty");
            throw new DomainInvariantException("File is empty");
        }

        try {
            logger.trace("upload: Checking if the expense already has the document uploaded");

            if (index == 1) {
                handleDocument1Updload(file, identifier, expense);
            }

            if (index == 2) {
                handleDocument2Upload(file, identifier, expense);
            }

            if (index == 3) {
                handleDocument3Upload(file, identifier, expense);
            }


        } catch (IOException ioException) {
            logger.error("upload: Exception caught: cannot write: {}", ioException.getMessage());
            throw new RuntimeException("Failed to upload the file");
        }
    }

    @Override
    public ResponseEntity<Resource> getAttachment1(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (expense.getDocument1UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        InputStreamResource resource = new InputStreamResource(s3FileIOProvider.download(expense.getDocument1UploadUrl()));

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(expense.getDocument1UploadContentType()))
                .body(resource);
    }

    @Override
    public ResponseEntity<Resource> getAttachment2(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (expense.getDocument2UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        InputStreamResource resource = new InputStreamResource(s3FileIOProvider.download(expense.getDocument2UploadUrl()));

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(expense.getDocument2UploadContentType()))
                .body(resource);
    }

    @Override
    public ResponseEntity<Resource> getAttachment3(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Expense expense = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (expense.getDocument3UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        InputStreamResource resource = new InputStreamResource(s3FileIOProvider.download(expense.getDocument3UploadUrl()));

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(expense.getDocument3UploadContentType()))
                .body(resource);
    }

    @Override
    public void delete(Expense expense) {
        if (expense.getDocument1UploadUrl() != null) {
            s3FileIOProvider.delete(expense.getDocument1UploadUrl());
        }

        if (expense.getDocument2UploadUrl() != null) {
            s3FileIOProvider.delete(expense.getDocument2UploadUrl());
        }

        if (expense.getDocument3UploadUrl() != null) {
            s3FileIOProvider.delete(expense.getDocument3UploadUrl());
        }
    }

    @Override
    public void delete(int index, Expense expense) {
        if (index == 1 && expense.getDocument1UploadUrl() != null) {
            s3FileIOProvider.delete(expense.getDocument1UploadUrl());
        }

        if (index == 2 && expense.getDocument2UploadUrl() != null) {
            s3FileIOProvider.delete(expense.getDocument2UploadUrl());
        }

        if (index == 3 && expense.getDocument3UploadUrl() != null) {
            s3FileIOProvider.delete(expense.getDocument3UploadUrl());
        }
    }

    private void handleDocument1Updload(MultipartFile file, String identifier, Expense expense) throws IOException {
        if (expense.getDocument1UploadUrl() != null) {
            s3FileIOProvider.delete(expense.getDocument1UploadUrl());
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
        }

        UUID uuid = UUID.randomUUID();
        String fileName = String.format("%s/%d/%d/%d/%s", s3Provider.getRootDirectory(), expense.getCompanyCode(), expense.getExpenseReportId(), expense.getId(), uuid);

        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(file.getContentType());
        objectMetadata.setContentLength(file.getSize());

        logger.trace("upload: Uploading {} to AWS S3", fileName);
        s3FileIOProvider.upload(fileName, new ByteArrayInputStream(file.getBytes()), objectMetadata);
        logger.trace("upload: Upload to AWS S3 succeeded");

        logger.trace("upload: Setting relevant fields on the expense");
        expense.setDocument1UploadMarker(identifier);
        expense.setDocument1UploadUuid(uuid.toString());
        expense.setDocument1UploadContentType(file.getContentType());
        expense.setDocument1UploadUrl(fileName);

        logger.trace("upload: Saving the expense");
        expenseRepository.saveAndFlush(expense);

        logger.trace("upload: Save successful");
    }

    private void handleDocument2Upload(MultipartFile file, String identifier, Expense expense) throws IOException {
        if (expense.getDocument2UploadUrl() != null) {
            s3FileIOProvider.delete(expense.getDocument2UploadUrl());
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
        }

        UUID uuid = UUID.randomUUID();
        String fileName = String.format("%s/%d/%d/%d/%s", s3Provider.getRootDirectory(), expense.getCompanyCode(), expense.getExpenseReportId(), expense.getId(), uuid);

        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(file.getContentType());
        objectMetadata.setContentLength(file.getSize());

        logger.trace("upload: Uploading {} to AWS S3", fileName);
        s3FileIOProvider.upload(fileName, new ByteArrayInputStream(file.getBytes()), objectMetadata);
        logger.trace("upload: Upload to AWS S3 succeeded");

        logger.trace("upload: Setting relevant fields on the expense");
        expense.setDocument2UploadMarker(identifier);
        expense.setDocument2UploadUuid(uuid.toString());
        expense.setDocument2UploadContentType(file.getContentType());
        expense.setDocument2UploadUrl(fileName);

        logger.trace("upload: Saving the expense");
        expenseRepository.saveAndFlush(expense);

        logger.trace("upload: Save successful");
    }

    private void handleDocument3Upload(MultipartFile file, String identifier, Expense expense) throws IOException {
        if (expense.getDocument3UploadUrl() != null) {
            s3FileIOProvider.delete(expense.getDocument3UploadUrl());
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
        }

        UUID uuid = UUID.randomUUID();
        String fileName = String.format("%s/%d/%d/%d/%s", s3Provider.getRootDirectory(), expense.getCompanyCode(), expense.getExpenseReportId(), expense.getId(), uuid);

        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(file.getContentType());
        objectMetadata.setContentLength(file.getSize());

        logger.trace("upload: Uploading {} to AWS S3", fileName);
        s3FileIOProvider.upload(fileName, new ByteArrayInputStream(file.getBytes()), objectMetadata);
        logger.trace("upload: Upload to AWS S3 succeeded");

        logger.trace("upload: Setting relevant fields on the expense");
        expense.setDocument3UploadMarker(identifier);
        expense.setDocument3UploadUuid(uuid.toString());
        expense.setDocument3UploadContentType(file.getContentType());
        expense.setDocument3UploadUrl(fileName);

        logger.trace("upload: Saving the expense");
        expenseRepository.saveAndFlush(expense);

        logger.trace("upload: Save successful");
    }
}
