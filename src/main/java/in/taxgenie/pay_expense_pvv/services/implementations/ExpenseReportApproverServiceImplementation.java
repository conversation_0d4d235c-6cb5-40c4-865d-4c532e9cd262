package in.taxgenie.pay_expense_pvv.services.implementations;

import java.math.BigInteger;
import java.sql.Date;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import in.taxgenie.pay_expense_pvv.repositories.QueueV2RepositoryJpa;
import in.taxgenie.pay_expense_pvv.utils.QueueUtil;
import in.taxgenie.pay_expense_pvv.viewmodels.queue.QueueRowDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Expense;
import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.ExpenseReport;
import in.taxgenie.pay_expense_pvv.entities.ReportState;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.SortLevel;
import in.taxgenie.pay_expense_pvv.entities.StateChannel;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.MasterRecordNotFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseReportRepository;
import in.taxgenie.pay_expense_pvv.repositories.IReportStateRepository;
import in.taxgenie.pay_expense_pvv.repositories.QueueV2Repository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmailNotificationService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseReportApproverService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.AdminQueueViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.BulkApproveContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.BulkApproveLineViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportSendBackResultViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportViewModelV2;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ReportStateCreateViewModel;

import static in.taxgenie.pay_expense_pvv.utils.QueueUtil.buildPageable;
import static in.taxgenie.pay_expense_pvv.utils.QueueUtil.toFilterMap;

@Service
public class ExpenseReportApproverServiceImplementation implements IExpenseReportApproverService {
	private final IExpenseReportRepository reportRepository;
	private final IReportStateRepository stateRepository;
	private final IEmailNotificationService emailNotificationService;
	private final Logger logger;

	public ExpenseReportApproverServiceImplementation(IExpenseReportRepository reportRepository, IReportStateRepository stateRepository, IEmailNotificationService emailNotificationService) {
		this.reportRepository = reportRepository;
		this.stateRepository = stateRepository;
		this.emailNotificationService = emailNotificationService;
		this.logger = LoggerFactory.getLogger(this.getClass());
	}

	@Autowired
	private QueueV2RepositoryJpa queueRepo;      // JPQL/Criteria version

	@Transactional
	@Override
	public void approve(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
		ExpenseReport report = reportRepository
				.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseReportId())
				.orElseThrow(() -> new MasterRecordNotFoundException(
						String.format("Could not find %s with id: %d and Company Code: %d", ExpenseReport.class,
								viewModel.getExpenseReportId(), auth.getCompanyCode())));

		// List<ReportState> stateList = stateRepository.findByExpenseReportIdSorted(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));
		List<ReportState> stateList = stateRepository.findByExpenseReportIdSortedByLevel(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));

		ReportState first =
				stateList
						.stream()
						.filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED).min(Comparator.comparing(ReportState::getLevel))
						.orElseThrow(() -> new DomainInvariantException("No approvable expenses found for report: " + viewModel.getExpenseReportId()));

		ReportState last =
				stateList
						.stream()
						.filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
						.max(Comparator.comparingInt(ReportState::getLevel))
						.orElseThrow(() -> new DomainInvariantException("No approvable expenses found for report: " + viewModel.getExpenseReportId()));


		handleApproval(viewModel, auth, report, stateList, first, last);
	}

	@Transactional
	@Override
	public void approveBulk(BulkApproveContainerViewModel viewModel, IAuthContextViewModel auth) {
		for (BulkApproveLineViewModel line : viewModel.getLines()) {
			ReportStateCreateViewModel stateCreateViewModel = new ReportStateCreateViewModel();
			stateCreateViewModel.setExpenseReportId(line.getId());
			stateCreateViewModel.setStatus(ExpenseActionStatus.APPROVED);
			stateCreateViewModel.setLevel(line.getLevel());

			this.approve(stateCreateViewModel, auth);
		}
	}

	//  Withdrawn for ABFL
	@Transactional
	@Override
	public void reject(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
		logger.info("reject: Ensuring the existence of Expense record for this request");
		ExpenseReport report =
				reportRepository
						.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseReportId())
						.orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", Expense.class, viewModel.getExpenseReportId(), auth.getCompanyCode())));


		logger.trace("reject: Getting the states of this expense");
		List<ReportState> stateList = stateRepository.findByExpenseReportIdSorted(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));

		logger.trace("reject: Getting the first unactioned state");
		ReportState first =
				stateList
						.stream()
						.filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
						.findFirst()
						.orElseThrow(() -> new DomainInvariantException("No approvable expenses found for expense: " + viewModel.getExpenseReportId()));

		logger.trace("reject: Checking if the level and approver match with the ones provided in the view-model");
		if (first.getLevel() != viewModel.getLevel() || !first.getApprover().equalsIgnoreCase(auth.getUserEmail())) {
			throw new DomainInvariantException(String.format("User %s could not be found at level %d for expense: %d", auth.getUserEmail(), viewModel.getLevel(), viewModel.getExpenseReportId()));
		}

		logger.trace("reject: Setting the status to rejected and setting audit fields");
		first.setStatus(ExpenseActionStatus.REJECTED);
		first.setRemarks(viewModel.getRemarks());
		first.setActionDate(LocalDate.now());
		first.setUpdatedTimestamp(ZonedDateTime.now());

		logger.trace("reject: Setting the expense status to declined");
		report.setReportStatus(ReportStatus.DECLINED);
		report.setActionLevel(StaticDataRegistry.DECLINED_STATUS_MARKER);

		logger.trace("reject: Saving both the state and expense");
		stateRepository.saveAndFlush(first);
		reportRepository.saveAndFlush(report);
		logger.trace("reject: Save successful; exiting");
	}

	@Transactional
	@Override
	public ExpenseReportSendBackResultViewModel approverSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
		return handleAndFinalizeSendBack(viewModel, auth);
	}

	@Transactional
	@Override
	public void checkerConsent(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
		ExpenseReport report =
				reportRepository
						.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseReportId())
						.orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", Expense.class, viewModel.getExpenseReportId(), auth.getCompanyCode())));

		// List<ReportState> stateList = stateRepository.findByExpenseReportIdSorted(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));
		List<ReportState> stateList = stateRepository.findByExpenseReportIdSortedByLevel(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));
		ReportState first =
				stateList
						.stream()
						.filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
						.findFirst()
						.orElseThrow(() -> new DomainInvariantException("No approvable expenses found for expense: " + viewModel.getExpenseReportId()));

		ReportState last =
				stateList
						.stream()
						.filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
						.max(Comparator.comparingInt(ReportState::getLevel))
						.orElseThrow(() -> new DomainInvariantException("No approvable expenses found for expense: " + viewModel.getExpenseReportId()));


		handleApproval(viewModel, auth, report, stateList, first, last);
	}

	@Transactional
	@Override
	public ExpenseReportSendBackResultViewModel checkerSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
		return handleAndFinalizeSendBack(viewModel, auth);
	}

	@Override
	public List<ExpenseReportViewModel> getApproverQueue(IAuthContextViewModel auth) {
		return stateRepository
				.getApproverQueue(auth.getCompanyCode(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK), StateChannel.APPROVER, ExpenseActionStatus.UNACTIONED, auth.getUserEmail())
				.stream()
				.map(s -> {
					ExpenseReportViewModel viewModel = new ExpenseReportViewModel();
					BeanUtils.copyProperties(s.getExpenseReport(), viewModel);
					viewModel.setExpenseType(s.getExpenseReport().getExpenseMetadata().getExpenseType());
					viewModel.setExpenseGroup(s.getExpenseReport().getExpenseMetadata().getExpenseGroup());
//                    viewModel.setDelegationRemarks(s.getDelegationRemarks());  // Not being used in the front-end
//                    viewModel.setDefaultApproverRemarks(s.getDefaultTriggerRemarks()); . // Not being used in the front-end
					viewModel.setCurrentApproverEmployeeCode(s.getApproverEmployeeCode());
					viewModel.setCurrentApproverFirstName(s.getApproverFirstName());
					viewModel.setCurrentApproverLastName(s.getApproverLastName());
					return viewModel;
				})
				.collect(Collectors.toList());
	}

	@Transactional(readOnly = true)
	@Override
	public ExpenseReportViewModelV2<ExpenseReportViewModel> getApproverQueueV2(
			IAuthContextViewModel auth, QueueFilterViewModel vm) {

		return getApproverQueueData(
				StateChannel.APPROVER, auth, vm);
	}

	@Override
	public List<ExpenseReportViewModel> getCheckerQueue(IAuthContextViewModel auth) {
		return stateRepository
				.getApproverQueue(auth.getCompanyCode(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK),
						StateChannel.CHECKER, ExpenseActionStatus.UNACTIONED, auth.getUserEmail())
				.stream().map(s -> {
					ExpenseReportViewModel viewModel = new ExpenseReportViewModel();
					BeanUtils.copyProperties(s.getExpenseReport(), viewModel);
					viewModel.setExpenseType(s.getExpenseReport().getExpenseMetadata().getExpenseType());
					viewModel.setExpenseGroup(s.getExpenseReport().getExpenseMetadata().getExpenseGroup());
//                    viewModel.setDelegationRemarks(s.getDelegationRemarks());
					viewModel.setCurrentApproverEmployeeCode(s.getApproverEmployeeCode());
					viewModel.setCurrentApproverFirstName(s.getApproverFirstName());
					viewModel.setCurrentApproverLastName(s.getApproverLastName());
					return viewModel;
				}).collect(Collectors.toList());
	}

	@Transactional(readOnly = true)
	@Override
	public ExpenseReportViewModelV2<ExpenseReportViewModel> getCheckerQueueV2(
			IAuthContextViewModel auth, QueueFilterViewModel vm) {

		return getApproverQueueData(
				StateChannel.CHECKER, auth, vm);
	}

	private ExpenseReportViewModelV2<ExpenseReportViewModel> getApproverQueueData(
			StateChannel channel, IAuthContextViewModel auth, QueueFilterViewModel vm) {

		Pageable page = buildPageable(vm);

		Page<QueueRowDTO> dtoPage =
				queueRepo.findCheckerQueue(
						auth.getCompanyCode(),
						channel,
						ExpenseActionStatus.UNACTIONED,
						auth.getUserEmail(),
						List.of(ReportStatus.SUBMITTED,
								ReportStatus.SENT_BACK),
						page,
						toFilterMap(vm));

		/* 1.  One-liner that uses the bulk helper */
		List<ExpenseReportViewModel> data = QueueUtil.mapPage(dtoPage);


		ExpenseReportViewModelV2<ExpenseReportViewModel> out =
				new ExpenseReportViewModelV2<>();
		out.setPages(dtoPage.getTotalPages());
		out.setTotal((int) dtoPage.getTotalElements());
		out.setData(data);
		return out;
	}

	@Override
	@Transactional(readOnly = true)
	public List<ExpenseReportViewModel> getAdminQueue(IAuthContextViewModel auth) {

		List<AdminQueueViewModel> res = reportRepository.getAdminQueueV2(auth.getCompanyCode());
		List<ExpenseReportViewModel> formattedResponse = res.stream().map(e -> {
			ExpenseReportViewModel viewModel = new ExpenseReportViewModel();
			BeanUtils.copyProperties(e, viewModel);
			return viewModel;
		}).collect(Collectors.toList());

		return formattedResponse;
	}

	@Transactional(readOnly = true)
	@Override
	public ExpenseReportViewModelV2<ExpenseReportViewModel> getAdminQueueV2(
			IAuthContextViewModel auth, QueueFilterViewModel vm) {

		Pageable page = buildPageable(vm);

		Page<QueueRowDTO> dtoPage =
				queueRepo.findAdminQueue(
						auth.getCompanyCode(),
						page,
						toFilterMap(vm));

		/* 1.  One-liner that uses the bulk helper */
		List<ExpenseReportViewModel> data = QueueUtil.mapPage(dtoPage);

		ExpenseReportViewModelV2<ExpenseReportViewModel> out =
				new ExpenseReportViewModelV2<>();
		out.setPages(dtoPage.getTotalPages());
		out.setTotal((int) dtoPage.getTotalElements());
		out.setData(data);
		return out;
	}

	private String getStatus(String matchStatus) {
		String status = "DRAFT";
		for (ReportStatus value : ReportStatus.values()) {
			if (value.name().contains(matchStatus)) {
				status = value.name();
				break;
			}
		}
		return status;
	}


	@Override
	public List<ExpenseReportViewModel> getSimilarExpenses(long reportId, IAuthContextViewModel auth) {
		ExpenseReport referenceReport = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), reportId)
				.orElseThrow(() -> new RecordNotFoundException("Could not find the report: " + reportId));

		if (referenceReport.getStartDate() == null || referenceReport.getEndDate() == null) {
			return new ArrayList<>();
		}

		return reportRepository
				.getReportsByMetadataUserStatus(auth.getCompanyCode(), referenceReport.getCreatingUserId(),
						referenceReport.getExpenseMetadataId(),
						List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED,
								ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM))
				.stream().filter(r -> r.getId() != referenceReport.getId())
				// Commented date check on 6 July 2022
				// .filter(r -> r.getStartDate().getMonth() ==
				// referenceReport.getStartDate().getMonth()
				// || r.getEndDate().getMonth() == referenceReport.getEndDate().getMonth())
				.filter(r -> r.getStartDate().isAfter(referenceReport.getStartDate().minusDays(1))
						&& r.getEndDate().isBefore(referenceReport.getEndDate().plusDays(1)))
				.map(r -> {
					ExpenseReportViewModel viewModel = new ExpenseReportViewModel();
					BeanUtils.copyProperties(r, viewModel);
					viewModel.setExpenseType(r.getExpenseMetadata().getExpenseType());
					viewModel.setExpenseGroup(r.getExpenseMetadata().getExpenseGroup());
					return viewModel;
				}).collect(Collectors.toList());
	}

	private void handleApproval(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth, ExpenseReport report, List<ReportState> stateList, ReportState first, ReportState last) {
		if (first.getLevel() != viewModel.getLevel() || !first.getApprover().equalsIgnoreCase(auth.getUserEmail())) {
			throw new DomainInvariantException(String.format("User %s could not be found at level %d for expense: %d", auth.getUserEmail(), viewModel.getLevel(), viewModel.getExpenseReportId()));
		}

		first.setStatus(ExpenseActionStatus.APPROVED);
		first.setRemarks(viewModel.getRemarks());
		first.setActionDate(LocalDate.now());
		first.setUpdatedTimestamp(ZonedDateTime.now());
		stateRepository.save(first);
		report.setActionLevel(report.getActionLevel() + StaticDataRegistry.APPROVAL_INCREMENT_FACTOR);

		ReportState currentState = first;

		Optional<ReportState> nextStateOptional = stateList
				.stream()
				.filter(s -> s.getLevel() == report.getActionLevel() && s.getStatus() == ExpenseActionStatus.UNACTIONED)
				.findFirst();

		while (nextStateOptional.isPresent()) {

//            if (currentState.equals(last)) {
//                report.setReportStatus(ReportStatus.ACCEPTED);
//                report.setActionLevel(StaticDataRegistry.ACCEPTED_STATUS_MARKER);
//
//                report.setActionStatus(ExpenseActionStatus.APPROVED);
//                report.setCurrentApproverFirstName(null);
//                report.setCurrentApproverLastName(null);
//                report.setCurrentApproverEmployeeCode(null);
//                report.setSendBackRemarks(null);
//            } else {
			if (nextStateOptional.get().getApprover().equals(currentState.getApprover())) {
				ReportState duplicateApprover = nextStateOptional.get();
				duplicateApprover.setStatus(ExpenseActionStatus.APPROVED);
				duplicateApprover.setRemarks(StaticDataRegistry.DUPLICATE_APPROVER_MESSAGE);
				duplicateApprover.setActionDate(LocalDate.now());
				duplicateApprover.setUpdatedTimestamp(ZonedDateTime.now());

//                    stateRepository.saveAndFlush(duplicateApprover);
				stateRepository.save(duplicateApprover);

				report.setActionLevel(report.getActionLevel() + StaticDataRegistry.APPROVAL_INCREMENT_FACTOR);

				currentState = nextStateOptional.get();
				nextStateOptional = stateList
						.stream()
						.filter(s -> s.getLevel() == report.getActionLevel() && s.getStatus() == ExpenseActionStatus.UNACTIONED)
						.findFirst();

			} else {
				report.setActionStatus(ExpenseActionStatus.UNACTIONED);
				report.setCurrentApproverFirstName(nextStateOptional.get().getApproverFirstName());
				report.setCurrentApproverLastName(nextStateOptional.get().getApproverLastName());
				report.setCurrentApproverEmployeeCode(nextStateOptional.get().getApproverEmployeeCode());
				report.setSendBackRemarks(null);
				break;
			}
		}
//        }

		if (currentState.equals(last)) {
			report.setReportStatus(ReportStatus.ACCEPTED);
			report.setActionLevel(StaticDataRegistry.ACCEPTED_STATUS_MARKER);

			report.setActionStatus(ExpenseActionStatus.APPROVED);
			report.setCurrentApproverFirstName(null);
			report.setCurrentApproverLastName(null);
			report.setCurrentApproverEmployeeCode(null);
			report.setSendBackRemarks(null);
		}

		stateRepository.flush();
		reportRepository.saveAndFlush(report);

		emailNotificationService.transmitApprovedNotification(report);
	}

	private ExpenseReportSendBackResultViewModel handleAndFinalizeSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
		ExpenseReport report =
				reportRepository
						.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseReportId())
						.orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", Expense.class, viewModel.getExpenseReportId(), auth.getCompanyCode())));

		List<ReportState> stateList = stateRepository.findByExpenseReportIdSorted(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED));

		return handleSendBack(viewModel, auth, report, stateList);
	}

	private ExpenseReportSendBackResultViewModel handleSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth, ExpenseReport report, List<ReportState> stateList) {
		ReportState first = processSendBack(viewModel, auth, report, stateList);
		return finalizeSendBack(viewModel, report, first);
	}

	private ReportState processSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth, ExpenseReport report, List<ReportState> stateList) {
		ReportState first =
				stateList
						.stream()
						.filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
						.min(Comparator.comparing(ReportState::getLevel))
						.orElseThrow(() -> new DomainInvariantException("No approvable expenses found for expense: " + viewModel.getExpenseReportId()));

		if (first.getLevel() != viewModel.getLevel() || !first.getApprover().equalsIgnoreCase(auth.getUserEmail())) {
			throw new DomainInvariantException(String.format("User %s could not be found at level %d for expense: %d", auth.getUserEmail(), viewModel.getLevel(), viewModel.getExpenseReportId()));
		}

		first.setStatus(ExpenseActionStatus.SENT_BACK);
		first.setRemarks(viewModel.getRemarks());
		first.setActionDate(LocalDate.now());
		first.setUpdatedTimestamp(ZonedDateTime.now());

		report.setReportStatus(ReportStatus.SENT_BACK);
		report.setActionLevel(StaticDataRegistry.FIRST_LEVEL);
		return first;
	}

	private ExpenseReportSendBackResultViewModel finalizeSendBack(ReportStateCreateViewModel viewModel, ExpenseReport report, ReportState first) {
		report.setContainsSentBack(true);

		report.setActionStatus(ExpenseActionStatus.SENT_BACK);
		report.setCurrentApproverFirstName(null);
		report.setCurrentApproverLastName(null);
		report.setCurrentApproverEmployeeCode(null);
		report.setSendBackRemarks(viewModel.getRemarks());

		stateRepository.saveAndFlush(first);
		reportRepository.saveAndFlush(report);

		emailNotificationService.transmitSendBackNotification(report);

		ExpenseReportSendBackResultViewModel resultViewModel = new ExpenseReportSendBackResultViewModel();
		resultViewModel.setDocumentNumber(report.getDocumentIdentifier());
		resultViewModel.setCreatorFirstName(report.getFirstName());
		resultViewModel.setCreatorLastName(report.getLastName());

		return resultViewModel;
	}


}