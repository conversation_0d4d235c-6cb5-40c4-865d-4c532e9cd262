package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.ExpenseReport;
import in.taxgenie.pay_expense_pvv.entities.ReportState;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseReportRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmailNotificationService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.comeng.ApprovalPendingLineViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.comeng.ApprovalPendingReminderEmailContainerViewModel;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Profile("prod")
@Component
public class ReminderEmailNotificationScheduledRunner {
    private final IExpenseReportRepository reportRepository;
    private final IEmailNotificationService emailNotificationService;


    public ReminderEmailNotificationScheduledRunner(
            IExpenseReportRepository reportRepository,
            IEmailNotificationService emailNotificationService
    ) {
        this.reportRepository = reportRepository;
        this.emailNotificationService = emailNotificationService;
    }

    //  Spring uses cron expression of 6 units. Starting unit is second, which is absent in traditional cron.
    //  https://docs.spring.io/spring-framework/docs/current/reference/html/integration.html#scheduling-cron-expression
    @Scheduled(cron = "0 0 0 * * *")
    @Transactional(propagation = Propagation.REQUIRED, readOnly = true, noRollbackFor = Exception.class)
    public void dispatchSentBackEmailReminders() {
        List<ExpenseReport> sentBackReports = reportRepository.findAllByReportStatus(ReportStatus.SENT_BACK);

        for (ExpenseReport report : sentBackReports) {
            if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(report.getEmployeeEmail())) {
                emailNotificationService.transmitSentBackReminderNotification(report);
            }
        }
    }

    @Scheduled(cron = "0 0 2 * * *")
    @Transactional(propagation = Propagation.REQUIRED, readOnly = true, noRollbackFor = Exception.class)
    public void dispatchApprovalPendingEmailReminders() {
        reportRepository
                .findAllByReportStatus(ReportStatus.SUBMITTED)
                .stream()
                .map(r -> r.getReportStates()
                        .stream()
                        .filter(s -> {
                            return s.getStatus() == ExpenseActionStatus.UNACTIONED;
                        }).min(Comparator.comparing(ReportState::getLevel))
                )
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.groupingBy(ReportState::getApprover, Collectors.mapping(s -> {
                    ApprovalPendingLineViewModel viewModel = new ApprovalPendingLineViewModel();
                    viewModel.setDefaultRemarks(s.getDefaultTriggerRemarks());
                    viewModel.setDeviationRemarks(s.getDeviationRemarks());
                    viewModel.setLevel(s.getLevel());
                    viewModel.setLineMarker(String.format("%s | %s", s.getExpenseReport().getExpenseMetadata().getExpenseType(), s.getExpenseReport().getExpenseMetadata().getExpenseGroup()));
                    viewModel.setEmployeeName(String.format("%s %s", s.getExpenseReport().getFirstName(), s.getExpenseReport().getLastName()));
                    viewModel.setEndDate(s.getExpenseReport().getEndDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
                    viewModel.setStartDate(s.getExpenseReport().getStartDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
                    viewModel.setReportClaimAmount(String.format("? %.2f", s.getExpenseReport().getReportClaimAmount()));
                    viewModel.setApproverName(String.format("%s %s", s.getApproverFirstName(), s.getApproverLastName()));

                    return viewModel;
                }, Collectors.toList())))
                .entrySet()
                .stream()
                .map(es -> new ApprovalPendingReminderEmailContainerViewModel(
                                es.getValue().get(es.getValue().size() - 1).getApproverName().startsWith("null")
                                        ? "Approver"
                                        : es.getValue()
                                        .get(es.getValue().size() - 1)
                                        .getApproverName(),
                                es.getKey(),
                                es.getValue()
                        )
                )
                .collect(Collectors.toList())
                .forEach(emailNotificationService::transmitApprovalReminderNotification);
    }
}
