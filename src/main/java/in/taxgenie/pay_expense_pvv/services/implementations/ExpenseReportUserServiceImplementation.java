package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.DuplicateRecordFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.repositories.implementations.QueueV2RepositoryQueryDslImpl;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentIdentifierService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmailNotificationService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseReportUserService;
import in.taxgenie.pay_expense_pvv.utils.IPropertyCopier;
import in.taxgenie.pay_expense_pvv.utils.QueueUtil;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportSubmitResultViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseReportViewModelV2;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseRuleViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.ExpenseValidationAggregates;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.IExpenseValidationAggregates;
import in.taxgenie.pay_expense_pvv.viewmodels.queue.QueueRowDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.sql.Date;
import java.time.LocalDate;
import java.time.Month;
import java.time.Period;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static in.taxgenie.pay_expense_pvv.utils.QueueUtil.buildPageable;
import static in.taxgenie.pay_expense_pvv.utils.QueueUtil.toFilterMap;

@Service
public class ExpenseReportUserServiceImplementation implements IExpenseReportUserService {
	private final IExpenseReportRepository reportRepository;
	private final IMetadataLimitRuleRepository ruleRepository;
	private final IExpenseRepository expenseRepository;
	private final IExpenseMetadataRepository metadataRepository;
	private final IApprovalDefinitionRepository definitionRepository;
	private final IApprovalDelegationRepository delegationRepository;
	private final IReportStateRepository stateRepository;
	private final IApproverRepository approverRepository;
	private final IEmployeeMasterDataService employeeService;
	private final IDocumentIdentifierService documentIdentifierService;
	private final IPropertyCopier<ExpenseReport, IEmployeeViewModel> employeeMasterDataCopier;
	private final IEmailNotificationService emailNotificationService;
	private final Logger logger;

	private long saveTimeForReportState;

	@Autowired
	private QueueV2RepositoryJpa queueRepo;      // JPQL/Criteria version

	public ExpenseReportUserServiceImplementation(IExpenseReportRepository reportRepository,
			IMetadataLimitRuleRepository ruleRepository, IExpenseRepository expenseRepository,
			IExpenseMetadataRepository metadataRepository, IApprovalDefinitionRepository definitionRepository,
			IApprovalDelegationRepository delegationRepository, IReportStateRepository stateRepository,
			IApproverRepository approverRepository, IEmployeeMasterDataService employeeService,
			IDocumentIdentifierService documentIdentifierService,
			IPropertyCopier<ExpenseReport, IEmployeeViewModel> employeeMasterDataCopier,
			IEmailNotificationService emailNotificationService) {
		this.reportRepository = reportRepository;
		this.ruleRepository = ruleRepository;
		this.expenseRepository = expenseRepository;
		this.metadataRepository = metadataRepository;
		this.definitionRepository = definitionRepository;
		this.delegationRepository = delegationRepository;
		this.stateRepository = stateRepository;
		this.approverRepository = approverRepository;
		this.employeeService = employeeService;
		this.documentIdentifierService = documentIdentifierService;
		this.employeeMasterDataCopier = employeeMasterDataCopier;
		this.emailNotificationService = emailNotificationService;
		this.logger = LoggerFactory.getLogger(this.getClass());
	}

	@Override
	public ExpenseReportViewModel create(long metadataId, IAuthContextViewModel auth) {
		ExpenseMetadata metadata = metadataRepository.findByCompanyCodeAndId(auth.getCompanyCode(), metadataId)
				.orElseThrow(() -> new RecordNotFoundException("Could not find the expense metadata: " + metadataId));

		if (metadata.isFrozen()) {
			throw new DomainInvariantException("Metadata is frozen");
		}

		if (reportRepository.countByCompanyCodeAndReportStatusAndCreatingUserId(auth.getCompanyCode(),
				ReportStatus.DRAFT, auth.getUserId()) > StaticDataRegistry.MAX_DRAFT_REPORTS) {
			throw new DomainInvariantException("Maximum allowed draft report limit exceeded");
		}

		ExpenseReport report = new ExpenseReport();

		logger.info("create: Fetching the employee details");
		IEmployeeViewModel employeeViewModel = employeeService.getEmployeeMasterData(auth.getUserEmail(), auth);

		if (employeeViewModel.getGlMainAccountCode() == null || employeeViewModel.getGlMainAccountCode().isEmpty()) {
			throw new DomainInvariantException("GL code is required to make expense.");
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(metadata.getApplicableGender())
				&& !metadata.getApplicableGender().equalsIgnoreCase(employeeViewModel.getGender())) {
			throw new DomainInvariantException("This type/group is gender restricted");
		}

		logger.info("create: Copying employee details");
		employeeMasterDataCopier.copyProperties(report, employeeViewModel);

		report.setCompanyCode(auth.getCompanyCode());
		report.setCreatedDate(LocalDate.now());
		report.setCreatingUserId(auth.getUserId());
		report.setCreatedTimestamp(ZonedDateTime.now());
		report.setEmployeeEmail(auth.getUserEmail().trim());
		report.setCompanyCode(metadata.getCompanyCode());
		report.setActionLevel(StaticDataRegistry.FIRST_LEVEL);
		report.setReportTitle(String.format("%s/%s Expense", metadata.getExpenseType(), metadata.getExpenseGroup()));

		report.setExpenseMetadata(metadata);
		report.setExpenseMetadataId(metadataId);
		metadata.getExpenseReports().add(report);

		reportRepository.saveAndFlush(report);
		metadataRepository.saveAndFlush(metadata);

		report.setDocumentIdentifier(documentIdentifierService.processAndGetIdentifier(metadata,
				LocalDate.now().getYear(), LocalDate.now().getMonthValue(), auth));

		reportRepository.saveAndFlush(report);

		return getViewModel(report);
	}

	@Override
	public ExpenseReportViewModel getById(long id, IAuthContextViewModel auth) {
		if (auth.getAuthorities().contains(StaticDataRegistry.ADMIN_ROLE_MARKER)) {
			return getViewModel(reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
					.orElseThrow(() -> new RecordNotFoundException("Could not find report with id: " + id)));
		}

		if (auth.getAuthorities().contains(StaticDataRegistry.APPROVER_ROLE_MARKER)
				|| auth.getAuthorities().contains(StaticDataRegistry.CHECKER_ROLE_MARKER)) {
			Optional<ExpenseReport> reportOptional = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id);
			if (reportOptional.isPresent()) {
				boolean isApprover = reportOptional.stream().anyMatch(r -> r.getReportStates().stream()
						.anyMatch(s -> s.getApprover().equalsIgnoreCase(auth.getUserEmail())));

				if (isApprover) {
					return getViewModel(reportOptional.get());
				}
			}
		}
		return getViewModel(reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
				.orElseThrow(() -> new RecordNotFoundException("Could not find report with id: " + id)));

//        return getViewModel(reportRepository.findByCompanyCodeAndIdAndCreatingUserId(auth.getCompanyCode(), id, auth.getUserId())
//                .orElseThrow(() -> new RecordNotFoundException("Could not find report with id: " + id)));
	}

    @Override
    public void save(ExpenseReportUpdateViewModel viewModel, IAuthContextViewModel auth) {
		logger.info("Inside save expense report method for Id: {}",viewModel.getId());
        ExpenseReport report = reportRepository.findByCompanyCodeAndIdAndEmployeeEmail(auth.getCompanyCode(), viewModel.getId(), auth.getUserEmail())
                .orElseThrow(() -> new RecordNotFoundException("Could not find Expense Report with id: " + viewModel.getId()));
		if (!isSubmittable(report)) {
            throw new DomainInvariantException("Report is neither in draft or sent-back state; hence not saveable");
        }
        BeanUtils.copyProperties(viewModel, report, "expenseMetadataId", "documentIdentifier", "createdDate");
        long t1 = System.currentTimeMillis();
        logger.info(String.format("Save: Performing validation for Expense Report id: %s created by user: %s", report.getId(), auth.getUserEmail()));
        validateReport(report, viewModel);
        long t2 = System.currentTimeMillis();
        logger.info(String.format("Validating Expense report id %s created by user : %s took %s seconds", report.getId(), auth.getUserEmail(), (t2-t1)*0.001));
        logger.info(String.format("Save : Saving Expense report id: %s created by user : %s", report.getId(), auth.getUserEmail()));
		logger.info("Updated expense report in save method started");
		ExpenseReport expenseReport = reportRepository.saveAndFlush(report);
		logger.info("Updated expense report in save method for id: {} with updated time: {}",expenseReport.getId(),expenseReport.getUpdatedTimestamp());
		long t3 = System.currentTimeMillis();
        logger.info(String.format("Saving Expense report id: %s created by user : %s took %s seconds", report.getId(), auth.getUserEmail(), (t3-t1)*0.001));
    }

	@Transactional
	@Override
	public ExpenseReportSubmitResultViewModel submit(long id, IAuthContextViewModel auth, long saveTime) {
		logger.info("Inside submit expense report method for Id: {}",id);
		Optional<ReportState> checkReportState = stateRepository
				.findByCompanyCodeAndIdAndSaveTime(auth.getCompanyCode(), id, saveTime);
		if (checkReportState.isPresent()) {
			throw new DuplicateRecordFoundException("Duplicate Record found with Report with  companyId, id and saveDate: "
					+ auth.getCompanyCode() + id + saveTime);
		}
		saveTimeForReportState = saveTime;
		ExpenseReport report = reportRepository
				.findByCompanyCodeAndIdAndEmployeeEmail(auth.getCompanyCode(), id, auth.getUserEmail())
				.orElseThrow(() -> new RecordNotFoundException("Could not find Report with id: " + id));

		if (report.getExpenses().isEmpty() || report.getReportClaimAmount() == 0) {
			throw new DomainInvariantException("Report has no expenses or total claim is zero");
		}

		if (!isSubmittable(report)) {
			throw new DomainInvariantException("Report is neither in draft or sent-back state; hence not submittable");
		}

		if (report.getEndDate().isAfter(LocalDate.now())) {
			throw new DomainInvariantException("Report end date cannot be after the current date: " + LocalDate.now());
		}

		validateMetadataLimits(report);

		validateExistingExpenseDateSanity(report);

		validateReportSubmission(report);

		logger.info("submit: Checking if the report has any expense that is deviated");
		if (getDeviationStatus(report)) {
			report.setContainsDeviation(true);
			report.setDeviationRemarks("Report consists of one or more deviated expenses");
		} else {
			// May be deviated before, but not now
			report.setContainsDeviation(false);
			report.setDeviationRemarks(null);
		}

		IEmployeeViewModel employeeViewModel = employeeService.getEmployeeMasterData(auth.getUserEmail(), auth);

		if (report.getReportStatus() == ReportStatus.SENT_BACK) {
			logger.info("Report status for id {}, is sent_back",report.getId());
//            handleResubmission(report, employeeViewModel, auth, saveTime);
			attachInitialApprovers(report, report.getExpenseMetadata(), auth, employeeViewModel, true);

		}

		if (report.getReportStatus() == ReportStatus.DRAFT) {
			logger.info("submit: Attaching approvers to the report for report id : {} ",report.getId());
			attachInitialApprovers(report, report.getExpenseMetadata(), auth, employeeViewModel, false);
			report.setSubmitDate(LocalDate.now());
			logger.info("Submit date for report id :{} is : {} ",report.getId(),report.getSubmitDate());
		}

		logger.info("submit: Setting the report status as submitted");
		report.setReportStatus(ReportStatus.SUBMITTED);

		ReportState firstState = report.getReportStates().get(0);
		report.setCurrentApproverEmployeeCode(firstState.getApproverEmployeeCode());
		report.setCurrentApproverFirstName(firstState.getApproverFirstName());
		report.setCurrentApproverLastName(firstState.getApproverLastName());

		report.setActionStatus(firstState.getStatus());

		logger.info("submit: Saving the report for id {} ",report.getId());
		ExpenseReport expenseReport = reportRepository.saveAndFlush(report);
		logger.info("submit: Save successful; exiting with id: {} and updated time : {} ",expenseReport.getId(),expenseReport.getUpdatedTimestamp());
		logger.info("Submit date for report id :{} is : {} ",expenseReport.getId(),expenseReport.getSubmitDate());
		emailNotificationService.transmitSubmitNotification(report);

		ExpenseReportSubmitResultViewModel viewModel = new ExpenseReportSubmitResultViewModel();
		viewModel.setDocumentNumber(report.getDocumentIdentifier());
		viewModel.setFirstApproverFirstName(report.getReportStates().get(0).getApproverFirstName());
		viewModel.setFirstApproverLastName(report.getReportStates().get(0).getApproverLastName());

		return viewModel;
	}

	@Override
	public void revoke(long id, IAuthContextViewModel auth) {
		ExpenseReport report = reportRepository
				.findByCompanyCodeAndIdAndEmployeeEmail(auth.getCompanyCode(), id, auth.getUserEmail())
				.orElseThrow(() -> new RecordNotFoundException("Could not find Report with id: " + id));

		logger.trace("revoke: Checking if current user is the creator of the report");
		if (!report.getEmployeeEmail().trim().equalsIgnoreCase(auth.getUserEmail().trim())) {
			logger.trace("revoke: User {} cannot revoke this expense: {}", auth.getUserEmail(), report.getId());
			throw new DomainInvariantException(
					String.format("User %s cannot revoke this report: %d", auth.getUserEmail(), report.getId()));
		}

		logger.trace("revoke: Checking if report is in draft or sent-back status");
		if (report.getReportStatus() == ReportStatus.SENT_BACK || report.getReportStatus() == ReportStatus.DRAFT) {
			logger.trace("revoke: Setting the report status to revoked");
			report.setReportStatus(ReportStatus.REVOKED);
			report.setActionLevel(StaticDataRegistry.REVOKED_STATUS_MARKER);

			report.setActionStatus(ExpenseActionStatus.UNACTIONED);
			report.setCurrentApproverFirstName(null);
			report.setCurrentApproverLastName(null);
			report.setCurrentApproverEmployeeCode(null);
			report.setSendBackRemarks(null);

			logger.trace("revoke: Saving the report");
			reportRepository.saveAndFlush(report);
			logger.trace("revoke: Save successful; exiting");
		} else {
			logger.trace("revoke: Report {} cannot be revoked as it is in {} status", report.getId(),
					report.getReportStatus());
			throw new DomainInvariantException(String.format("Report %d cannot be revoked as it is in %s status",
					report.getId(), report.getReportStatus()));
		}
	}

	@Override
	public List<ExpenseReportViewModel> getQueue(IAuthContextViewModel auth) {
		return reportRepository.getUserQueue(auth.getCompanyCode(), auth.getUserId()).stream().map(e -> {
			ExpenseReportViewModel viewModel = new ExpenseReportViewModel();
			BeanUtils.copyProperties(e, viewModel);
			viewModel.setExpenseType(e.getExpenseMetadata().getExpenseType());
			viewModel.setExpenseGroup(e.getExpenseMetadata().getExpenseGroup());

//                    if (e.getReportStatus() == ReportStatus.SENT_BACK) {
//                        ReportState state = stateRepository.getStateForResubmission(auth.getCompanyCode(), e.getId(), e.getActionLevel(), ExpenseActionStatus.SENT_BACK)
//                                .orElseThrow(() -> new RecordNotFoundException("Could not find the sent-back state for expense: " + e.getId()));
//
//                        viewModel.setSendBackRemarks(state.getRemarks());
//                        viewModel.setCurrentApproverEmployeeCode(state.getApproverEmployeeCode());
//                        viewModel.setCurrentApproverFirstName(state.getApproverFirstName());
//                        viewModel.setCurrentApproverLastName(state.getApproverLastName());
//                    }
//
//                    if (e.getReportStatus() == ReportStatus.DECLINED) {
//                        ReportState state = stateRepository.findFirstByCompanyCodeAndExpenseReportIdAndStatus(auth.getCompanyCode(), e.getId(), ExpenseActionStatus.REJECTED)
//                                .orElseThrow(() -> new RecordNotFoundException("Could not find the sent-back state for expense: " + e.getId()));
//
//                        viewModel.setRejectRemarks(state.getRemarks());
//                    }

			if (e.getReportStatus() == ReportStatus.SUBMITTED) {
				Optional<ReportState> currentState = stateRepository
						.findFirstByCompanyCodeAndExpenseReportIdAndStatusOrderByLevel(auth.getCompanyCode(), e.getId(),
								ExpenseActionStatus.UNACTIONED);
				if (currentState.isPresent()) {
					viewModel.setCurrentApproverEmployeeCode(currentState.get().getApproverEmployeeCode());
					viewModel.setCurrentApproverFirstName(currentState.get().getApproverFirstName());
					viewModel.setCurrentApproverLastName(currentState.get().getApproverLastName());
				}
			}

			return viewModel;
		}).collect(Collectors.toList());
	}

	@Transactional(readOnly = true)
	@Override
	public ExpenseReportViewModelV2<ExpenseReportViewModel> getQueueV2(
			IAuthContextViewModel auth, QueueFilterViewModel vm) {

		Pageable page = buildPageable(vm);

		Page<QueueRowDTO> dtoPage =
				queueRepo.findUserQueue(
						auth.getCompanyCode(),
						auth.getUserId(),
						page,
						toFilterMap(vm));

		/* 1.  One-liner that uses the bulk helper */
		List<ExpenseReportViewModel> data = QueueUtil.mapPage(dtoPage);

		ExpenseReportViewModelV2<ExpenseReportViewModel> out =
				new ExpenseReportViewModelV2<>();
		out.setPages(dtoPage.getTotalPages());
		out.setTotal((int) dtoPage.getTotalElements());
		out.setData(data);
		return out;
	}


	private void validateReportSubmission(ExpenseReport report) {
		double lineTotal = 0.0;
		boolean allVouchersValid = true;
		for (Expense e : report.getExpenses()) {
			lineTotal += e.getClaimAmount();
			allVouchersValid = allVouchersValid && e.isHasBeenValidated();
		}

		if (!allVouchersValid) {
			throw new DomainInvariantException("There exist invalid vouchers in the report");
		}

		if (Math.ceil(report.getReportClaimAmount()) != Math.ceil(lineTotal)) {
			throw new DomainInvariantException("Report claim does not match with line expenses");
		}

		if (report.getReportClaimAmount() == 0) {
			throw new DomainInvariantException("Report total claim cannot be zero");
		}

		for (Expense e : report.getExpenses()) {
			if (e.getClaimAmount() == 0) {
				throw new DomainInvariantException("Report contains an empty expense");
			}
		}
	}

	private boolean getDeviationStatus(ExpenseReport report) {
		for (Expense e : report.getExpenses()) {
			if (e.isDeviated()) {
				logger.info("getDeviationStatus: Expense {} is deviated with remarks: {}", e.getId(),
						e.getDeviationRemarks());
				return true;
			}
		}

		logger.trace("getDeviationStatus: No deviated expenses found in the report");
		return false;
	}

	private ExpenseReportViewModel getViewModel(ExpenseReport report) {
		ExpenseReportViewModel viewModel = new ExpenseReportViewModel();
		BeanUtils.copyProperties(report, viewModel);
		return viewModel;
	}

	private void validateReport(ExpenseReport report, ExpenseReportUpdateViewModel viewModel) {
		double lineAmountSum = 0;
		double sgstAmountSum = 0;
		double cgstAmountSum = 0;
		double igstAmountSum = 0;
		double taxableAmountSum = 0;
		for (Expense expense : report.getExpenses()) {
			lineAmountSum += expense.getClaimAmount() != null ? expense.getClaimAmount() : 0;
			sgstAmountSum += expense.getSgstAmount() != null ? expense.getSgstAmount() : 0;
			cgstAmountSum += expense.getCgstAmount() != null ? expense.getCgstAmount() : 0;
			igstAmountSum += expense.getIgstAmount() != null ? expense.getIgstAmount() : 0;
			taxableAmountSum += expense.getTaxableAmount() != null ? expense.getTaxableAmount() : 0;
		}

		if (Math.ceil(lineAmountSum) != Math.ceil(viewModel.getReportClaimAmount())) {
			throw new DomainInvariantException(
					"Report total claim amount doesn't tally with sum of expense claim amounts");
		}

		if (Math.ceil(sgstAmountSum) != Math.ceil(viewModel.getReportSgstAmount())) {
			throw new DomainInvariantException(
					"Report total SGST amount doesn't tally with sum of expense SGST amounts");
		}

		if (Math.ceil(cgstAmountSum) != Math.ceil(viewModel.getReportCgstAmount())) {
			throw new DomainInvariantException(
					"Report total CGST amount doesn't tally with sum of expense CGST amounts");
		}

		if (Math.ceil(igstAmountSum) != Math.ceil(viewModel.getReportIgstAmount())) {
			throw new DomainInvariantException(
					"Report total IGST amount doesn't tally with sum of expense IGST amounts");
		}

		if (Math.ceil(taxableAmountSum) != Math.ceil(viewModel.getReportTaxableAmount())) {
			throw new DomainInvariantException(
					"Report total taxable amount doesn't tally with sum of expense taxable amounts");
		}

		if (report.getExpenseMetadata().isPurposeRequired()
				&& StaticDataRegistry.isNullOrEmptyOrWhitespace(report.getPurpose())) {
			throw new DomainInvariantException("Report should mention the purpose");
		}

		validateMetadataLimits(report);

		validateExistingExpenseDateSanity(report);
	}

	private void validateExistingExpenseDateSanity(ExpenseReport report) {
		for (Expense e : report.getExpenses()) {
			if (e.getExpenseDate() == null) {
				continue;
			}

			if (e.getExpenseDate().isBefore(report.getStartDate()) || e.getExpenseDate().isAfter(report.getEndDate())) {
				throw new DomainInvariantException("This report contains expenses that are not in this date range");
			}
		}
	}

	private void validateMetadataLimits(ExpenseReport report) {
		logger.info(
				String.format("validateMetadataLimits: getApplicable Rules For Expense Report id: %s", report.getId()));
		List<MetadataLimitRule> rules = getApplicableRule(report);

		if (rules.isEmpty()) {
			logger.info(String.format("validateMetadataLimits: Limit rules were not found for Expense Report id: %s",
					report.getId()));
			return;
		}

		logger.info(String.format("validateMetadataLimits: Rules were found for for Expense Report id: %s",
				report.getId()));
		for (MetadataLimitRule rule : rules) {
			switch (rule.getIntervalMarker()) {
			case DAILY:
				validateDailyLimit(report, rule);
				break;
			case MONTHLY:
				validateMonthlyLimit(report, rule);
				break;
			case QUARTERLY:
				validateQuarterlyLimit(report, rule);
				break;
			case HALF_YEARLY:
				validateHalfYearlyLimit(report, rule);
				break;
			case YEARLY:
				validateYearlyLimit(report, rule);
				break;
			case PER_REPORT:
				validatePerReportLimit(report, rule);
				break;
			default:
				break;
			}
		}
	}

	private void validatePerReportLimit(ExpenseReport report, MetadataLimitRule rule) {
		double total = 0;
		for (Expense e : report.getExpenses()) {
			total += e.getClaimAmount();
		}

		if (total > rule.getLimitAmount()) {
			throw new DomainInvariantException(
					String.format("Report amount cannot exceed %.2f", rule.getLimitAmount()));
		}
	}

	private void validateDailyLimit(ExpenseReport report, MetadataLimitRule rule) {
		for (Expense expense : report.getExpenses()) {

			if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
				if (!rule.getLocationCategory().equals(expense.getLocationCategory())) {
					continue;
				}
			}

			if (expense.getExpenseDate() == null) {
				continue;
			}

			Double totalClaim = expenseRepository.getExpensesOnDay(report.getCompanyCode(), report.getCreatingUserId(),
					report.getExpenseMetadataId(), expense.getExpenseDate());

			if (totalClaim == null) {
				totalClaim = 0.0;
			}

			if (totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()) {
				throw new DomainInvariantException(String.format("Daily limit of %.2f for metadata %d is over",
						rule.getLimitAmount(), report.getExpenseMetadataId()));
			}
		}
	}

	private void validateMonthlyLimit(ExpenseReport report, MetadataLimitRule rule) {

		// Only start the validations if there are any expenses in the first place (as
		// mobile calls save initally before creating the first expense)
		if (report.getExpenses().isEmpty()) {
			return;
		}

		logger.info(String.format("validateMonthlyLimit: Started monthly limit checks for Expense Report id %s",
				report.getId()));
		// Get the start and end dates of the expense report
		LocalDate start = report.getStartDate().withDayOfMonth(1);
		LocalDate end = report.getEndDate().withDayOfMonth(report.getEndDate().lengthOfMonth());

		List<IExpenseValidationAggregates> expensesCountByMonth = expenseRepository
				.getExpensesAggregatesByMonth(report.getCompanyCode(), report.getCreatingUserId(),
						report.getExpenseMetadataId(), List.of(ReportStatus.SUBMITTED, ReportStatus.ACCEPTED,
								ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM, ReportStatus.DRAFT),
						start, end);
		List<IExpenseValidationAggregates> expensesSumByMonth = expenseRepository.getExpensesAggregatesByMonth(
				report.getCompanyCode(), report.getCreatingUserId(), report.getExpenseMetadataId(),
				List.of(ReportStatus.SUBMITTED, ReportStatus.ACCEPTED, ReportStatus.PAID,
						ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
				start, end);

		HashMap<LocalDate, ExpenseValidationAggregates> expenseAggregates = new HashMap<>();

		// Initialize the HashMap to make sure that in cases of no values, null key
		// error is never encountered.
		start.datesUntil(end, Period.ofMonths(1)).map(date -> expenseAggregates.put(date.withDayOfMonth(1),
				new ExpenseValidationAggregates(0L, 0.0, date.withDayOfMonth(1)))).collect(Collectors.toList());

//         Running two separate streams as the count and sum may have completely separate months (only drafts in one month, means that there will be
//         no sum in that month but there will a count).
		expensesCountByMonth.forEach(aggregate -> {
			// All dates will be converted to the first day of that month to allow ignoring
			// of the day
			LocalDate date = aggregate.getExpenseDate().withDayOfMonth(1);
			Long nonNullCount = null == aggregate.getCount() ? 0L : aggregate.getCount();
			expenseAggregates.put(date,
					new ExpenseValidationAggregates(nonNullCount, 0.0, aggregate.getExpenseDate().withDayOfMonth(1)));
		});

		logger.info(String.format(
				"validateMonthlyLimit: Performing the filling of the hashmap for expensesSumByMonth for Expense Report id %s",
				report.getId()));
		// If there is a count for that month, add the statistic for sum to it as well.
		// else create the statistic for
		// a new month with only the sum, with the count as null.
		expensesSumByMonth.forEach(aggregate -> {
			LocalDate date = aggregate.getExpenseDate().withDayOfMonth(1);
			// As sum() can return null.
			Double nonNullSum = null == aggregate.getSum() ? 0.0 : aggregate.getSum();
			if (expenseAggregates.containsKey(date)) {
				expenseAggregates.get(date).setSum(nonNullSum);
			} else {
				expenseAggregates.put(aggregate.getExpenseDate(),
						new ExpenseValidationAggregates(0L, nonNullSum, aggregate.getExpenseDate().withDayOfMonth(1)));
			}
		});

		logger.info(String.format("validateMonthlyLimit: The created hashmap is for Expense Report id %s \n\t%s",
				report.getId(), expenseAggregates));

		for (Expense expense : report.getExpenses()) {

			logger.info(String.format(
					"validateMonthlyLimit: Checking if location category rule is applicable for Expense Report id %s",
					report.getId()));
			if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
				if (!rule.getLocationCategory().equals(expense.getLocationCategory())) {
					logger.info(String.format(
							"validateMonthlyLimit: Location category rule is not applicable for Expense Report id %s",
							report.getId()));
					continue;
				}
			}

			if (expense.getExpenseDate() == null) {
				logger.info(String.format("validateMonthlyLimit: Expense Report id %s has no date", report.getId()));
				continue;
			}

			long nExpenses = expenseAggregates.get(expense.getExpenseDate().withDayOfMonth(1)).getCount();

			if (rule.getExpenseCountLimit() > 0 && nExpenses > rule.getExpenseCountLimit()) {
				throw new DomainInvariantException(String.format(
						"You have crossed the expense limit of %d for this period", rule.getExpenseCountLimit()));
			}

			Double totalClaim = expenseAggregates.get(expense.getExpenseDate().withDayOfMonth(1)).getSum();

			// Keep the check though it may be redundant
			if (totalClaim == null) {
				totalClaim = 0.0;
			}

			if (totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()) {
				throw new DomainInvariantException(String.format(
						"Monthly limit of %.2f for %s-%s is over; total claims so far: %.2f", rule.getLimitAmount(),
						report.getExpenseMetadata().getExpenseType(), report.getExpenseMetadata().getExpenseGroup(),
						totalClaim + report.getReportClaimAmount()));
			}
		}
	}

	private void validateQuarterlyLimit(ExpenseReport report, MetadataLimitRule rule) {
		for (Expense expense : report.getExpenses()) {
			if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
				if (!rule.getLocationCategory().equals(expense.getLocationCategory())) {
					continue;
				}
			}

			if (expense.getExpenseDate() == null) {
				continue;
			}

			LocalDate[] delimiters = getQuarterDelimiters(expense.getExpenseDate());
			Double totalClaim = expenseRepository.getExpensesBetween(report.getCompanyCode(),
					report.getCreatingUserId(), report.getExpenseMetadataId(), List.of(ReportStatus.SUBMITTED,
							ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
					delimiters[0], delimiters[1]);

			if (totalClaim == null) {
				totalClaim = 0.0;
			}

			if (totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()) {
				throw new DomainInvariantException(String.format(
						"Quarterly limit of %.2f for %s-%s is over; total claims so far: %.2f", rule.getLimitAmount(),
						report.getExpenseMetadata().getExpenseType(), report.getExpenseMetadata().getExpenseGroup(),
						totalClaim + report.getReportClaimAmount()));
			}
		}
	}

	private void validateHalfYearlyLimit(ExpenseReport report, MetadataLimitRule rule) {
		for (Expense expense : report.getExpenses()) {
			if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
				if (!rule.getLocationCategory().equals(expense.getLocationCategory())) {
					continue;
				}
			}

			if (expense.getExpenseDate() == null) {
				continue;
			}

			LocalDate[] delimiters = getHalfYearDelimiters(expense.getExpenseDate());
			Double totalClaim = expenseRepository.getExpensesBetween(report.getCompanyCode(),
					report.getCreatingUserId(), report.getExpenseMetadataId(), List.of(ReportStatus.SUBMITTED,
							ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
					delimiters[0], delimiters[1]);

			if (totalClaim == null) {
				totalClaim = 0.0;
			}

			if (totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()) {
				throw new DomainInvariantException(String.format(
						"Half-yearly limit of %.2f for %s-%s is over; total claims so far: %.2f", rule.getLimitAmount(),
						report.getExpenseMetadata().getExpenseType(), report.getExpenseMetadata().getExpenseGroup(),
						totalClaim + report.getReportClaimAmount()));
			}
		}
	}

	private void validateYearlyLimit(ExpenseReport report, MetadataLimitRule rule) {
		for (Expense expense : report.getExpenses()) {
			if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
				if (!rule.getLocationCategory().equals(expense.getLocationCategory())) {
					continue;
				}
			}

			if (expense.getExpenseDate() == null) {
				continue;
			}

			LocalDate[] delimiters = getYearDelimiters(expense.getExpenseDate());
			Double totalClaim = expenseRepository.getExpensesBetween(report.getCompanyCode(),
					report.getCreatingUserId(), report.getExpenseMetadataId(), List.of(ReportStatus.SUBMITTED,
							ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
					delimiters[0], delimiters[1]);

			if (totalClaim == null) {
				totalClaim = 0.0;
			}

			if (totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()) {
				throw new DomainInvariantException(String.format(
						"Yearly limit of %.2f for %s-%s is over; total claims so far: %.2f", rule.getLimitAmount(),
						report.getExpenseMetadata().getExpenseType(), report.getExpenseMetadata().getExpenseGroup(),
						totalClaim + report.getReportClaimAmount()));
			}
		}
	}

	private LocalDate[] getQuarterDelimiters(LocalDate markDate) {
		switch (markDate.getMonth()) {
		case JANUARY:
		case FEBRUARY:
		case MARCH:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.JANUARY, 1),
					LocalDate.of(markDate.getYear(), Month.MARCH, 31) };
		case APRIL:
		case MAY:
		case JUNE:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.APRIL, 1),
					LocalDate.of(markDate.getYear(), Month.JUNE, 30) };
		case JULY:
		case AUGUST:
		case SEPTEMBER:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.JULY, 1),
					LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30) };
		default:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.OCTOBER, 1),
					LocalDate.of(markDate.getYear(), Month.DECEMBER, 31) };
		}
	}

	private LocalDate[] getHalfYearDelimiters(LocalDate markDate) {
		switch (markDate.getMonth()) {
		case APRIL:
		case MAY:
		case JUNE:
		case JULY:
		case AUGUST:
		case SEPTEMBER:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.APRIL, 1),
					LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30) };
		default:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.OCTOBER, 1),
					LocalDate.of(markDate.getYear(), Month.MARCH, 31) };
		}
	}

	private LocalDate[] getYearDelimiters(LocalDate markDate) {
		switch (markDate.getMonth()) {
		case JANUARY:
		case FEBRUARY:
		case MARCH:
			return new LocalDate[] { LocalDate.of(markDate.getYear() - 1, Month.APRIL, 1),
					LocalDate.of(markDate.getYear(), Month.MARCH, 31) };
		default:
			return new LocalDate[] { LocalDate.of(markDate.getYear(), Month.APRIL, 1),
					LocalDate.of(markDate.getYear() + 1, Month.MARCH, 31) };
		}
	}

	private List<MetadataLimitRule> getApplicableRule(ExpenseReport report) {
		logger.trace("getApplicableRule: Finding the Metadata Limit Rules for Report with id: {}", report.getId());

		return ruleRepository
				.findByCompanyCodeAndExpenseMetadataId(report.getCompanyCode(), report.getExpenseMetadataId()).stream()
				.filter(r -> !r.isFrozen() && isRuleMatch(r, report)).collect(Collectors.toList());
	}

	private boolean isRuleMatch(MetadataLimitRule rule, ExpenseReport report) {
		boolean result = true;

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
			result = rule.getBranchCode().equals(report.getEmployeeBranch());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
			result = rule.getDepartmentCode().equals(report.getEmployeeDepartment());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
			result = rule.getCostCenterCode().equals(report.getEmployeeCostCenter());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
			result = rule.getEmployeeType().equals(report.getEmployeeType());
			if (!result)
				return false;
		}

		if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
			result = rule.getEmployeeGrade().equals(report.getEmployeeGrade());
			if (!result)
				return false;
		}

		if (!report.getExpenses().isEmpty()
				&& StaticDataRegistry.isNotNullOrEmptyOrWhitespace(report.getExpenses().get(0).getLocationCategory())
				&& StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
			result = rule.getLocationCategory().equals(report.getExpenses().get(0).getLocationCategory());
			if (!result)
				return false;
		}

		return result;
	}

	private boolean isSubmittable(ExpenseReport report) {
		return report.getReportStatus() == ReportStatus.DRAFT || report.getReportStatus() == ReportStatus.SENT_BACK;
	}

	private void handleResubmission(ExpenseReport report, IEmployeeViewModel employeeViewModel,
			IAuthContextViewModel auth, long saveTime) {
		List<ReportState> actionedStates = stateRepository
				.getActionedStatesByExpenseReport(auth.getCompanyCode(), report.getId(),
						List.of(ExpenseActionStatus.APPROVED, ExpenseActionStatus.SENT_BACK))
				.stream().filter(distinctByKey(ReportState::getLevel))
				.sorted(Comparator.comparing(ReportState::getLevel)).collect(Collectors.toList());

		for (ReportState state : actionedStates) {
			// copy the state
			logger.trace("handleResubmission: Found the original state; proceeding with cloning");
			ReportState newState = new ReportState();
			BeanUtils.copyProperties(state, newState, "id", "remarks", "actionDate", "updatedTimestamp");

			logger.trace("handleResubmission: Setting status as unactioned");
			newState.setStatus(ExpenseActionStatus.UNACTIONED);

			logger.trace("handleResubmission: Setting the audit fields");
			newState.setCreatedTimestamp(ZonedDateTime.now());

			logger.trace("handleResubmission: Attaching the bidirectional mapping");
			newState.setExpenseReport(report);
			newState.setExpenseReportId(report.getId());
			newState.setSaveTime(saveTime);
			report.getReportStates().add(newState);

			stateRepository.saveAndFlush(newState);
		}

		report.setUpdatedTimestamp(ZonedDateTime.now());
		report.setUpdatingUserId(auth.getUserId());
		report.setActionLevel(StaticDataRegistry.FIRST_LEVEL);

		if (report.isContainsDeviation()) {
			attachDeviationStatesForResubmission(report, employeeViewModel, auth);
		} else {
			removeUnactionedDeviationStates(report, auth);
		}

		logger.trace("handleResubmission: Saving the entities");
		reportRepository.saveAndFlush(report);
		logger.trace("handleResubmission: Save successful, exiting");
	}

	private void removeUnactionedDeviationStates(ExpenseReport report, IAuthContextViewModel auth) {
		List<ReportState> unactionedDeviationStates = stateRepository
				.getActionedStatesByExpenseReport(auth.getCompanyCode(), report.getId(),
						List.of(ExpenseActionStatus.UNACTIONED))
				.stream().filter(distinctByKey(ReportState::getLevel))
				.sorted(Comparator.comparing(ReportState::getLevel)).filter(ReportState::isDeviationAssignment)
				.collect(Collectors.toList());

		stateRepository.deleteAll(unactionedDeviationStates);
	}

	private void attachDeviationStatesForResubmission(ExpenseReport report, IEmployeeViewModel employeeViewModel,
			IAuthContextViewModel auth) {
		List<ApprovalDefinition> approvalDefinitions = getApprovalDefinitions(report, auth);

		approvalDefinitions.removeIf(d -> !d.isForDeviation());

		approvalDefinitions.sort(Comparator.comparing(ApprovalDefinition::getLevel));

		logger.info("attachApprovers: Proceeding to get and attach approvers");
		getAndAttachApprovers(report, report.getExpenseMetadata(), auth, employeeViewModel, approvalDefinitions);
	}

	private List<ApprovalDefinition> getApprovalDefinitions(ExpenseReport report, IAuthContextViewModel auth) {
		List<ApprovalDefinition> approvalDefinitions = definitionRepository.findApplicableDefinitions(
				auth.getCompanyCode(), report.getExpenseMetadataId(), report.getReportClaimAmount());

		List<ApprovalDefinition> approvalDefinitionsEqualAmount = definitionRepository
				.findApplicableDefinitionsEqualAmount(auth.getCompanyCode(), report.getExpenseMetadataId(),
						report.getReportClaimAmount());

		approvalDefinitions.addAll(approvalDefinitionsEqualAmount);

		Optional<ApprovalDefinition> lastApplicableDefinition = Optional.empty();

		// If the report amount is exactly the limit, then the definition should stop at
		// that limit
		// If the amount is exactly the limit, approvalDefinitionsEqualAmount will NOT
		// be empty
		if (approvalDefinitionsEqualAmount.isEmpty()) {
			lastApplicableDefinition = definitionRepository
					.findFirstByCompanyCodeAndExpenseMetadataIdAndLimitAmountGreaterThanAndIsFrozenIsFalseOrderByLevel(
							auth.getCompanyCode(), report.getExpenseMetadataId(), report.getReportClaimAmount());
		}

		if (lastApplicableDefinition.isPresent()) {
			double limitAmount = lastApplicableDefinition.get().getLimitAmount();
			List<ApprovalDefinition> equalLimitAmountDefinitionsInLastBand = definitionRepository
					.getEqualLimitAmountDefinitionsInLastBand(auth.getCompanyCode(), report.getExpenseMetadataId(),
							limitAmount);

			approvalDefinitions.addAll(equalLimitAmountDefinitionsInLastBand);
		}

		return approvalDefinitions;
	}

	private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
		Set<Object> seen = ConcurrentHashMap.newKeySet();
		return t -> seen.add(keyExtractor.apply(t));
	}

	// Approver related
	private void attachInitialApprovers(ExpenseReport report, ExpenseMetadata metadata, IAuthContextViewModel auth,
			IEmployeeViewModel employeeViewModel, Boolean isResubmission) {
		logger.trace("attachApprovers: Getting the approvers from definitions");
		List<ApprovalDefinition> approvalDefinitions = getApprovalDefinitions(report, auth);

		// This section is adapted from the handle resubmission. Main difference is
		// editing the previous states
		// instead of cloning the saving new ones (leaving the previous untouched).
		if (isResubmission) {
			// Get all the previous report states of the expense. and set them to rejected
			// if they were unactioned
			// This is so that they cannot be considered in the new approval flow (as
			// changed their status from 0 -> 2)
			List<ReportState> allPreviousReportStates = stateRepository
					// Get all states, not just actioned ones
					.getStatesByExpenseReport(auth.getCompanyCode(), report.getId()).stream()
//                            .filter(distinctByKey(ReportState::getLevel)) //<- have removed this as want to go through all the effected states. Not completely sure why we use this.
					.sorted(Comparator.comparing(ReportState::getLevel)).collect(Collectors.toList());
			// The setting of unactioned to rejected (0 -> 2)
			for (ReportState state : allPreviousReportStates) {
				if (state.getStatus() == ExpenseActionStatus.UNACTIONED) {
					state.setStatus(ExpenseActionStatus.REJECTED);
					state.setUpdatedTimestamp(ZonedDateTime.now());
				}
			}
			// Hence, here instead of creating new states and saving them, we update the
			// already present previous states.
			stateRepository.saveAllAndFlush(allPreviousReportStates);

			// Rest lifted from handleResubmission(...) function.
			report.setUpdatedTimestamp(ZonedDateTime.now());
			report.setUpdatingUserId(auth.getUserId());
			report.setActionLevel(StaticDataRegistry.FIRST_LEVEL);

			// Keep previous unactioned deviation states within the history for now.
//            removeUnactionedDeviationStates(report, auth);

			// The below has been removed as we don't need to add deviations as we are
			// creating a submission approval matrix from scratch each time.
//            if (report.isContainsDeviation()) {
//                attachDeviationStatesForResubmission(report, employeeViewModel, auth);
//            } else {
//                removeUnactionedDeviationStates(report, auth);
//            }

			logger.trace("handleResubmission: Saving the entities");
			reportRepository.saveAndFlush(report);
			logger.trace("handleResubmission: Save successful, exiting");

		}

		if (!report.isContainsDeviation()) {
			approvalDefinitions.removeIf(ApprovalDefinition::isForDeviation);
		}

		approvalDefinitions.sort(Comparator.comparing(ApprovalDefinition::getLevel));

		logger.info("attachApprovers: Proceeding to get and attach approvers");
		getAndAttachApprovers(report, metadata, auth, employeeViewModel, approvalDefinitions);
	}

	private void getAndAttachApprovers(ExpenseReport report, ExpenseMetadata metadata, IAuthContextViewModel auth,
			IEmployeeViewModel employeeViewModel, List<ApprovalDefinition> approvalDefinitions) {
		logger.trace("getAndAttachApprovers: Processing the definitions");
		approvalDefinitions.forEach(d -> {
			logger.trace("getAndAttachApprovers: Checking if the approver should be the reporting manager");
			if (d.isShouldFetchFromEmployeeMaster()) {
				logger.trace("getAndAttachApprovers: Attaching reporting manager as the approver from employee master");
				attachFromEmployeeMaster(report, auth, employeeViewModel, d, false);
			} else {
				logger.trace("getAndAttachApprovers: Fetching the approver other than reporting manager");
				fetchAndAttachApprover(report, metadata, auth, employeeViewModel, d, report.isContainsDeviation(),
						report.getDeviationRemarks());
			}
		});
	}

	private void fetchAndAttachApprover(ExpenseReport report, ExpenseMetadata metadata, IAuthContextViewModel auth,
			IEmployeeViewModel employeeViewModel, ApprovalDefinition d, boolean isDeviated, String deviationRemarks) {
		logger.trace("fetchAndAttachApprover: Fetching approver by criteria: {}/{}/{}/{}", metadata.getCompanyCode(),
				d.getApprovalMatcher(), StaticDataRegistry.getEmployeeKVMap(report).get(d.getApprovalMatcher()),
				d.getApprovalTitle());

		Optional<Approver> optional = Optional.empty();

		if (d.getApprovalMatcher().equals(d.getApprovalTitle())) {
			// singularity
			optional = approverRepository.findSingularApproverByCriteria(metadata.getCompanyCode(),
					d.getApprovalMatcher(), d.getApprovalTitle());
		} else {
			optional = approverRepository.findApproverByCriteria(metadata.getCompanyCode(), d.getApprovalMatcher(),
					StaticDataRegistry.getEmployeeKVMap(report).get(d.getApprovalMatcher()), d.getApprovalTitle());
		}

		// there may not be any approvers in some cases (only checkers)
		if (optional.isEmpty()) {
			logger.trace("fetchAndAttachApprover: No approver is found, trying default approver");
			tryAttachingDefaultApprover(report, auth, employeeViewModel, d);
		} else {
			if (auth.getUserEmail().equalsIgnoreCase(optional.get().getApprover())) {
				logger.info("fetchAndAttachApprover: User and approver are same, hence attaching user's manager");
				attachFromEmployeeMaster(report, auth, employeeViewModel, d, false);
			} else {
				logger.info("fetchAndAttachApprover: Attaching the approver: {} at position: {}",
						optional.get().getApprover(), d.getLevel());
				checkDelegationAndAttachApprover(optional.get().getApprover(), report, d, auth, false, d, isDeviated,
						deviationRemarks);
			}
		}
	}

	private void tryAttachingDefaultApprover(ExpenseReport report, IAuthContextViewModel auth,
			IEmployeeViewModel employeeViewModel, ApprovalDefinition d) {
		logger.trace("tryAttachingDefaultApprover: Finding the default approver");
		Optional<Approver> defaultApprover = approverRepository.findApproverByCriteria(report.getCompanyCode(),
				StaticDataRegistry.DEFAULT_APPROVER_MATCHER, StaticDataRegistry.DEFAULT_APPROVER_VALUE,
				StaticDataRegistry.DEFAULT_APPROVER_TITLE);
		if (defaultApprover.isPresent()) {
			logger.trace("tryAttachingDefaultApprover: Default approver is defined");
			if (auth.getUserEmail().equalsIgnoreCase(defaultApprover.get().getApprover())) {
				logger.info("tryAttachingDefaultApprover: Default approver is the user, hence attaching the manager");
				attachFromEmployeeMaster(report, auth, employeeViewModel, d, true);
			} else {
				logger.info(
						"tryAttachingDefaultApprover: Attaching the default approver: {} at position: {} for channel: {}",
						defaultApprover.get().getApprover(), d.getLevel(), d.getChannel());
				attachApprover(defaultApprover.get().getApprover(), d.getLevel(), report, d.getChannel(), null, null,
						null, true, d, auth);
			}
		} else {
			logger.trace("tryAttachingDefaultApprover: There is neither a matching aprrover or default approver. Preventing submission of expense report id: {}", report.getId());
			throw new DomainInvariantException("Invalid Metadata: There is neither a matching approver nor default approver defined for this workflow. Please contact your administrator for assistance.");
		}
	}

	private void attachFromEmployeeMaster(ExpenseReport report, IAuthContextViewModel auth,
			IEmployeeViewModel employeeViewModel, ApprovalDefinition d, boolean isDefault) {
		logger.info(
				"attachFromEmployeeMaster: Found that the approver has to be fetched from employee master; getting the reporting manager to attach at level {}",
				d.getLevel());
		try {
			IEmployeeViewModel manager = employeeService
					.getEmployeeMasterDataByCode(employeeViewModel.getReportingManager(), auth);
			logger.trace(
					"attachFromEmployeeMaster: Proceeding to check approver delegation and attaching the approver");
			checkDelegationAndAttachApprover(manager.getEmail(), report, d, auth, isDefault, d,
					report.isContainsDeviation(), null);
		} catch (RuntimeException exception) {
			logger.info(
					"attachFromEmployeeMaster: Could not find the manager of this employee from CEM master, attaching the default");
			tryAttachingDefaultApprover(report, auth, employeeViewModel, d);
		}
	}

	private void checkDelegationAndAttachApprover(String approver, ExpenseReport report, ApprovalDefinition defintion,
			IAuthContextViewModel auth, boolean isDefault, ApprovalDefinition definition, boolean isDeviated,
			String deviationRemarks) {
		logger.trace("checkDelegationAndAttachApprover: Checking if the delegation exists for {}", approver);
		Optional<ApprovalDelegation> delegationOptional = delegationRepository.findTheDelegateFor(auth.getCompanyCode(),
				approver, LocalDate.now());

		if (delegationOptional.isPresent()) {
			logger.trace(
					"checkDelegationAndAttachApprover: Found a delegation for {}; proceeding to attach the delegated approver {} at level {} in {} channel",
					approver, delegationOptional.get().getAssignedTo(), defintion.getLevel(), defintion.getChannel());

			logger.trace(
					"checkDelegationAndAttachApprover: Checking whether the delegated approver and the creator of the report are same");
			if (delegationOptional.get().getAssignedTo().equalsIgnoreCase(report.getEmployeeEmail())) {
				logger.trace(
						"checkDelegationAndAttachApprover: Found the delegated approver and creator of the report are same; bypassing delegation");
				attachApprover(delegationOptional.get().getOriginator(), defintion.getLevel(), report,
						defintion.getChannel(), approver, null, null, isDefault, definition, auth);
			} else {
				logger.trace(
						"checkDelegationAndAttachApprover: Delegated approver and creator of the report are different; allowing the delegation");
				attachApprover(delegationOptional.get().getAssignedTo(), defintion.getLevel(), report,
						defintion.getChannel(), approver, delegationOptional.get().getId(),
						delegationOptional.get().getCreatedTimestamp(), isDefault, definition, auth);
			}
		} else {
			logger.trace(
					"checkDelegationAndAttachApprover: No delegations; proceeding to attach the original approver {} at level {} in {} channel",
					approver, defintion.getLevel(), defintion.getChannel());
			attachApprover(approver, defintion.getLevel(), report, defintion.getChannel(), null, null, null, isDefault,
					definition, auth);
		}
	}

	private void attachApprover(String approver, int level, ExpenseReport report, StateChannel channel,
			String originalApprover, Long delegationId, ZonedDateTime delegationTimestamp, boolean isDefault,
			ApprovalDefinition definition, IAuthContextViewModel auth) {
		logger.trace("attachApprover: Attaching {} at level {}", approver, level);

		if (report.getEmployeeEmail().equalsIgnoreCase(approver)) {
			logger.trace("attachApprover: The creator {} and approver {} are the same at level {}. Moving to default", report.getEmployeeEmail(), approver, level);
			throw new DomainInvariantException("Error: Invalid Metadata\n" +
					"The user is currently part of the approval workflow for this voucher. Please contact your administrator for assistance.");
		}

		logger.trace("attachApprover: Creating a new expense state");
		ReportState state = new ReportState();

		IEmployeeViewModel employeeDetails = employeeService.getEmployeeMasterData(approver, auth);

//        Optional<ReportState> lastLevelState =
//                stateRepository.findFirstByCompanyCodeAndExpenseReportIdOrderByLevelDesc(report.getCompanyCode(), report.getId());

		Optional<ReportState> lastLevelState = stateRepository
				.findFirstByCompanyCodeAndExpenseReportIdAndStatusOrderByLevelDesc(report.getCompanyCode(),
						report.getId(), ExpenseActionStatus.UNACTIONED);

		logger.trace("attachApprover: Setting fields");
		if (report.isContainsDeviation() && definition.isForDeviation()) {
			state.setDeviationAssignment(true);
			state.setDeviationRemarks(report.getDeviationRemarks());
		}

//        if (lastLevelState.isPresent()) {
//            state.setLevel(lastLevelState.get().getLevel() + StaticDataRegistry.DEFINITION_LEVEL_INCREMENT_FACTOR);
//        } else {
//            state.setLevel(level);
//        }

		if (lastLevelState.isPresent()) {
			state.setLevel(lastLevelState.get().getLevel() + StaticDataRegistry.DEFINITION_LEVEL_INCREMENT_FACTOR);
		} else {
			state.setLevel(level);
		}

		// Here we now always set the level to the level of the approver in the approval
		// definition. The previous code
		// is now not being used as we are now treat the re-submission as a new
		// submission that is, each time we create
		// a new approval matrix with new approvers.
//        state.setLevel(level);

		state.setApprover(approver);
		state.setCompanyCode(report.getCompanyCode());
		state.setStatus(ExpenseActionStatus.UNACTIONED);
		state.setCreatedTimestamp(ZonedDateTime.now());
		state.setChannel(channel);
		state.setDelegated(false);

		state.setApproverFirstName(employeeDetails.getFirstName());
		state.setApproverLastName(employeeDetails.getLastName());
		state.setApproverEmployeeCode(employeeDetails.getEmployeeCode());

		if (isDefault) {
			state.setDefaultTriggerRemarks(String.format("Default assignment for %s -> %s",
					definition.getApprovalMatcher(), definition.getApprovalTitle()));
		}

		if (delegationId != null) {
			logger.trace("attachApprover: Setting delegation specific fields");
			state.setDelegationRemarks(
					StaticDataRegistry.getApprovalDelegationMarker(originalApprover, approver, level, delegationId));
			state.setDelegationTimestamp(delegationTimestamp);
			state.setDelegated(true);
		}

		logger.trace("attachApprover: Setting bi-directional mapping for expense and state");
		state.setExpenseReport(report);
		state.setExpenseReportId(report.getId());
		state.setSaveTime(saveTimeForReportState);
		report.getReportStates().add(state);

		logger.trace("attachApprover: Saving the state");
		stateRepository.saveAndFlush(state);
		reportRepository.saveAndFlush(report);
		logger.trace("attachApprover: Save successful, returning");
	}

	private ExpenseRuleViewModel getRuleViewModel(ExpenseRule rule) {
		logger.trace("getRuleViewModel: Copying rule to view-model and returning");
		ExpenseRuleViewModel viewModel = new ExpenseRuleViewModel();
		BeanUtils.copyProperties(rule, viewModel);
		return viewModel;
	}
}
