package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ExpenseReport;
import in.taxgenie.pay_expense_pvv.entities.IntervalMarker;
import in.taxgenie.pay_expense_pvv.entities.MetadataLimitRule;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseReportRepository;
import in.taxgenie.pay_expense_pvv.repositories.IExpenseRepository;
import in.taxgenie.pay_expense_pvv.repositories.IMetadataLimitRuleRepository;
import in.taxgenie.pay_expense_pvv.repositories.IReportStateRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseReportCommonService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.ReportLimitConsumptionViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ReportStateViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ExpenseReportCommonServiceImplementation implements IExpenseReportCommonService {
    private final IReportStateRepository reportStateRepository;
    private final IExpenseReportRepository reportRepository;
    private final IMetadataLimitRuleRepository ruleRepository;
    private final IExpenseRepository expenseRepository;
    private final Logger logger;

    public ExpenseReportCommonServiceImplementation(IReportStateRepository reportStateRepository, IExpenseReportRepository reportRepository, IMetadataLimitRuleRepository ruleRepository, IExpenseRepository expenseRepository) {
        this.reportStateRepository = reportStateRepository;
        this.reportRepository = reportRepository;
        this.ruleRepository = ruleRepository;
        this.expenseRepository = expenseRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public List<ReportStateViewModel> getStates(long reportId, IAuthContextViewModel auth) {
        return reportStateRepository
                .findByExpenseReportIdSorted(auth.getCompanyCode(), reportId, List.of(ReportStatus.values()))
                .stream()
                .map(s -> {
                    ReportStateViewModel viewModel = new ReportStateViewModel();
                    BeanUtils.copyProperties(s, viewModel);
                    return viewModel;
                })

                .collect(Collectors.toList());
    }

    @Override
    public List<ReportLimitConsumptionViewModel> getConsumption(long reportId, IAuthContextViewModel auth) {
        ExpenseReport report = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), reportId)
                .orElseThrow(() -> new RecordNotFoundException("Could not find Expense Report with id: " + reportId));


        List<MetadataLimitRule> rules = getApplicableRule(report);
        List<ReportLimitConsumptionViewModel> consumptions = new ArrayList<>();

        for (MetadataLimitRule rule : rules) {
            switch (rule.getIntervalMarker()) {
                case DAILY:
                    consumptions.add(getDailyLimitConsumption(report, rule));
                    break;
                case MONTHLY:
                    consumptions.add(getMonthlyLimitConsumption(report, rule));
                    break;
                case QUARTERLY:
                    consumptions.add(getQuarterlyLimitConsumption(report, rule));
                    break;
                case HALF_YEARLY:
                    consumptions.add(getHalfYearlyLimitConsumption(report, rule));
                    break;
                case YEARLY:
                    consumptions.add(getYearlyLimitConsumption(report, rule));
                    break;
                default:
                    break;
            }
        }

        return consumptions;
    }

    private LocalDate[] getQuarterDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case JANUARY:
            case FEBRUARY:
            case MARCH:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.JANUARY, 1), LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
            case APRIL:
            case MAY:
            case JUNE:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1), LocalDate.of(markDate.getYear(), Month.JUNE, 30)};
            case JULY:
            case AUGUST:
            case SEPTEMBER:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.JULY, 1), LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.OCTOBER, 1), LocalDate.of(markDate.getYear(), Month.DECEMBER, 31)};
        }
    }

    private LocalDate[] getHalfYearDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case APRIL:
            case MAY:
            case JUNE:
            case JULY:
            case AUGUST:
            case SEPTEMBER:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1), LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.OCTOBER, 1), LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
        }
    }

    private LocalDate[] getYearDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case JANUARY:
            case FEBRUARY:
            case MARCH:
                return new LocalDate[]{LocalDate.of(markDate.getYear() - 1, Month.APRIL, 1), LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1), LocalDate.of(markDate.getYear() + 1, Month.MARCH, 31)};
        }
    }

    private List<MetadataLimitRule> getApplicableRule(ExpenseReport report) {
        logger.trace("getApplicableRule: Finding the Metadata Limit Rules for Report with id: {}", report.getId());

        return
                ruleRepository
                        .findByCompanyCodeAndExpenseMetadataId(report.getCompanyCode(), report.getExpenseMetadataId())
                        .stream()
                        .filter(r -> isRuleMatch(r, report))
                        .collect(Collectors.toList());
    }

    private boolean isRuleMatch(MetadataLimitRule rule, ExpenseReport report) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(report.getEmployeeBranch());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(report.getEmployeeDepartment());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(report.getEmployeeCostCenter());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(report.getEmployeeType());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(report.getEmployeeGrade());
            if (!result) return false;
        }

        return result;
    }

    private ReportLimitConsumptionViewModel getDailyLimitConsumption(ExpenseReport report, MetadataLimitRule rule) {
        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.DAILY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel.setConsumedAmount(expenseRepository.getExpensesOnDay(report.getCompanyCode(), report.getCreatingUserId(), report.getExpenseMetadataId(), report.getStartDate()));

        return viewModel;
    }

    private ReportLimitConsumptionViewModel getMonthlyLimitConsumption(ExpenseReport report, MetadataLimitRule rule) {
        LocalDate start = report.getStartDate().withDayOfMonth(1);
        LocalDate end = report.getStartDate().withDayOfMonth(report.getStartDate().lengthOfMonth());


        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.MONTHLY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel.setConsumedAmount(expenseRepository.getExpensesBetween(report.getCompanyCode(), report.getCreatingUserId(), report.getExpenseMetadataId(),List.of(ReportStatus.SUBMITTED, ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM), start, end));

        return viewModel;
    }

    private ReportLimitConsumptionViewModel getQuarterlyLimitConsumption(ExpenseReport report, MetadataLimitRule rule) {
        LocalDate[] delimiters = getQuarterDelimiters(report.getStartDate());
        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.QUARTERLY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel.setConsumedAmount(expenseRepository.getExpensesBetween(report.getCompanyCode(), report.getCreatingUserId(), report.getExpenseMetadataId(),List.of(ReportStatus.SUBMITTED, ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM), delimiters[0], delimiters[1]));

        return viewModel;
    }

    private ReportLimitConsumptionViewModel getHalfYearlyLimitConsumption(ExpenseReport report, MetadataLimitRule rule) {
        LocalDate[] delimiters = getHalfYearDelimiters(report.getStartDate());
        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.HALF_YEARLY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel.setConsumedAmount(expenseRepository.getExpensesBetween(report.getCompanyCode(), report.getCreatingUserId(), report.getExpenseMetadataId(),List.of(ReportStatus.SUBMITTED, ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM), delimiters[0], delimiters[1]));

        return viewModel;
    }

    private ReportLimitConsumptionViewModel getYearlyLimitConsumption(ExpenseReport report, MetadataLimitRule rule) {
        LocalDate[] delimiters = getYearDelimiters(report.getStartDate());
        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.YEARLY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel.setConsumedAmount(expenseRepository.getExpensesBetween(report.getCompanyCode(), report.getCreatingUserId(), report.getExpenseMetadataId(),List.of(ReportStatus.SUBMITTED, ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM), delimiters[0], delimiters[1]));

        return viewModel;
    }
}
