package in.taxgenie.pay_expense_pvv.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.opencsv.CSVWriter;

import java.io.FileWriter;
import java.io.IOException;

import in.taxgenie.pay_expense_pvv.reports.viewmodels.ExpenseLineItemsJasperViewModel;
import in.taxgenie.pay_expense_pvv.reports.viewmodels.ExpenseReportJasperViewModel;
import in.taxgenie.pay_expense_pvv.reports.viewmodels.TATReportJasperViewModel;
import org.springframework.stereotype.Component;

@Component
public class ExcelUtil {

	private XSSFWorkbook workbook;
	private XSSFSheet sheet;

	public ExcelUtil() {

		workbook = new XSSFWorkbook();
	}

	private void writeHeaderAll(String sheetName, List<String> columns) {

		sheet = workbook.createSheet(sheetName);
		Row row = sheet.createRow(0);
		CellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();
		font.setFontName("Times New Roman");
		font.setBold(true);
		font.setFontHeight(14);
		style.setFont(font);
		int columnCount = 0;
		for (String column : columns) {
			createCell(row, columnCount++, column, style);
		}
	}

	private void createCell(Row row, int columnCount, Object valueOfCell, CellStyle style) {
		sheet.autoSizeColumn(columnCount);
		Cell cell = row.createCell(columnCount);
		try {
			if (valueOfCell instanceof Integer) {
				cell.setCellValue((Integer) valueOfCell);
			} else if (valueOfCell instanceof Long) {
				cell.setCellValue((Long) valueOfCell);
			} else if (valueOfCell instanceof String) {
				cell.setCellValue((String) valueOfCell);
			} else if (valueOfCell instanceof Double) {
				cell.setCellValue((Double) valueOfCell);
			} else if (valueOfCell instanceof LocalDate) {
				style.setDataFormat(workbook.getCreationHelper().createDataFormat().getFormat("yyyy-mm-dd"));
				cell.setCellValue(java.util.Date
						.from(((LocalDate) valueOfCell).atStartOfDay(ZoneId.systemDefault()).toInstant()));
			} else {
				cell.setCellValue((Boolean) valueOfCell);
			}
		} catch (NullPointerException np) {
			// can ignore the exception
		}
		cell.setCellStyle(style);
	}

	private CellStyle getStyle(int fontSize, CellStyle style) {
		XSSFFont font = workbook.createFont();
		font.setFontName("Times New Roman");
		font.setFontHeight(12);
		style.setFont(font);
		return style;
	}

	private String writeToPath(String reportPath) {
		File currDir = new File(reportPath);
		String path = currDir.getAbsolutePath();
		try {
			FileOutputStream outputStream = new FileOutputStream(path);
			workbook.write(outputStream);
			workbook.close();
			outputStream.close();
		} catch (IOException ex) {
			System.err.format("IO Exception for path :%s %n", path);
		}
		return path;
	}

	public String generateExpenseReportExcel(List<ExpenseReportJasperViewModel> viewModel, String reportPath)
			throws IOException {
		writeHeaderAll("Expense Report", StringConstants.getExcelColumnsForAll());
		int rowCount = 1;
		CellStyle style = workbook.createCellStyle();
		for (ExpenseReportJasperViewModel record : viewModel) {
			Row row = sheet.createRow(rowCount++);
			int columnCount = 0;
			createCell(row, columnCount++, record.getFiscalYear(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeName(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeGrade(), getStyle(14, style));
			createCell(row, columnCount++, record.getReportTitle(), getStyle(14, style));
			createCell(row, columnCount++, record.getDocumentIdentifier(), getStyle(14, style));
			createCell(row, columnCount++, record.getSubmitDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getVoucherAmount(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrency(), getStyle(14, style));
			createCell(row, columnCount++, record.getStartDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getEndDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getExpenseType(), getStyle(14, style));
			createCell(row, columnCount++, record.getVoucherStatus(), getStyle(14, style));
			createCell(row, columnCount++, record.getPendingAt(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeCostCenter(), getStyle(14, style));
			createCell(row, columnCount++, record.getLocation(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentStatus(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getPostedDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentReference(), getStyle(14, style));
			createCell(row, columnCount++, record.getMobileNumber(), getStyle(14, style));
			createCell(row, columnCount++, record.getLimitExceeded(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackByCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackBy(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackOnDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackOnLevel(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackTimes(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackRemarks(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrentApproverEmployeeCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrentApproverName(), getStyle(14, style));
			createCell(row, columnCount++, record.getApprovedDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getApproverLevel(), getStyle(14, style));
			createCell(row, columnCount++, record.getEntity(), getStyle(14, style));
			createCell(row, columnCount++, record.getDelay(), getStyle(14, style));
		}
		String path = writeToPath(reportPath);
		return path;
	}

	public String generateBusinessExpenseReportExcel(List<ExpenseReportJasperViewModel> expenseReportJasperViewModel,
			String reportPath) {
		writeHeaderAll("Expense Report", StringConstants.getExcelColumnsForBusiness());
		int rowCount = 1;
		CellStyle style = workbook.createCellStyle();
		for (ExpenseReportJasperViewModel record : expenseReportJasperViewModel) {
			Row row = sheet.createRow(rowCount++);
			int columnCount = 0;
			createCell(row, columnCount++, record.getFiscalYear(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeName(), getStyle(14, style));
			createCell(row, columnCount++, record.getDocumentIdentifier(), getStyle(14, style));
			createCell(row, columnCount++, record.getReportTitle(), getStyle(14, style));
			createCell(row, columnCount++, record.getExpenseType(), getStyle(14, style));
			createCell(row, columnCount++, record.getSubmitDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getVoucherAmount(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrency(), getStyle(14, style));
			createCell(row, columnCount++, record.getStartDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getEndDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getDelay(), getStyle(14, style));
			createCell(row, columnCount++, record.getVoucherStatus(), getStyle(14, style));
			createCell(row, columnCount++, record.getPendingAt(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeCostCenter(), getStyle(14, style));
			createCell(row, columnCount++, record.getLocation(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentStatus(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getPostedDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentReference(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackByCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackBy(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackOnDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackOnLevel(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackTimes(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackRemarks(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrentApproverEmployeeCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrentApproverName(), getStyle(14, style));
			createCell(row, columnCount++, record.getApprovedDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getApproverLevel(), getStyle(14, style));
			createCell(row, columnCount++, record.getEntity(), getStyle(14, style));
		}

		String path = writeToPath(reportPath);
		return path;
	}

	public String generateTravelExpenseReportExcel(List<ExpenseReportJasperViewModel> expenseReportJasperViewModel,
			String reportPath) {
		writeHeaderAll("Expense Report", StringConstants.getExcelColumnsForTravel());
		int rowCount = 1;
		CellStyle style = workbook.createCellStyle();
		for (ExpenseReportJasperViewModel record : expenseReportJasperViewModel) {
			Row row = sheet.createRow(rowCount++);
			int columnCount = 0;
			createCell(row, columnCount++, record.getFiscalYear(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeName(), getStyle(14, style));
			createCell(row, columnCount++, record.getDocumentIdentifier(), getStyle(14, style));
			createCell(row, columnCount++, record.getReportTitle(), getStyle(14, style));
			createCell(row, columnCount++, record.getCreatedDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getTotalExpenses(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrency(), getStyle(14, style));
			createCell(row, columnCount++, record.getStartDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getEndDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentStatus(), getStyle(14, style));
			createCell(row, columnCount++, record.getPendingAt(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getPostedDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentReference(), getStyle(14, style));
			createCell(row, columnCount++, record.getDepartment(), getStyle(14, style));
			createCell(row, columnCount++, record.getLocation(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackByCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackBy(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackOnDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getSentBackTimes(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrentApproverEmployeeCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrentApproverName(), getStyle(14, style));
			createCell(row, columnCount++, record.getApprovedDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getEntity(), getStyle(14, style));
		}

		String path = writeToPath(reportPath);
		return path;
	}

	public String generateLineItemExpenseReportExcel(
			List<ExpenseLineItemsJasperViewModel> expenseLineItemsJasperViewModel, String reportPath) {
		writeHeaderAll("Expense Report", StringConstants.getExcelColumnsForLineItem());
		int rowCount = 1;
		CellStyle style = workbook.createCellStyle();
		for (ExpenseLineItemsJasperViewModel record : expenseLineItemsJasperViewModel) {
			Row row = sheet.createRow(rowCount++);
			int columnCount = 0;
			createCell(row, columnCount++, record.getFiscalYear(), getStyle(14, style));
			createCell(row, columnCount++, record.getDocumentIdentifier(), getStyle(14, style));
			createCell(row, columnCount++, record.getReportCreatedDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getReportTitle(), getStyle(14, style));
			createCell(row, columnCount++, record.getExpenseType(), getStyle(14, style));
			createCell(row, columnCount++, record.getExpenseName(), getStyle(14, style));
			createCell(row, columnCount++, record.getExpenseDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getSourceLocation(), getStyle(14, style));
			createCell(row, columnCount++, record.getDestinationLocation(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeCode(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeName(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeGrade(), getStyle(14, style));
			createCell(row, columnCount++, record.getSubmitDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getVoucherStatus(), getStyle(14, style));
			createCell(row, columnCount++, record.getVoucherAmount(), getStyle(14, style));
			createCell(row, columnCount++, record.getCurrency(), getStyle(14, style));
			createCell(row, columnCount++, record.getInvoiceAmount(), getStyle(14, style));
			createCell(row, columnCount++, record.getGlDocumentReference(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeCostCenter(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentReference(), getStyle(14, style));
			createCell(row, columnCount++, record.getStartDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getEndDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeCostCenter(), getStyle(14, style));
			createCell(row, columnCount++, record.getLocation(), getStyle(14, style));
			createCell(row, columnCount++, record.getDescription(), getStyle(14, style));
			createCell(row, columnCount++, record.getEntity(), getStyle(14, style));
		}

		String path = writeToPath(reportPath);
		return path;
	}

	public String generateTATExpenseReportExcel(List<TATReportJasperViewModel> tatReportJasperViewModel,
			String reportPath) throws IOException {
		writeHeaderAll("Expense Report", StringConstants.getExcelColumnsForTAT());
		int rowCount = 1;
		CellStyle style = workbook.createCellStyle();
		for (TATReportJasperViewModel record : tatReportJasperViewModel) {
			Row row = sheet.createRow(rowCount++);
			int columnCount = 0;

			createCell(row, columnCount++, record.getDocumentIdentifier(), getStyle(14, style));
			createCell(row, columnCount++, record.getCreatedDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getVoucherStatus(), getStyle(14, style));
			createCell(row, columnCount++, record.getSubmitDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeGrade(), getStyle(14, style));
			createCell(row, columnCount++, record.getPendingAt(), getStyle(14, style));
			createCell(row, columnCount++, record.getExpenseType(), getStyle(14, style));
			createCell(row, columnCount++, record.getEmployeeCostCenter(), getStyle(14, style));
			createCell(row, columnCount++, record.getDepartment(), getStyle(14, style));
			createCell(row, columnCount++, record.getLocation(), getStyle(14, style));
			createCell(row, columnCount++, record.getVoucherAmount(), getStyle(14, style));

			createCell(row, columnCount++, record.getB1ApproverDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getTat1(), getStyle(14, style));
			createCell(row, columnCount++, record.getB1ApproverName(), getStyle(14, style));

			createCell(row, columnCount++, record.getB2ApproverDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getTat2(), getStyle(14, style));
			createCell(row, columnCount++, record.getB2ApproverName(), getStyle(14, style));

			createCell(row, columnCount++, record.getB3ApproverDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getTat3(), getStyle(14, style));
			createCell(row, columnCount++, record.getB3ApproverName(), getStyle(14, style));

			createCell(row, columnCount++, record.getB4ApproverDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getTat4(), getStyle(14, style));
			createCell(row, columnCount++, record.getB4ApproverName(), getStyle(14, style));

			createCell(row, columnCount++, record.getB5ApproverDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getTat5(), getStyle(14, style));
			createCell(row, columnCount++, record.getB5ApproverName(), getStyle(14, style));

			createCell(row, columnCount++, record.getTotalBATAT(), getStyle(14, style));
			createCell(row, columnCount++, record.getChecker1ApproverDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getTat6(), getStyle(14, style));
			createCell(row, columnCount++, record.getChecker1ApproverName(), getStyle(14, style));

			createCell(row, columnCount++, record.getChecker2ApproverDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getTat7(), getStyle(14, style));
			createCell(row, columnCount++, record.getChecker2ApproverName(), getStyle(14, style));

			createCell(row, columnCount++, record.getPaymentDate(), getStyle(14, style));
			createCell(row, columnCount++, record.getPaymentReference(), getStyle(14, style));

			createCell(row, columnCount++, record.getPaymentTAT(), getStyle(14, style));
			createCell(row, columnCount++, record.getTotalCheckerTAT(), getStyle(14, style));

			createCell(row, columnCount++, record.getTotalTAT(), getStyle(14, style));

			createCell(row, columnCount++, record.getEntity(), getStyle(14, style));
		}

		String path = writeToPath(reportPath);
		return path;
	}

	public String toCsvForAll(String csvFile, List<ExpenseReportJasperViewModel> expenseReportJasperViewModel) {

		try (CSVPrinter printer = new CSVPrinter(new FileWriter(csvFile), CSVFormat.DEFAULT)) {
			// create header row
			printer.printRecord(StringConstants.getExcelColumnsForAll().toArray());
			// create data rows
			for (ExpenseReportJasperViewModel view : expenseReportJasperViewModel) {
				Object values[] = { view.getFiscalYear(), view.getEmployeeCode(), view.getEmployeeName(),
						view.getEmployeeGrade(), view.getReportTitle(), view.getDocumentIdentifier(),
						view.getSubmitDate(), view.getReportClaimAmount(), view.getCurrency(), view.getStartDate(),
						view.getEndDate(), view.getExpenseType(), view.getVoucherStatus(), view.getPendingAt(),
						view.getEmployeeCostCenter(), view.getLocation(), view.getPaymentStatus(),
						view.getPaymentDate(), view.getPostedDate(), view.getPaymentReference(), view.getMobileNumber(),
						view.getLimitExceeded(), view.getSentBackByCode(), view.getSentBackBy(),
						view.getSentBackOnDate(), view.getSentBackOnLevel(), view.getSentBackTimes(),
						view.getSentBackRemarks(), view.getCurrentApproverEmployeeCode(), view.getCurrentApproverName(),
						view.getApprovedDate(), view.getApproverLevel(), view.getEntity(), view.getDelay() };
				printer.printRecord(values);
			}
			printer.flush();
			printer.close();
		} catch (IOException io) {
			System.err.format("IO Exception expense report csv", io.getMessage());
		}
		return csvFile;
	}

	public String toCsvForBuisness(String csvFile, List<ExpenseReportJasperViewModel> expenseReportJasperViewModel)
			throws IOException {
		try (CSVPrinter printer = new CSVPrinter(new FileWriter(csvFile), CSVFormat.DEFAULT)) {
			// create header row
			printer.printRecord(StringConstants.getExcelColumnsForBusiness().toArray());
			// create data rows
			for (ExpenseReportJasperViewModel view : expenseReportJasperViewModel) {
				Object values[] = { view.getFiscalYear(), view.getEmployeeCode(), view.getEmployeeName(),
						view.getDocumentIdentifier(), view.getReportTitle(), view.getExpenseType(),
						view.getSubmitDate(), view.getVoucherAmount(), view.getCurrency(), view.getStartDate(),
						view.getEndDate(), view.getDelay(), view.getVoucherStatus(), view.getPendingAt(),
						view.getEmployeeCostCenter(), view.getLocation(), view.getPaymentStatus(),
						view.getPaymentDate(), view.getPostedDate(), view.getPaymentReference(),
						view.getSentBackByCode(), view.getSentBackBy(), view.getSentBackOnDate(),
						view.getSentBackOnLevel(), view.getSentBackTimes(), view.getSentBackRemarks(),
						view.getCurrentApproverEmployeeCode(), view.getCurrentApproverName(), view.getApprovedDate(),
						view.getApproverLevel(), view.getEntity() };
				for (Object value : values) {
					System.out.print(value + ", ");
				}
				System.out.println();
				printer.printRecord(values);
			}
			printer.flush();
			printer.close();
		} catch (IOException io) {
			System.err.format("IO Exception expense report csv", io.getMessage());
		}
		return csvFile;
	}

	public String toCsvForTravel(String csvFile, List<ExpenseReportJasperViewModel> trvJasperViewModel)
			throws IOException {
		try (CSVPrinter printer = new CSVPrinter(new FileWriter(csvFile), CSVFormat.DEFAULT)) {
			// create header row
			printer.printRecord(StringConstants.getExcelColumnsForTravel().toArray());
			// create data rows
			for (ExpenseReportJasperViewModel view : trvJasperViewModel) {
				Object values[] = { view.getFiscalYear(), view.getEmployeeCode(), view.getEmployeeName(),
						view.getDocumentIdentifier(), view.getReportTitle(), view.getCreatedDate(),
						view.getTotalExpenses(), view.getCurrency(), view.getStartDate(), view.getEndDate(),
						view.getPaymentStatus(), view.getPendingAt(), view.getPaymentDate(), view.getPostedDate(),
						view.getPaymentReference(), view.getDepartment(), view.getLocation(), view.getSentBackByCode(),
						view.getSentBackBy(), view.getSentBackOnDate(), view.getSentBackTimes(),
						view.getCurrentApproverEmployeeCode(), view.getCurrentApproverName(), view.getApprovedDate(),
						view.getEntity() };
				printer.printRecord(values);
			}
			printer.flush();
			printer.close();
		} catch (IOException io) {
			System.err.format("IO Exception expense report csv", io.getMessage());
		}
		return csvFile;
	}

	public String toCsvForLineItem(String csvFile, List<ExpenseLineItemsJasperViewModel> lineItemJasperViewModel)
			throws IOException {
		try (CSVPrinter printer = new CSVPrinter(new FileWriter(csvFile), CSVFormat.DEFAULT)) {
			// create header row
			printer.printRecord(StringConstants.getExcelColumnsForLineItem().toArray());
			// create data rows
			for (ExpenseLineItemsJasperViewModel view : lineItemJasperViewModel) {
				Object values[] = { view.getFiscalYear(), view.getDocumentIdentifier(), view.getReportCreatedDate(),
						view.getReportTitle(), view.getExpenseType(), view.getExpenseName(), view.getExpenseDate(),
						view.getSourceLocation(), view.getDestinationLocation(), view.getEmployeeCode(),
						view.getEmployeeName(), view.getEmployeeGrade(), view.getSubmitDate(), view.getVoucherStatus(),
						view.getVoucherAmount(), view.getCurrency(), view.getInvoiceAmount(), view.getEmployeeGLCode(),
						view.getGlDocumentReference(), view.getPaymentDate(), view.getPaymentReference(),
						view.getStartDate(), view.getEndDate(), view.getEmployeeCostCenter(), view.getLocation(),
						view.getDescription(), view.getEntity() };
				printer.printRecord(values);
			}
			printer.flush();
			printer.close();
		} catch (IOException io) {
			System.err.format("IO Exception expense report csv", io.getMessage());
		}
		return csvFile;
	}

	public String toCsvForTAT(String csvFile, List<TATReportJasperViewModel> tatJasperViewModel) throws IOException {
		try (CSVPrinter printer = new CSVPrinter(new FileWriter(csvFile), CSVFormat.DEFAULT)) {
			// create header row
			printer.printRecord(StringConstants.getExcelColumnsForTAT().toArray());
			// create data rows
			for (TATReportJasperViewModel view : tatJasperViewModel) {
				Object values[] = { view.getDocumentIdentifier(), view.getCreatedDate(), view.getVoucherStatus(),
						view.getSubmitDate(), view.getEmployeeGrade(), view.getPendingAt(), view.getExpenseType(),
						view.getEmployeeCostCenter(), view.getDepartment(), view.getLocation(), view.getVoucherAmount(),
						view.getB1ApproverDate(), view.getTat1(), view.getB1ApproverName(), view.getB2ApproverDate(),
						view.getTat2(), view.getB2ApproverName(), view.getB3ApproverDate(), view.getTat3(),
						view.getB3ApproverName(), view.getB4ApproverDate(), view.getTat4(), view.getB4ApproverName(),
						view.getB5ApproverDate(), view.getTat5(), view.getB5ApproverName(), view.getTotalBATAT(),
						view.getChecker1ApproverDate(), view.getTat6(), view.getChecker1ApproverName(),
						view.getChecker2ApproverDate(), view.getTat7(), view.getChecker2ApproverName(),
						view.getPaymentDate(), view.getPaymentReference(), view.getPaymentTAT(),
						view.getTotalCheckerTAT(), view.getTotalTAT(), view.getEntity() };
				printer.printRecord(values);
			}
			printer.flush();
			printer.close();
		} catch (IOException io) {
			System.err.format("IO Exception expense report csv", io.getMessage());
		}
		return csvFile;
	}

	public <T> void writePOJOToCSV(String filePath, List<T> pojoList) {
		try (CSVWriter writer = new CSVWriter(new FileWriter(filePath))) {
			// Get the class of the first POJO in the list to extract the field names
			Class<?> pojoClass = pojoList.get(0).getClass();

			// Extract field names
			Field[] fields = pojoClass.getDeclaredFields();
			String[] header = new String[fields.length];
			for (int i = 0; i < fields.length; i++) {
				String fieldName = fields[i].getName();
				String spacedFieldName = addSpacesToFieldName(fieldName);
				header[i] = spacedFieldName;
			}

			// Write header to CSV file
			writer.writeNext(header);

			// Write data rows to CSV file
			for (T pojo : pojoList) {
				String[] data = new String[fields.length];
				for (int i = 0; i < fields.length; i++) {
					fields[i].setAccessible(true);
					Object value = fields[i].get(pojo);
					data[i] = String.valueOf(value);
				}
				writer.writeNext(data);
			}

			System.out.println("CSV file written successfully!");

		} catch (IOException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	public <T> void writePOJOToCSV(String filePath, List<T> pojoList, List<String> headers) {
		try (CSVWriter writer = new CSVWriter(new FileWriter(filePath))) {
			// Get the class of the first POJO in the list to extract the field names
			Class<?> pojoClass = pojoList.get(0).getClass();

//			// Extract field names
			Field[] fields = pojoClass.getDeclaredFields();
//			String[] header = new String[fields.length];
//			for (int i = 0; i < fields.length; i++) {
//				String fieldName = fields[i].getName();
//				String spacedFieldName = addSpacesToFieldName(fieldName);
//				header[i] = spacedFieldName;
//			}

			String[] header = headers.toArray(new String[0]);

			// Write header to CSV file
			writer.writeNext(header);

			// Write data rows to CSV file
			for (T pojo : pojoList) {
				String[] data = new String[fields.length];
				for (int i = 0; i < fields.length; i++) {
					fields[i].setAccessible(true);
					Object value = fields[i].get(pojo);
					data[i] = null == value ? "" : String.valueOf(value);
				}
				writer.writeNext(data);
			}

			System.out.println("CSV file written successfully!");

		} catch (IOException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	private String addSpacesToFieldName(String fieldName) {
		StringBuilder spacedFieldName = new StringBuilder();
		for (char c : fieldName.toCharArray()) {
			if (Character.isUpperCase(c)) {
				spacedFieldName.append(' ');
			}
			spacedFieldName.append(c);
		}
		if (spacedFieldName.length() > 0) {
			char firstLetter = spacedFieldName.charAt(0);
			spacedFieldName.setCharAt(0, Character.toUpperCase(firstLetter));
		}
		return spacedFieldName.toString();
	}

}
