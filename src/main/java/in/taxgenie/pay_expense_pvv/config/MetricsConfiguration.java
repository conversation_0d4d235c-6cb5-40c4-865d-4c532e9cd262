package in.taxgenie.pay_expense_pvv.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Configuration
public class MetricsConfiguration {

    @Bean
    public OncePerRequestFilter metricsFilter(MeterRegistry meterRegistry) {
        return new OncePerRequestFilter() {
            @Override
            protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                          Filter<PERSON>hain filterChain) throws ServletException, IOException {
                
                String uri = request.getRequestURI();
                String method = request.getMethod();
                
                // Only track metrics for our submit endpoint
                if (uri.contains("/submit/") && "PUT".equals(method)) {
                    Timer.Sample sample = Timer.start(meterRegistry);
                    
                    try {
                        filterChain.doFilter(request, response);
                    } finally {
                        sample.stop(Timer.builder("expense.report.submit")
                                .description("Time taken to submit expense report")
                                .tag("method", method)
                                .tag("status", String.valueOf(response.getStatus()))
                                .register(meterRegistry));
                        
                        // Count submissions
                        meterRegistry.counter("expense.report.submit.count",
                                "status", String.valueOf(response.getStatus()),
                                "success", response.getStatus() < 400 ? "true" : "false")
                                .increment();
                    }
                } else {
                    filterChain.doFilter(request, response);
                }
            }
        };
    }
}
