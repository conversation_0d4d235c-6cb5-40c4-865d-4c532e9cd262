FROM openjdk:latest

EXPOSE 8080

ENV DB_URL=DB_URL DB_USERNAME=DB_USERNAME DB_PASSWORD=DB_PASSWORD FILE_UPLOAD_LIMIT=FILE_UPLOAD_LIMIT JWT_SECRET=JWT_SECRET PAY_EXPENSE_PRODUCT_ID=PAY_EXPENSE_PRODUCT_ID

ARG JAR_FILE=target/pay-expense-pvv.jar

ADD ${JAR_FILE} pay-expense.jar

RUN mkdir uploads

COPY ./uploads /uploads

ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom","-jar","/pay-expense.jar"]