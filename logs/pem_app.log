2025-05-23 11:58:54 - Starting PayExpensePvvApplication using Java 17.0.10 on Vedants-Mac-Studio.local with PID 6581 (/Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix/target/classes started by vedantdube in /Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix)
2025-05-23 11:58:54 - HV000001: Hibernate Validator 6.2.0.Final
2025-05-23 11:58:54 - Running with Spring Boot v2.5.9, Spring v5.3.15
2025-05-23 11:58:54 - The following profiles are active: tg-internal-gcp
2025-05-23 11:58:54 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-23 11:58:54 - Finished Spring Data repository scanning in 64 ms. Found 18 JPA repository interfaces.
2025-05-23 11:58:54 - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionH<PERSON><PERSON>@59845579' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 11:58:54 - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 11:58:54 - Tomcat initialized with port(s): 8080 (http)
2025-05-23 11:58:54 - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-23 11:58:54 - Starting service [Tomcat]
2025-05-23 11:58:54 - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-05-23 11:58:54 - Initializing Spring embedded WebApplicationContext
2025-05-23 11:58:54 - Root WebApplicationContext: initialization completed in 855 ms
2025-05-23 11:58:55 - Filter 'appJwtAuthenticationFilter' configured for use
2025-05-23 11:58:55 - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-05-23 11:58:55 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-23 11:58:55 - HHH000412: Hibernate ORM core version 5.4.33
2025-05-23 11:58:55 - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-23 11:58:55 - HikariPool-1 - configuration:
2025-05-23 11:58:55 - allowPoolSuspension................................false
2025-05-23 11:58:55 - autoCommit................................true
2025-05-23 11:58:55 - catalog................................none
2025-05-23 11:58:55 - connectionInitSql................................none
2025-05-23 11:58:55 - connectionTestQuery................................none
2025-05-23 11:58:55 - connectionTimeout................................30000
2025-05-23 11:58:55 - dataSource................................none
2025-05-23 11:58:55 - dataSourceClassName................................none
2025-05-23 11:58:55 - dataSourceJNDI................................none
2025-05-23 11:58:55 - dataSourceProperties................................{password=<masked>}
2025-05-23 11:58:55 - driverClassName................................"com.mysql.cj.jdbc.Driver"
2025-05-23 11:58:55 - exceptionOverrideClassName................................none
2025-05-23 11:58:55 - healthCheckProperties................................{}
2025-05-23 11:58:55 - healthCheckRegistry................................none
2025-05-23 11:58:55 - idleTimeout................................***********-05-23 11:58:55 - initializationFailTimeout................................1
2025-05-23 11:58:55 - isolateInternalQueries................................false
2025-05-23 11:58:55 - jdbcUrl................................***************************************************
2025-05-23 11:58:55 - keepaliveTime................................0
2025-05-23 11:58:55 - leakDetectionThreshold................................0
2025-05-23 11:58:55 - maxLifetime................................1800000
2025-05-23 11:58:55 - maximumPoolSize................................150
2025-05-23 11:58:55 - metricRegistry................................none
2025-05-23 11:58:55 - metricsTrackerFactory................................none
2025-05-23 11:58:55 - minimumIdle................................20
2025-05-23 11:58:55 - password................................<masked>
2025-05-23 11:58:55 - poolName................................"HikariPool-1"
2025-05-23 11:58:55 - readOnly................................false
2025-05-23 11:58:55 - registerMbeans................................false
2025-05-23 11:58:55 - scheduledExecutor................................none
2025-05-23 11:58:55 - schema................................none
2025-05-23 11:58:55 - threadFactory................................internal
2025-05-23 11:58:55 - transactionIsolation................................default
2025-05-23 11:58:55 - username................................"taxgenie"
2025-05-23 11:58:55 - validationTimeout................................5000
2025-05-23 11:58:55 - HikariPool-1 - Starting...
2025-05-23 11:58:55 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@225ddf5f
2025-05-23 11:58:55 - HikariPool-1 - Start completed.
2025-05-23 11:58:55 - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-05-23 11:58:55 - HikariPool-1 - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 11:58:55 - HikariPool-1 - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 11:58:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@180ae152
2025-05-23 11:58:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bd9f2c7
2025-05-23 11:58:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@47c1e291
2025-05-23 11:58:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@33ee0a56
2025-05-23 11:58:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@53152e4
2025-05-23 11:58:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@63d54f44
2025-05-23 11:58:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@61542f01
2025-05-23 11:58:57 - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-23 11:58:57 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 11:58:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@612e2393
2025-05-23 11:58:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1013b706
2025-05-23 11:58:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@349d7bc2
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@77c42656
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - The default project ID is noble-stratum-393405
2025-05-23 11:58:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6df6ce89
2025-05-23 11:58:57 - Default credentials provider for <NAME_EMAIL>
2025-05-23 11:58:57 - Scopes in use by default credentials: [https://www.googleapis.com/auth/pubsub, https://www.googleapis.com/auth/spanner.admin, https://www.googleapis.com/auth/spanner.data, https://www.googleapis.com/auth/datastore, https://www.googleapis.com/auth/sqlservice.admin, https://www.googleapis.com/auth/devstorage.read_only, https://www.googleapis.com/auth/devstorage.read_write, https://www.googleapis.com/auth/cloudruntimeconfig, https://www.googleapis.com/auth/trace.append, https://www.googleapis.com/auth/cloud-platform, https://www.googleapis.com/auth/cloud-vision, https://www.googleapis.com/auth/bigquery, https://www.googleapis.com/auth/monitoring.write]
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 11:58:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e46ca46
2025-05-23 11:58:58 - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-23 11:58:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1266675b
2025-05-23 11:58:58 - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6b27363b, org.springframework.security.web.context.SecurityContextPersistenceFilter@2ef86335, org.springframework.security.web.header.HeaderWriterFilter@57497703, org.springframework.web.filter.CorsFilter@1815577b, org.springframework.security.web.authentication.logout.LogoutFilter@5de6a9b9, in.taxgenie.pay_expense_pvv.auth.AppJwtAuthenticationFilter@423f8a73, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@65f913fd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7ec9780b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6965f207, org.springframework.security.web.session.SessionManagementFilter@502356ec, org.springframework.security.web.access.ExceptionTranslationFilter@3da94286]
2025-05-23 11:58:58 - Starting ProtocolHandler ["http-nio-8080"]
2025-05-23 11:58:58 - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-23 11:58:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@78e28d8f
2025-05-23 11:58:58 - Started PayExpensePvvApplication in 4.808 seconds (JVM running for 5.171)
2025-05-23 11:58:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2d15f155
2025-05-23 11:58:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@26cd7c0b
2025-05-23 11:58:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1a2ee61f
2025-05-23 11:58:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2a63a853
2025-05-23 11:58:59 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 11:59:25 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 11:59:25 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 11:59:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 11:59:55 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 11:59:55 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 11:59:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:00:25 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:00:25 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:00:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:00:55 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:00:55 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:00:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:00:58 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 12:00:58 - Initializing Servlet 'dispatcherServlet'
2025-05-23 12:00:58 - Completed initialization in 1 ms
2025-05-23 12:00:58 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:00:58 - doFilterInternal: Request came from http://localhost:8080/api/v1/dashboard with path /api/v1/dashboard and host 0:0:0:0:0:0:0:1
2025-05-23 12:00:58 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:00:59 - getMonthlyClaimLine: From 2025-03-01 to 2025-03-31
2025-05-23 12:00:59 - getMonthlyClaimLine: From 2025-04-01 to 2025-04-30
2025-05-23 12:00:59 - getMonthlyClaimLine: From 2025-05-01 to 2025-05-31
2025-05-23 12:00:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:00:59 - doFilterInternal: Request came from http://localhost:8080/api/v1/dashboard with path /api/v1/dashboard and host 0:0:0:0:0:0:0:1
2025-05-23 12:00:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:00:59 - getMonthlyClaimLine: From 2025-03-01 to 2025-03-31
2025-05-23 12:00:59 - getMonthlyClaimLine: From 2025-04-01 to 2025-04-30
2025-05-23 12:00:59 - getMonthlyClaimLine: From 2025-05-01 to 2025-05-31
2025-05-23 12:01:02 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata with path /api/v1/expense-metadata and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/user/queue with path /api/v1/expense-report/user/queue and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/user/queue with path /api/v1/expense-report/user/queue and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - getAll: Returning view model list
2025-05-23 12:01:02 -  Auth in User queue 13000  25364  [approver, user, admin, checker, report]
2025-05-23 12:01:02 -  Auth in User queue 13000  25364  [approver, user, admin, checker, report]
2025-05-23 12:01:02 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata with path /api/v1/expense-metadata and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:02 - getAll: Returning view model list
2025-05-23 12:01:02 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@51a75385
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/get/2243 with path /api/v1/expense-report/get/2243 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata/44 with path /api/v1/expense-metadata/44 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - getById: Find the source record
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-subgroup/get/by-metadata/44 with path /api/v1/expense-subgroup/get/by-metadata/44 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/by-report-id/2243 with path /api/v1/expense/get/by-report-id/2243 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - doFilterInternal: Request came from http://localhost:8080/api/v1/locations with path /api/v1/locations and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:04 - getAll: Returning view model list
2025-05-23 12:01:06 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:06 - doFilterInternal: Request came from http://localhost:8080/api/v1/dashboard with path /api/v1/dashboard and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:06 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:06 - getMonthlyClaimLine: From 2025-03-01 to 2025-03-31
2025-05-23 12:01:06 - getMonthlyClaimLine: From 2025-04-01 to 2025-04-30
2025-05-23 12:01:06 - getMonthlyClaimLine: From 2025-05-01 to 2025-05-31
2025-05-23 12:01:08 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/user/queue with path /api/v1/expense-report/user/queue and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata with path /api/v1/expense-metadata and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/user/queue with path /api/v1/expense-report/user/queue and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 -  Auth in User queue 13000  25364  [approver, user, admin, checker, report]
2025-05-23 12:01:08 - getAll: Returning view model list
2025-05-23 12:01:08 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 -  Auth in User queue 13000  25364  [approver, user, admin, checker, report]
2025-05-23 12:01:08 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata with path /api/v1/expense-metadata and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:08 - getAll: Returning view model list
2025-05-23 12:01:17 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:17 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:17 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/user/queue with path /api/v1/expense-report/user/queue and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:17 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata with path /api/v1/expense-metadata and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:17 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:17 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:17 - getAll: Returning view model list
2025-05-23 12:01:17 -  Auth in User queue 13000  25364  [approver, user, admin, checker, report]
2025-05-23 12:01:21 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:21 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/get/2237 with path /api/v1/expense-report/get/2237 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:21 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:21 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:21 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata/51 with path /api/v1/expense-metadata/51 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:21 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:21 - getById: Find the source record
2025-05-23 12:01:22 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:22 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-subgroup/get/by-metadata/51 with path /api/v1/expense-subgroup/get/by-metadata/51 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:22 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:22 - doFilterInternal: Request came from http://localhost:8080/api/v1/locations with path /api/v1/locations and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:22 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:22 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/by-report-id/2237 with path /api/v1/expense/get/by-report-id/2237 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:22 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:22 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:22 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:22 - getAll: Returning view model list
2025-05-23 12:01:23 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:23 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/200197 with path /api/v1/expense/get/200197 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:23 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:23 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:23 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:23 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-subgroup/105 with path /api/v1/expense-subgroup/105 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:23 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-rules/1234 with path /api/v1/expense-rules/1234 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:23 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:23 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:23 - getRuleById: Finding the source record
2025-05-23 12:01:23 - getViewModel: Preparing view model
2025-05-23 12:01:23 - getViewModel: Copying fields to view model
2025-05-23 12:01:23 - getViewModel: Returning the view model
2025-05-23 12:01:25 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:01:25 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:01:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:01:28 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:28 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/200197 with path /api/v1/expense/get/200197 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:28 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/get/2237 with path /api/v1/expense-report/get/2237 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata/51 with path /api/v1/expense-metadata/51 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - getById: Find the source record
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-subgroup/get/by-metadata/51 with path /api/v1/expense-subgroup/get/by-metadata/51 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - doFilterInternal: Request came from http://localhost:8080/api/v1/locations with path /api/v1/locations and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/by-report-id/2237 with path /api/v1/expense/get/by-report-id/2237 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:40 - getAll: Returning view model list
2025-05-23 12:01:43 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/admin/queue with path /api/v1/expense-report/admin/queue and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata with path /api/v1/expense-metadata and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/admin/queue with path /api/v1/expense-report/admin/queue and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - getAll: Returning view model list
2025-05-23 12:01:43 -  Auth in Admin queue 13000  25364  [approver, user, admin, checker, report]
2025-05-23 12:01:43 -  Auth in Admin queue 13000  25364  [approver, user, admin, checker, report]
2025-05-23 12:01:43 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata with path /api/v1/expense-metadata and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:43 - getAll: Returning view model list
2025-05-23 12:01:49 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:49 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:49 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-metadata with path /api/v1/expense-metadata and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:49 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/admin/queue with path /api/v1/expense-report/admin/queue and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:49 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:49 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:49 - getAll: Returning view model list
2025-05-23 12:01:49 -  Auth in Admin queue 13000  25364  [approver, user, admin, checker, report]
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/similar/2240 with path /api/v1/expense-report/similar/2240 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/by-report-id/2240 with path /api/v1/expense/get/by-report-id/2240 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/get/2240 with path /api/v1/expense-report/get/2240 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/get/states/2240 with path /api/v1/expense-report/get/states/2240 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - doFilterInternal: Request came from http://localhost:8080/api/v1/dashboard with path /api/v1/dashboard and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:51 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:52 - getMonthlyClaimLine: From 2025-03-01 to 2025-03-31
2025-05-23 12:01:52 - getMonthlyClaimLine: From 2025-04-01 to 2025-04-30
2025-05-23 12:01:52 - getMonthlyClaimLine: From 2025-05-01 to 2025-05-31
2025-05-23 12:01:52 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:52 - doFilterInternal: Request came from http://localhost:8080/api/v1/dashboard/creator/900048/<EMAIL> with path /api/v1/dashboard/creator/900048/<EMAIL> and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:52 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:55 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:01:55 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:01:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/200199 with path /api/v1/expense/get/200199 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-rules/1006 with path /api/v1/expense-rules/1006 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-subgroup/79 with path /api/v1/expense-subgroup/79 and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - getRuleById: Finding the source record
2025-05-23 12:01:59 - getViewModel: Preparing view model
2025-05-23 12:01:59 - getViewModel: Copying fields to view model
2025-05-23 12:01:59 - getViewModel: Returning the view model
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/document/2/200199/invoice with path /api/v1/expense/document/2/200199/invoice and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/document/1/200199/invoice with path /api/v1/expense/document/1/200199/invoice and host 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:01:59 - getFile: Checking if the expense exists
2025-05-23 12:01:59 - getFile: Checking if the expense exists
2025-05-23 12:01:59 - getFile: Checking if the document upload url exists
2025-05-23 12:01:59 - getFile: Reading the file
2025-05-23 12:01:59 - getFile: Checking if the document upload url exists
2025-05-23 12:01:59 - getFile: Reading the file
2025-05-23 12:02:00 - getFile: Preparing the response
2025-05-23 12:02:00 - getFile: Preparing the response
2025-05-23 12:02:25 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:02:25 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:02:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:02:55 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:02:55 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:02:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:02:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:02:59 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/save with path /api/v1/expense/save and host 0:0:0:0:0:0:0:1
2025-05-23 12:02:59 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:02:59 - save: Fetching the applicable rule
2025-05-23 12:02:59 - upload: Starting upload for expenseId=200197 index=1
2025-05-23 12:02:59 - upload: Uploading payexpense/13000/2237/200197/295546d2-17eb-4697-9f91-07912be81a38 to bucket payexpense
2025-05-23 12:03:00 - upload: Saved expenseId=200197 with new document
2025-05-23 12:03:00 - save: Getting the validation status of the expense
2025-05-23 12:03:00 - getValidationStatus: Running validations on the expense
2025-05-23 12:03:00 - getValidationStatus: Validating travel descriptor
2025-05-23 12:03:00 - ExpenseValidationStatusViewModel: Travel descriptor is not applicable to this expense
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating mobility descriptor
2025-05-23 12:03:00 - ExpenseValidationStatusViewModel: Mobility descriptor is not applicable to this expense
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating transport descriptor
2025-05-23 12:03:00 - ExpenseValidationStatusViewModel: Transport descriptor is not applicable to this expense
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating claim and applicable amounts
2025-05-23 12:03:00 - validateClaimAndApplicableAmounts: Found valid
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating invoice and applicable amounts
2025-05-23 12:03:00 - validateInvoiceAndApplicableAmounts: Found valid
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating standard deduction computation
2025-05-23 12:03:00 - validateStandardDeduction: Standard deduction is not applicable, clearing related fields if any
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating per diem computation
2025-05-23 12:03:00 - validatePerDiem: Per diem doesn't apply to this expense
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating merchant details
2025-05-23 12:03:00 - validateMerchantDetails: Merchant details doesn't apply to this expense; clearing related fields if any
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating source location
2025-05-23 12:03:00 - validateSourceLocation: Source location doesn't apply to this expense; clearing related fields if any
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating destination location
2025-05-23 12:03:00 - validateDestinationLocation: Destination location doesn't apply to this expense; clearing related fields if any
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating date range
2025-05-23 12:03:00 - validateDateRange: Date range doesn't apply to this expense; clearing the related fields if any
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating expense identifier
2025-05-23 12:03:00 - validateExpenseIdentifier: Found valid
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating unit rate computation
2025-05-23 12:03:00 - validateUnitRateComputation: Unit rate doesn't apply to this expense; clearing related fields if any
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating bill amount
2025-05-23 12:03:00 - validateBillAmount: Claim amount is within the limit; found valid
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating date range overlap
2025-05-23 12:03:00 - validateDateRangeOverlap: Checking if the date range overlaps with earlier expense
2025-05-23 12:03:00 - validateDateRangeOverlap: Date range doesn't apply; found valid
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Validating Invoice attachment
2025-05-23 12:03:00 - validateAttachments: Found valid
2025-05-23 12:03:00 - getValidationStatus: Valid: true
2025-05-23 12:03:00 - getValidationStatus: Returning; Final validity: true
2025-05-23 12:03:00 - save: Checking if all validations are passing
2025-05-23 12:03:00 - save: Saving the updated expense
2025-05-23 12:03:00 - save: Save successful; exiting
2025-05-23 12:03:00 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:00 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/by-report-id/2237 with path /api/v1/expense/get/by-report-id/2237 and host 0:0:0:0:0:0:0:1
2025-05-23 12:03:00 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:00 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:00 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/save with path /api/v1/expense-report/save and host 0:0:0:0:0:0:0:1
2025-05-23 12:03:00 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:00 - Save: Performing validation for Expense Report id: 2237 created by user: <EMAIL>
2025-05-23 12:03:00 - validateMetadataLimits: getApplicable Rules For Expense Report id: 2237
2025-05-23 12:03:00 - getApplicableRule: Finding the Metadata Limit Rules for Report with id: 2237
2025-05-23 12:03:00 - validateMetadataLimits: Rules were found for for Expense Report id: 2237
2025-05-23 12:03:00 - validateMonthlyLimit: Started monthly limit checks for Expense Report id 2237
2025-05-23 12:03:00 - validateMonthlyLimit: Performing the filling of the hashmap for expensesSumByMonth for Expense Report id 2237
2025-05-23 12:03:00 - validateMonthlyLimit: The created hashmap is for Expense Report id 2237 
	{2025-05-01=IExpenseValidationAggregates{count=1, sum=0.0, expenseDate=2025-05-01}}
2025-05-23 12:03:00 - validateMonthlyLimit: Checking if location category rule is applicable for Expense Report id 2237
2025-05-23 12:03:00 - Validating Expense report id 2237 created by user : <EMAIL> took 0.10200000000000001 seconds
2025-05-23 12:03:00 - Save : Saving Expense report id: 2237 created by user : <EMAIL>
2025-05-23 12:03:01 - Saving Expense report id: 2237 created by user : <EMAIL> took 0.23600000000000002 seconds
2025-05-23 12:03:03 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/200197 with path /api/v1/expense/get/200197 and host 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-subgroup/105 with path /api/v1/expense-subgroup/105 and host 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-rules/1234 with path /api/v1/expense-rules/1234 and host 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - getRuleById: Finding the source record
2025-05-23 12:03:03 - getViewModel: Preparing view model
2025-05-23 12:03:03 - getViewModel: Copying fields to view model
2025-05-23 12:03:03 - getViewModel: Returning the view model
2025-05-23 12:03:03 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/document/1/200197/invoice with path /api/v1/expense/document/1/200197/invoice and host 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:03:03 - getFile: Checking if the expense exists
2025-05-23 12:03:03 - getFile: Checking if the document upload url exists
2025-05-23 12:03:03 - getFile: Reading the file
2025-05-23 12:03:03 - getFile: Preparing the response
2025-05-23 12:03:25 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:03:25 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:03:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:03:55 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:03:55 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:03:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:04:25 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:04:25 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:04:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:04:55 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:04:55 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:04:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:05:25 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:05:25 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:05:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:05:55 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:05:55 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:05:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:06:25 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:06:25 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:06:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:06:55 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:06:55 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:06:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:07:25 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:07:25 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:07:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:07:50 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:07:50 - HikariPool-1 - Shutdown initiated...
2025-05-23 12:07:50 - HikariPool-1 - Before shutdown stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@225ddf5f: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@180ae152: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4bd9f2c7: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@47c1e291: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@33ee0a56: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@53152e4: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@63d54f44: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@61542f01: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@612e2393: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1013b706: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@349d7bc2: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@77c42656: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6df6ce89: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2e46ca46: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1266675b: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@78e28d8f: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2d15f155: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@26cd7c0b: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1a2ee61f: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2a63a853: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@51a75385: (connection evicted)
2025-05-23 12:07:50 - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-05-23 12:07:50 - HikariPool-1 - Shutdown completed.
2025-05-23 12:07:51 - Starting PayExpensePvvApplication using Java 17.0.10 on Vedants-Mac-Studio.local with PID 9352 (/Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix/target/classes started by vedantdube in /Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix)
2025-05-23 12:07:51 - HV000001: Hibernate Validator 6.2.0.Final
2025-05-23 12:07:51 - Running with Spring Boot v2.5.9, Spring v5.3.15
2025-05-23 12:07:51 - The following profiles are active: tg-internal-gcp
2025-05-23 12:07:51 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-23 12:07:51 - Finished Spring Data repository scanning in 50 ms. Found 18 JPA repository interfaces.
2025-05-23 12:07:51 - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@733e6df7' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:07:51 - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:07:51 - Tomcat initialized with port(s): 8080 (http)
2025-05-23 12:07:51 - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-23 12:07:51 - Starting service [Tomcat]
2025-05-23 12:07:51 - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-05-23 12:07:52 - Initializing Spring embedded WebApplicationContext
2025-05-23 12:07:52 - Root WebApplicationContext: initialization completed in 830 ms
2025-05-23 12:07:52 - Filter 'appJwtAuthenticationFilter' configured for use
2025-05-23 12:07:52 - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-05-23 12:07:52 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-23 12:07:52 - HHH000412: Hibernate ORM core version 5.4.33
2025-05-23 12:07:52 - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-23 12:07:52 - HikariPool-1 - configuration:
2025-05-23 12:07:52 - allowPoolSuspension................................false
2025-05-23 12:07:52 - autoCommit................................true
2025-05-23 12:07:52 - catalog................................none
2025-05-23 12:07:52 - connectionInitSql................................none
2025-05-23 12:07:52 - connectionTestQuery................................none
2025-05-23 12:07:52 - connectionTimeout................................30000
2025-05-23 12:07:52 - dataSource................................none
2025-05-23 12:07:52 - dataSourceClassName................................none
2025-05-23 12:07:52 - dataSourceJNDI................................none
2025-05-23 12:07:52 - dataSourceProperties................................{password=<masked>}
2025-05-23 12:07:52 - driverClassName................................"com.mysql.cj.jdbc.Driver"
2025-05-23 12:07:52 - exceptionOverrideClassName................................none
2025-05-23 12:07:52 - healthCheckProperties................................{}
2025-05-23 12:07:52 - healthCheckRegistry................................none
2025-05-23 12:07:52 - idleTimeout................................***********-05-23 12:07:52 - initializationFailTimeout................................1
2025-05-23 12:07:52 - isolateInternalQueries................................false
2025-05-23 12:07:52 - jdbcUrl................................***************************************************
2025-05-23 12:07:52 - keepaliveTime................................0
2025-05-23 12:07:52 - leakDetectionThreshold................................0
2025-05-23 12:07:52 - maxLifetime................................1800000
2025-05-23 12:07:52 - maximumPoolSize................................150
2025-05-23 12:07:52 - metricRegistry................................none
2025-05-23 12:07:52 - metricsTrackerFactory................................none
2025-05-23 12:07:52 - minimumIdle................................20
2025-05-23 12:07:52 - password................................<masked>
2025-05-23 12:07:52 - poolName................................"HikariPool-1"
2025-05-23 12:07:52 - readOnly................................false
2025-05-23 12:07:52 - registerMbeans................................false
2025-05-23 12:07:52 - scheduledExecutor................................none
2025-05-23 12:07:52 - schema................................none
2025-05-23 12:07:52 - threadFactory................................internal
2025-05-23 12:07:52 - transactionIsolation................................default
2025-05-23 12:07:52 - username................................"taxgenie"
2025-05-23 12:07:52 - validationTimeout................................5000
2025-05-23 12:07:52 - HikariPool-1 - Starting...
2025-05-23 12:07:53 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@545f0b6
2025-05-23 12:07:53 - HikariPool-1 - Start completed.
2025-05-23 12:07:53 - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-05-23 12:07:53 - HikariPool-1 - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:07:53 - HikariPool-1 - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:07:54 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4af54899
2025-05-23 12:07:55 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2ef7977f
2025-05-23 12:07:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1841cd66
2025-05-23 12:07:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@37fa2dd4
2025-05-23 12:07:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@61f7d61
2025-05-23 12:07:57 - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-23 12:07:57 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:07:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f363854
2025-05-23 12:07:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:57 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:58 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:58 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7dee7a1a
2025-05-23 12:07:58 - The default project ID is noble-stratum-393405
2025-05-23 12:07:58 - Default credentials provider for <NAME_EMAIL>
2025-05-23 12:07:58 - Scopes in use by default credentials: [https://www.googleapis.com/auth/pubsub, https://www.googleapis.com/auth/spanner.admin, https://www.googleapis.com/auth/spanner.data, https://www.googleapis.com/auth/datastore, https://www.googleapis.com/auth/sqlservice.admin, https://www.googleapis.com/auth/devstorage.read_only, https://www.googleapis.com/auth/devstorage.read_write, https://www.googleapis.com/auth/cloudruntimeconfig, https://www.googleapis.com/auth/trace.append, https://www.googleapis.com/auth/cloud-platform, https://www.googleapis.com/auth/cloud-vision, https://www.googleapis.com/auth/bigquery, https://www.googleapis.com/auth/monitoring.write]
2025-05-23 12:07:58 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:58 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:58 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:58 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:58 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:58 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:07:58 - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-23 12:07:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@157a0408
2025-05-23 12:07:58 - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5fd81b2c, org.springframework.security.web.context.SecurityContextPersistenceFilter@1353f515, org.springframework.security.web.header.HeaderWriterFilter@1b27c0d5, org.springframework.web.filter.CorsFilter@60b616c8, org.springframework.security.web.authentication.logout.LogoutFilter@7ea2ce9d, in.taxgenie.pay_expense_pvv.auth.AppJwtAuthenticationFilter@6c9a3661, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3b5b6df4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3afca969, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2591f310, org.springframework.security.web.session.SessionManagementFilter@150f3678, org.springframework.security.web.access.ExceptionTranslationFilter@4fa9533f]
2025-05-23 12:07:58 - Starting ProtocolHandler ["http-nio-8080"]
2025-05-23 12:07:58 - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-23 12:07:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d37e314
2025-05-23 12:07:59 - Started PayExpensePvvApplication in 8.162 seconds (JVM running for 8.438)
2025-05-23 12:07:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3ea6f215
2025-05-23 12:07:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@b521df3
2025-05-23 12:08:00 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5ba51e4d
2025-05-23 12:08:00 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4ecec267
2025-05-23 12:08:01 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@33766a49
2025-05-23 12:08:01 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3168ad28
2025-05-23 12:08:01 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@f72d234
2025-05-23 12:08:02 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@71050843
2025-05-23 12:08:02 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@44d9c5b2
2025-05-23 12:08:02 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4a1e7d98
2025-05-23 12:08:02 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:08:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:08:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:08:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:08:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:08:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:08:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:09:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:09:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:09:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:09:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:09:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:09:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:10:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:10:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:10:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:10:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:10:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:10:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:11:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:11:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:11:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:11:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:11:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:11:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:12:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:12:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:12:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:12:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:12:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:12:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:13:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:13:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:13:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:13:31 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:13:31 - HikariPool-1 - Shutdown initiated...
2025-05-23 12:13:31 - HikariPool-1 - Before shutdown stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@545f0b6: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4af54899: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2ef7977f: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1841cd66: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@37fa2dd4: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@61f7d61: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4f363854: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7dee7a1a: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@157a0408: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6d37e314: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3ea6f215: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@b521df3: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5ba51e4d: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4ecec267: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@33766a49: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3168ad28: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@f72d234: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@71050843: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@44d9c5b2: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4a1e7d98: (connection evicted)
2025-05-23 12:13:31 - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-05-23 12:13:31 - HikariPool-1 - Shutdown completed.
2025-05-23 12:13:32 - Starting PayExpensePvvApplication using Java 17.0.10 on Vedants-Mac-Studio.local with PID 10739 (/Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix/target/classes started by vedantdube in /Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix)
2025-05-23 12:13:32 - Running with Spring Boot v2.5.9, Spring v5.3.15
2025-05-23 12:13:32 - The following profiles are active: tg-internal-gcp
2025-05-23 12:13:32 - HV000001: Hibernate Validator 6.2.0.Final
2025-05-23 12:13:32 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-23 12:13:32 - Finished Spring Data repository scanning in 50 ms. Found 18 JPA repository interfaces.
2025-05-23 12:13:32 - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@741ac284' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:13:32 - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:13:32 - Tomcat initialized with port(s): 8080 (http)
2025-05-23 12:13:32 - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-23 12:13:32 - Starting service [Tomcat]
2025-05-23 12:13:32 - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-05-23 12:13:33 - Initializing Spring embedded WebApplicationContext
2025-05-23 12:13:33 - Root WebApplicationContext: initialization completed in 832 ms
2025-05-23 12:13:33 - Filter 'appJwtAuthenticationFilter' configured for use
2025-05-23 12:13:33 - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-05-23 12:13:33 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-23 12:13:33 - HHH000412: Hibernate ORM core version 5.4.33
2025-05-23 12:13:33 - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-23 12:13:33 - HikariPool-1 - configuration:
2025-05-23 12:13:33 - allowPoolSuspension................................false
2025-05-23 12:13:33 - autoCommit................................true
2025-05-23 12:13:33 - catalog................................none
2025-05-23 12:13:33 - connectionInitSql................................none
2025-05-23 12:13:33 - connectionTestQuery................................none
2025-05-23 12:13:33 - connectionTimeout................................30000
2025-05-23 12:13:33 - dataSource................................none
2025-05-23 12:13:33 - dataSourceClassName................................none
2025-05-23 12:13:33 - dataSourceJNDI................................none
2025-05-23 12:13:33 - dataSourceProperties................................{password=<masked>}
2025-05-23 12:13:33 - driverClassName................................"com.mysql.cj.jdbc.Driver"
2025-05-23 12:13:33 - exceptionOverrideClassName................................none
2025-05-23 12:13:33 - healthCheckProperties................................{}
2025-05-23 12:13:33 - healthCheckRegistry................................none
2025-05-23 12:13:33 - idleTimeout................................***********-05-23 12:13:33 - initializationFailTimeout................................1
2025-05-23 12:13:33 - isolateInternalQueries................................false
2025-05-23 12:13:33 - jdbcUrl................................***************************************************
2025-05-23 12:13:33 - keepaliveTime................................0
2025-05-23 12:13:33 - leakDetectionThreshold................................0
2025-05-23 12:13:33 - maxLifetime................................1800000
2025-05-23 12:13:33 - maximumPoolSize................................150
2025-05-23 12:13:33 - metricRegistry................................none
2025-05-23 12:13:33 - metricsTrackerFactory................................none
2025-05-23 12:13:33 - minimumIdle................................20
2025-05-23 12:13:33 - password................................<masked>
2025-05-23 12:13:33 - poolName................................"HikariPool-1"
2025-05-23 12:13:33 - readOnly................................false
2025-05-23 12:13:33 - registerMbeans................................false
2025-05-23 12:13:33 - scheduledExecutor................................none
2025-05-23 12:13:33 - schema................................none
2025-05-23 12:13:33 - threadFactory................................internal
2025-05-23 12:13:33 - transactionIsolation................................default
2025-05-23 12:13:33 - username................................"taxgenie"
2025-05-23 12:13:33 - validationTimeout................................5000
2025-05-23 12:13:33 - HikariPool-1 - Starting...
2025-05-23 12:13:33 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3c9ef37b
2025-05-23 12:13:33 - HikariPool-1 - Start completed.
2025-05-23 12:13:33 - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-05-23 12:13:33 - HikariPool-1 - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:13:33 - HikariPool-1 - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:13:34 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4288eb24
2025-05-23 12:13:35 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@37a89105
2025-05-23 12:13:36 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@75e3a717
2025-05-23 12:13:36 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@701c62a2
2025-05-23 12:13:37 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b889a05
2025-05-23 12:13:37 - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-23 12:13:37 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:13:37 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7481acda
2025-05-23 12:13:37 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7c86da71
2025-05-23 12:13:37 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:37 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:37 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:37 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:38 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:38 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:38 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5c7727d9
2025-05-23 12:13:38 - The default project ID is noble-stratum-393405
2025-05-23 12:13:38 - Default credentials provider for <NAME_EMAIL>
2025-05-23 12:13:38 - Scopes in use by default credentials: [https://www.googleapis.com/auth/pubsub, https://www.googleapis.com/auth/spanner.admin, https://www.googleapis.com/auth/spanner.data, https://www.googleapis.com/auth/datastore, https://www.googleapis.com/auth/sqlservice.admin, https://www.googleapis.com/auth/devstorage.read_only, https://www.googleapis.com/auth/devstorage.read_write, https://www.googleapis.com/auth/cloudruntimeconfig, https://www.googleapis.com/auth/trace.append, https://www.googleapis.com/auth/cloud-platform, https://www.googleapis.com/auth/cloud-vision, https://www.googleapis.com/auth/bigquery, https://www.googleapis.com/auth/monitoring.write]
2025-05-23 12:13:38 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:38 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:38 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:38 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:38 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:38 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:13:38 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5a21a942
2025-05-23 12:13:38 - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-23 12:13:38 - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6d6d9406, org.springframework.security.web.context.SecurityContextPersistenceFilter@1b27c0d5, org.springframework.security.web.header.HeaderWriterFilter@6b27363b, org.springframework.web.filter.CorsFilter@3962ec84, org.springframework.security.web.authentication.logout.LogoutFilter@3c744de9, in.taxgenie.pay_expense_pvv.auth.AppJwtAuthenticationFilter@727986ad, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@bd62a6e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2e4084e0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7645ced6, org.springframework.security.web.session.SessionManagementFilter@4fa9533f, org.springframework.security.web.access.ExceptionTranslationFilter@1b5496fd]
2025-05-23 12:13:38 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@96ee7a4
2025-05-23 12:13:38 - Starting ProtocolHandler ["http-nio-8080"]
2025-05-23 12:13:38 - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-23 12:13:38 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@185010fc
2025-05-23 12:13:39 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@44651a04
2025-05-23 12:13:39 - Started PayExpensePvvApplication in 7.169 seconds (JVM running for 7.435)
2025-05-23 12:13:39 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@23036d0f
2025-05-23 12:13:39 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4604b18d
2025-05-23 12:13:40 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@634d0ad5
2025-05-23 12:13:40 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4c489e56
2025-05-23 12:13:40 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@18363ebc
2025-05-23 12:13:41 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@735f784d
2025-05-23 12:13:41 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@53962e55
2025-05-23 12:13:41 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:14:03 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:14:03 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:14:03 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:14:33 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:14:33 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:14:33 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:14:44 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:14:44 - HikariPool-1 - Shutdown initiated...
2025-05-23 12:14:44 - HikariPool-1 - Before shutdown stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3c9ef37b: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4288eb24: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@37a89105: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@75e3a717: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@701c62a2: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6b889a05: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7481acda: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7c86da71: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5c7727d9: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5a21a942: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@96ee7a4: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@185010fc: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@44651a04: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@23036d0f: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4604b18d: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@634d0ad5: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4c489e56: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@18363ebc: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@735f784d: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@53962e55: (connection evicted)
2025-05-23 12:14:44 - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-05-23 12:14:44 - HikariPool-1 - Shutdown completed.
2025-05-23 12:14:46 - HV000001: Hibernate Validator 6.2.0.Final
2025-05-23 12:14:46 - Starting PayExpensePvvApplication using Java 17.0.10 on Vedants-Mac-Studio.local with PID 11033 (/Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix/target/classes started by vedantdube in /Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix)
2025-05-23 12:14:46 - Running with Spring Boot v2.5.9, Spring v5.3.15
2025-05-23 12:14:46 - The following profiles are active: tg-internal-gcp
2025-05-23 12:14:46 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-23 12:14:46 - Finished Spring Data repository scanning in 49 ms. Found 18 JPA repository interfaces.
2025-05-23 12:14:46 - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@36c7cbe1' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:14:46 - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:14:46 - Tomcat initialized with port(s): 8080 (http)
2025-05-23 12:14:46 - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-23 12:14:46 - Starting service [Tomcat]
2025-05-23 12:14:46 - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-05-23 12:14:46 - Initializing Spring embedded WebApplicationContext
2025-05-23 12:14:46 - Root WebApplicationContext: initialization completed in 831 ms
2025-05-23 12:14:47 - Filter 'appJwtAuthenticationFilter' configured for use
2025-05-23 12:14:47 - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-05-23 12:14:47 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-23 12:14:47 - HHH000412: Hibernate ORM core version 5.4.33
2025-05-23 12:14:47 - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-23 12:14:47 - HikariPool-1 - configuration:
2025-05-23 12:14:47 - allowPoolSuspension................................false
2025-05-23 12:14:47 - autoCommit................................true
2025-05-23 12:14:47 - catalog................................none
2025-05-23 12:14:47 - connectionInitSql................................none
2025-05-23 12:14:47 - connectionTestQuery................................none
2025-05-23 12:14:47 - connectionTimeout................................30000
2025-05-23 12:14:47 - dataSource................................none
2025-05-23 12:14:47 - dataSourceClassName................................none
2025-05-23 12:14:47 - dataSourceJNDI................................none
2025-05-23 12:14:47 - dataSourceProperties................................{password=<masked>}
2025-05-23 12:14:47 - driverClassName................................"com.mysql.cj.jdbc.Driver"
2025-05-23 12:14:47 - exceptionOverrideClassName................................none
2025-05-23 12:14:47 - healthCheckProperties................................{}
2025-05-23 12:14:47 - healthCheckRegistry................................none
2025-05-23 12:14:47 - idleTimeout................................***********-05-23 12:14:47 - initializationFailTimeout................................1
2025-05-23 12:14:47 - isolateInternalQueries................................false
2025-05-23 12:14:47 - jdbcUrl................................***************************************************
2025-05-23 12:14:47 - keepaliveTime................................0
2025-05-23 12:14:47 - leakDetectionThreshold................................0
2025-05-23 12:14:47 - maxLifetime................................1800000
2025-05-23 12:14:47 - maximumPoolSize................................150
2025-05-23 12:14:47 - metricRegistry................................none
2025-05-23 12:14:47 - metricsTrackerFactory................................none
2025-05-23 12:14:47 - minimumIdle................................20
2025-05-23 12:14:47 - password................................<masked>
2025-05-23 12:14:47 - poolName................................"HikariPool-1"
2025-05-23 12:14:47 - readOnly................................false
2025-05-23 12:14:47 - registerMbeans................................false
2025-05-23 12:14:47 - scheduledExecutor................................none
2025-05-23 12:14:47 - schema................................none
2025-05-23 12:14:47 - threadFactory................................internal
2025-05-23 12:14:47 - transactionIsolation................................default
2025-05-23 12:14:47 - username................................"taxgenie"
2025-05-23 12:14:47 - validationTimeout................................5000
2025-05-23 12:14:47 - HikariPool-1 - Starting...
2025-05-23 12:14:47 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2270f58d
2025-05-23 12:14:47 - HikariPool-1 - Start completed.
2025-05-23 12:14:47 - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-05-23 12:14:47 - HikariPool-1 - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:14:47 - HikariPool-1 - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:14:47 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2fc0a1d
2025-05-23 12:14:47 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7870b9fd
2025-05-23 12:14:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@37a3882d
2025-05-23 12:14:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7d015fab
2025-05-23 12:14:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@e4fe2ff
2025-05-23 12:14:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@f38af91
2025-05-23 12:14:48 - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-23 12:14:48 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:14:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@45ef99f6
2025-05-23 12:14:49 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@49edc38d
2025-05-23 12:14:49 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1ff8a8b6
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6e951ccb
2025-05-23 12:14:49 - The default project ID is noble-stratum-393405
2025-05-23 12:14:49 - Default credentials provider for <NAME_EMAIL>
2025-05-23 12:14:49 - Scopes in use by default credentials: [https://www.googleapis.com/auth/pubsub, https://www.googleapis.com/auth/spanner.admin, https://www.googleapis.com/auth/spanner.data, https://www.googleapis.com/auth/datastore, https://www.googleapis.com/auth/sqlservice.admin, https://www.googleapis.com/auth/devstorage.read_only, https://www.googleapis.com/auth/devstorage.read_write, https://www.googleapis.com/auth/cloudruntimeconfig, https://www.googleapis.com/auth/trace.append, https://www.googleapis.com/auth/cloud-platform, https://www.googleapis.com/auth/cloud-vision, https://www.googleapis.com/auth/bigquery, https://www.googleapis.com/auth/monitoring.write]
2025-05-23 12:14:49 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@58389916
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:14:49 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@565b3afb
2025-05-23 12:14:49 - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-23 12:14:49 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@650c2481
2025-05-23 12:14:49 - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@64bf60c7, org.springframework.security.web.context.SecurityContextPersistenceFilter@1afd9827, org.springframework.security.web.header.HeaderWriterFilter@8d0dcd9, org.springframework.web.filter.CorsFilter@3ccb12d, org.springframework.security.web.authentication.logout.LogoutFilter@6fd458ec, in.taxgenie.pay_expense_pvv.auth.AppJwtAuthenticationFilter@759a678a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6bd2dd5d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b07c4c8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@367628c8, org.springframework.security.web.session.SessionManagementFilter@65accb85, org.springframework.security.web.access.ExceptionTranslationFilter@502356ec]
2025-05-23 12:14:50 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3abd106f
2025-05-23 12:14:50 - Starting ProtocolHandler ["http-nio-8080"]
2025-05-23 12:14:50 - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-23 12:14:50 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2bf6cc08
2025-05-23 12:14:50 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@63e1229d
2025-05-23 12:14:50 - Started PayExpensePvvApplication in 4.422 seconds (JVM running for 4.699)
2025-05-23 12:14:50 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f97313e
2025-05-23 12:14:50 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@59610169
2025-05-23 12:14:50 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5f5740e2
2025-05-23 12:14:50 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:15:05 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 12:15:05 - Initializing Servlet 'dispatcherServlet'
2025-05-23 12:15:05 - Completed initialization in 1 ms
2025-05-23 12:15:05 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:05 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/200197 with path /api/v1/expense/get/200197 and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:05 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-subgroup/105 with path /api/v1/expense-subgroup/105 and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-rules/1234 with path /api/v1/expense-rules/1234 and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - getRuleById: Finding the source record
2025-05-23 12:15:06 - getViewModel: Preparing view model
2025-05-23 12:15:06 - getViewModel: Copying fields to view model
2025-05-23 12:15:06 - getViewModel: Returning the view model
2025-05-23 12:15:06 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/document/1/200197/invoice with path /api/v1/expense/document/1/200197/invoice and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:06 - getFile: Checking if the expense exists
2025-05-23 12:15:06 - getFile: Checking if the document upload url exists
2025-05-23 12:15:06 - getFile: Reading the file
2025-05-23 12:15:06 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@78c1d621
2025-05-23 12:15:07 - getFile: Preparing the response
2025-05-23 12:15:16 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:16 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/200197 with path /api/v1/expense/get/200197 and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:16 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:16 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:16 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/document/1/200197/invoice with path /api/v1/expense/document/1/200197/invoice and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:16 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:16 - getFile: Checking if the expense exists
2025-05-23 12:15:16 - getFile: Checking if the document upload url exists
2025-05-23 12:15:16 - getFile: Reading the file
2025-05-23 12:15:17 - getFile: Preparing the response
2025-05-23 12:15:17 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:15:17 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:15:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:15:20 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:20 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/by-report-id/2237 with path /api/v1/expense/get/by-report-id/2237 and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:20 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:20 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:20 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/save with path /api/v1/expense-report/save and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:20 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:21 - Save: Performing validation for Expense Report id: 2237 created by user: <EMAIL>
2025-05-23 12:15:21 - validateMetadataLimits: getApplicable Rules For Expense Report id: 2237
2025-05-23 12:15:21 - getApplicableRule: Finding the Metadata Limit Rules for Report with id: 2237
2025-05-23 12:15:21 - validateMetadataLimits: Rules were found for for Expense Report id: 2237
2025-05-23 12:15:21 - validateMonthlyLimit: Started monthly limit checks for Expense Report id 2237
2025-05-23 12:15:21 - validateMonthlyLimit: Performing the filling of the hashmap for expensesSumByMonth for Expense Report id 2237
2025-05-23 12:15:21 - validateMonthlyLimit: The created hashmap is for Expense Report id 2237 
	{2025-05-01=IExpenseValidationAggregates{count=1, sum=0.0, expenseDate=2025-05-01}}
2025-05-23 12:15:21 - validateMonthlyLimit: Checking if location category rule is applicable for Expense Report id 2237
2025-05-23 12:15:21 - Validating Expense report id 2237 created by user : <EMAIL> took 0.068 seconds
2025-05-23 12:15:21 - Save : Saving Expense report id: 2237 created by user : <EMAIL>
2025-05-23 12:15:21 - Saving Expense report id: 2237 created by user : <EMAIL> took 0.113 seconds
2025-05-23 12:15:22 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:22 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/create/2237/105 with path /api/v1/expense/create/2237/105 and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:22 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:22 - create: Creating a new Expense entity
2025-05-23 12:15:22 - create: Finding the applicable Expense Rule
2025-05-23 12:15:22 - getApplicableRule: Finding the Expense with id: 0
2025-05-23 12:15:22 - create: Attaching the applicable Expense Rule as id
2025-05-23 12:15:22 - create: Saving both Expense and Expense Metadata
2025-05-23 12:15:22 - SQL Error: 1364, SQLState: HY000
2025-05-23 12:15:22 - Field 'has_been_validated' doesn't have a default value
2025-05-23 12:15:22 - Exception caught: class org.springframework.orm.jpa.JpaSystemException; message: could not execute statement; nested exception is org.hibernate.exception.GenericJDBCException: could not execute statement
2025-05-23 12:15:35 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:35 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/200197 with path /api/v1/expense/get/200197 and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:35 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:35 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:35 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/document/1/200197/invoice with path /api/v1/expense/document/1/200197/invoice and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:35 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:35 - getFile: Checking if the expense exists
2025-05-23 12:15:35 - getFile: Checking if the document upload url exists
2025-05-23 12:15:35 - getFile: Reading the file
2025-05-23 12:15:36 - getFile: Preparing the response
2025-05-23 12:15:47 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:15:47 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:15:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:15:53 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:53 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/save with path /api/v1/expense/save and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:53 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:53 - save: Fetching the applicable rule
2025-05-23 12:15:53 - upload: Starting upload for expenseId=200197 index=1
2025-05-23 12:15:53 - Removed embedded scripts from PDF for expenseId=200197
2025-05-23 12:15:53 - upload: Uploading payexpense/13000/2237/200197/c2d07f77-c81f-42fd-89c6-9bf2810e42df to bucket payexpense
2025-05-23 12:15:53 - upload: Saved expenseId=200197 with new document
2025-05-23 12:15:53 - save: Getting the validation status of the expense
2025-05-23 12:15:53 - getValidationStatus: Running validations on the expense
2025-05-23 12:15:53 - getValidationStatus: Validating travel descriptor
2025-05-23 12:15:53 - ExpenseValidationStatusViewModel: Travel descriptor is not applicable to this expense
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating mobility descriptor
2025-05-23 12:15:53 - ExpenseValidationStatusViewModel: Mobility descriptor is not applicable to this expense
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating transport descriptor
2025-05-23 12:15:53 - ExpenseValidationStatusViewModel: Transport descriptor is not applicable to this expense
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating claim and applicable amounts
2025-05-23 12:15:53 - validateClaimAndApplicableAmounts: Found valid
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating invoice and applicable amounts
2025-05-23 12:15:53 - validateInvoiceAndApplicableAmounts: Found valid
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating standard deduction computation
2025-05-23 12:15:53 - validateStandardDeduction: Standard deduction is not applicable, clearing related fields if any
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating per diem computation
2025-05-23 12:15:53 - validatePerDiem: Per diem doesn't apply to this expense
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating merchant details
2025-05-23 12:15:53 - validateMerchantDetails: Merchant details doesn't apply to this expense; clearing related fields if any
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating source location
2025-05-23 12:15:53 - validateSourceLocation: Source location doesn't apply to this expense; clearing related fields if any
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating destination location
2025-05-23 12:15:53 - validateDestinationLocation: Destination location doesn't apply to this expense; clearing related fields if any
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating date range
2025-05-23 12:15:53 - validateDateRange: Date range doesn't apply to this expense; clearing the related fields if any
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating expense identifier
2025-05-23 12:15:53 - validateExpenseIdentifier: Found valid
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating unit rate computation
2025-05-23 12:15:53 - validateUnitRateComputation: Unit rate doesn't apply to this expense; clearing related fields if any
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating bill amount
2025-05-23 12:15:53 - validateBillAmount: Claim amount is within the limit; found valid
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating date range overlap
2025-05-23 12:15:53 - validateDateRangeOverlap: Checking if the date range overlaps with earlier expense
2025-05-23 12:15:53 - validateDateRangeOverlap: Date range doesn't apply; found valid
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Validating Invoice attachment
2025-05-23 12:15:53 - validateAttachments: Found valid
2025-05-23 12:15:53 - getValidationStatus: Valid: true
2025-05-23 12:15:53 - getValidationStatus: Returning; Final validity: true
2025-05-23 12:15:53 - save: Checking if all validations are passing
2025-05-23 12:15:53 - save: Saving the updated expense
2025-05-23 12:15:54 - save: Save successful; exiting
2025-05-23 12:15:54 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:54 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/by-report-id/2237 with path /api/v1/expense/get/by-report-id/2237 and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:54 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:54 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:54 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/save with path /api/v1/expense-report/save and host 0:0:0:0:0:0:0:1
2025-05-23 12:15:54 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:15:54 - Save: Performing validation for Expense Report id: 2237 created by user: <EMAIL>
2025-05-23 12:15:54 - validateMetadataLimits: getApplicable Rules For Expense Report id: 2237
2025-05-23 12:15:54 - getApplicableRule: Finding the Metadata Limit Rules for Report with id: 2237
2025-05-23 12:15:54 - validateMetadataLimits: Rules were found for for Expense Report id: 2237
2025-05-23 12:15:54 - validateMonthlyLimit: Started monthly limit checks for Expense Report id 2237
2025-05-23 12:15:55 - validateMonthlyLimit: Performing the filling of the hashmap for expensesSumByMonth for Expense Report id 2237
2025-05-23 12:15:55 - validateMonthlyLimit: The created hashmap is for Expense Report id 2237 
	{2025-05-01=IExpenseValidationAggregates{count=1, sum=0.0, expenseDate=2025-05-01}}
2025-05-23 12:15:55 - validateMonthlyLimit: Checking if location category rule is applicable for Expense Report id 2237
2025-05-23 12:15:55 - Validating Expense report id 2237 created by user : <EMAIL> took 0.171 seconds
2025-05-23 12:15:55 - Save : Saving Expense report id: 2237 created by user : <EMAIL>
2025-05-23 12:15:55 - Saving Expense report id: 2237 created by user : <EMAIL> took 0.248 seconds
2025-05-23 12:16:17 - HikariPool-1 - Before cleanup stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:16:17 - HikariPool-1 - After cleanup  stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:16:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:16:29 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:16:29 - HikariPool-1 - Shutdown initiated...
2025-05-23 12:16:29 - HikariPool-1 - Before shutdown stats (total=21, active=0, idle=21, waiting=0)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2270f58d: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2fc0a1d: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7870b9fd: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@37a3882d: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7d015fab: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@e4fe2ff: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@f38af91: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@45ef99f6: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@49edc38d: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1ff8a8b6: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6e951ccb: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@58389916: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@565b3afb: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@650c2481: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3abd106f: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2bf6cc08: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@63e1229d: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4f97313e: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@59610169: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5f5740e2: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@78c1d621: (connection evicted)
2025-05-23 12:16:29 - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-05-23 12:16:29 - HikariPool-1 - Shutdown completed.
2025-05-23 12:16:31 - HV000001: Hibernate Validator 6.2.0.Final
2025-05-23 12:16:31 - Starting PayExpensePvvApplication using Java 17.0.10 on Vedants-Mac-Studio.local with PID 11420 (/Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix/target/classes started by vedantdube in /Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix)
2025-05-23 12:16:31 - Running with Spring Boot v2.5.9, Spring v5.3.15
2025-05-23 12:16:31 - The following profiles are active: tg-internal-gcp
2025-05-23 12:16:31 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-23 12:16:31 - Finished Spring Data repository scanning in 50 ms. Found 18 JPA repository interfaces.
2025-05-23 12:16:31 - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@108e9837' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:16:31 - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:16:32 - Tomcat initialized with port(s): 8080 (http)
2025-05-23 12:16:32 - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-23 12:16:32 - Starting service [Tomcat]
2025-05-23 12:16:32 - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-05-23 12:16:32 - Initializing Spring embedded WebApplicationContext
2025-05-23 12:16:32 - Root WebApplicationContext: initialization completed in 825 ms
2025-05-23 12:16:32 - Filter 'appJwtAuthenticationFilter' configured for use
2025-05-23 12:16:32 - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-05-23 12:16:32 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-23 12:16:32 - HHH000412: Hibernate ORM core version 5.4.33
2025-05-23 12:16:32 - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-23 12:16:32 - HikariPool-1 - configuration:
2025-05-23 12:16:32 - allowPoolSuspension................................false
2025-05-23 12:16:32 - autoCommit................................true
2025-05-23 12:16:32 - catalog................................none
2025-05-23 12:16:32 - connectionInitSql................................none
2025-05-23 12:16:32 - connectionTestQuery................................none
2025-05-23 12:16:32 - connectionTimeout................................30000
2025-05-23 12:16:32 - dataSource................................none
2025-05-23 12:16:32 - dataSourceClassName................................none
2025-05-23 12:16:32 - dataSourceJNDI................................none
2025-05-23 12:16:32 - dataSourceProperties................................{password=<masked>}
2025-05-23 12:16:32 - driverClassName................................"com.mysql.cj.jdbc.Driver"
2025-05-23 12:16:32 - exceptionOverrideClassName................................none
2025-05-23 12:16:32 - healthCheckProperties................................{}
2025-05-23 12:16:32 - healthCheckRegistry................................none
2025-05-23 12:16:32 - idleTimeout................................***********-05-23 12:16:32 - initializationFailTimeout................................1
2025-05-23 12:16:32 - isolateInternalQueries................................false
2025-05-23 12:16:32 - jdbcUrl................................***************************************************
2025-05-23 12:16:32 - keepaliveTime................................0
2025-05-23 12:16:32 - leakDetectionThreshold................................0
2025-05-23 12:16:32 - maxLifetime................................1800000
2025-05-23 12:16:32 - maximumPoolSize................................150
2025-05-23 12:16:32 - metricRegistry................................none
2025-05-23 12:16:32 - metricsTrackerFactory................................none
2025-05-23 12:16:32 - minimumIdle................................20
2025-05-23 12:16:32 - password................................<masked>
2025-05-23 12:16:32 - poolName................................"HikariPool-1"
2025-05-23 12:16:32 - readOnly................................false
2025-05-23 12:16:32 - registerMbeans................................false
2025-05-23 12:16:32 - scheduledExecutor................................none
2025-05-23 12:16:32 - schema................................none
2025-05-23 12:16:32 - threadFactory................................internal
2025-05-23 12:16:32 - transactionIsolation................................default
2025-05-23 12:16:32 - username................................"taxgenie"
2025-05-23 12:16:32 - validationTimeout................................5000
2025-05-23 12:16:32 - HikariPool-1 - Starting...
2025-05-23 12:16:32 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@57b3d869
2025-05-23 12:16:32 - HikariPool-1 - Start completed.
2025-05-23 12:16:32 - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-05-23 12:16:32 - HikariPool-1 - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:16:32 - HikariPool-1 - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:16:34 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@295d0209
2025-05-23 12:16:34 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b3e8551
2025-05-23 12:16:34 - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-23 12:16:34 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:16:34 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3cc15116
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - The default project ID is noble-stratum-393405
2025-05-23 12:16:35 - Default credentials provider for <NAME_EMAIL>
2025-05-23 12:16:35 - Scopes in use by default credentials: [https://www.googleapis.com/auth/pubsub, https://www.googleapis.com/auth/spanner.admin, https://www.googleapis.com/auth/spanner.data, https://www.googleapis.com/auth/datastore, https://www.googleapis.com/auth/sqlservice.admin, https://www.googleapis.com/auth/devstorage.read_only, https://www.googleapis.com/auth/devstorage.read_write, https://www.googleapis.com/auth/cloudruntimeconfig, https://www.googleapis.com/auth/trace.append, https://www.googleapis.com/auth/cloud-platform, https://www.googleapis.com/auth/cloud-vision, https://www.googleapis.com/auth/bigquery, https://www.googleapis.com/auth/monitoring.write]
2025-05-23 12:16:35 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@438e39ad
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:35 - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-23 12:16:36 - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@62ee252b, org.springframework.security.web.context.SecurityContextPersistenceFilter@150f3678, org.springframework.security.web.header.HeaderWriterFilter@4fa9533f, org.springframework.web.filter.CorsFilter@3181d4de, org.springframework.security.web.authentication.logout.LogoutFilter@38138629, in.taxgenie.pay_expense_pvv.auth.AppJwtAuthenticationFilter@7b95bdb0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1353f515, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@684f20ea, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6f535d77, org.springframework.security.web.session.SessionManagementFilter@206421da, org.springframework.security.web.access.ExceptionTranslationFilter@726cda5b]
2025-05-23 12:16:36 - Starting ProtocolHandler ["http-nio-8080"]
2025-05-23 12:16:36 - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-23 12:16:36 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@514a08c6
2025-05-23 12:16:36 - Started PayExpensePvvApplication in 5.554 seconds (JVM running for 5.832)
2025-05-23 12:16:36 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f451498
2025-05-23 12:16:37 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3038931a
2025-05-23 12:16:38 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@15f0e66e
2025-05-23 12:16:39 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3a5f6128
2025-05-23 12:16:39 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@69380cf0
2025-05-23 12:16:40 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@23e09cf9
2025-05-23 12:16:40 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b397cc1
2025-05-23 12:16:41 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e328fe7
2025-05-23 12:16:41 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@69a19b4b
2025-05-23 12:16:42 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f931341
2025-05-23 12:16:42 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@62b03b72
2025-05-23 12:16:42 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@387c201b
2025-05-23 12:16:43 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@19e3e221
2025-05-23 12:16:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@35fc70e1
2025-05-23 12:16:44 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:16:48 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:16:48 - HikariPool-1 - Shutdown initiated...
2025-05-23 12:16:48 - HikariPool-1 - Before shutdown stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@57b3d869: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@295d0209: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6b3e8551: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3cc15116: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@438e39ad: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@514a08c6: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4f451498: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3038931a: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@15f0e66e: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3a5f6128: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@69380cf0: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@23e09cf9: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6b397cc1: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4e328fe7: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@69a19b4b: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4f931341: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@62b03b72: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@387c201b: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@19e3e221: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@35fc70e1: (connection evicted)
2025-05-23 12:16:48 - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-05-23 12:16:48 - HikariPool-1 - Shutdown completed.
2025-05-23 12:16:49 - HV000001: Hibernate Validator 6.2.0.Final
2025-05-23 12:16:49 - Starting PayExpensePvvApplication using Java 17.0.10 on Vedants-Mac-Studio.local with PID 11493 (/Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix/target/classes started by vedantdube in /Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix)
2025-05-23 12:16:49 - Running with Spring Boot v2.5.9, Spring v5.3.15
2025-05-23 12:16:49 - The following profiles are active: tg-internal-gcp
2025-05-23 12:16:49 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-23 12:16:49 - Finished Spring Data repository scanning in 47 ms. Found 18 JPA repository interfaces.
2025-05-23 12:16:50 - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@52f6900a' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:16:50 - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:16:50 - Tomcat initialized with port(s): 8080 (http)
2025-05-23 12:16:50 - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-23 12:16:50 - Starting service [Tomcat]
2025-05-23 12:16:50 - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-05-23 12:16:50 - Initializing Spring embedded WebApplicationContext
2025-05-23 12:16:50 - Root WebApplicationContext: initialization completed in 780 ms
2025-05-23 12:16:50 - Filter 'appJwtAuthenticationFilter' configured for use
2025-05-23 12:16:50 - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-05-23 12:16:50 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-23 12:16:50 - HHH000412: Hibernate ORM core version 5.4.33
2025-05-23 12:16:50 - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-23 12:16:50 - HikariPool-1 - configuration:
2025-05-23 12:16:50 - allowPoolSuspension................................false
2025-05-23 12:16:50 - autoCommit................................true
2025-05-23 12:16:50 - catalog................................none
2025-05-23 12:16:50 - connectionInitSql................................none
2025-05-23 12:16:50 - connectionTestQuery................................none
2025-05-23 12:16:50 - connectionTimeout................................30000
2025-05-23 12:16:50 - dataSource................................none
2025-05-23 12:16:50 - dataSourceClassName................................none
2025-05-23 12:16:50 - dataSourceJNDI................................none
2025-05-23 12:16:50 - dataSourceProperties................................{password=<masked>}
2025-05-23 12:16:50 - driverClassName................................"com.mysql.cj.jdbc.Driver"
2025-05-23 12:16:50 - exceptionOverrideClassName................................none
2025-05-23 12:16:50 - healthCheckProperties................................{}
2025-05-23 12:16:50 - healthCheckRegistry................................none
2025-05-23 12:16:50 - idleTimeout................................***********-05-23 12:16:50 - initializationFailTimeout................................1
2025-05-23 12:16:50 - isolateInternalQueries................................false
2025-05-23 12:16:50 - jdbcUrl................................***************************************************
2025-05-23 12:16:50 - keepaliveTime................................0
2025-05-23 12:16:50 - leakDetectionThreshold................................0
2025-05-23 12:16:50 - maxLifetime................................1800000
2025-05-23 12:16:50 - maximumPoolSize................................150
2025-05-23 12:16:50 - metricRegistry................................none
2025-05-23 12:16:50 - metricsTrackerFactory................................none
2025-05-23 12:16:50 - minimumIdle................................20
2025-05-23 12:16:50 - password................................<masked>
2025-05-23 12:16:50 - poolName................................"HikariPool-1"
2025-05-23 12:16:50 - readOnly................................false
2025-05-23 12:16:50 - registerMbeans................................false
2025-05-23 12:16:50 - scheduledExecutor................................none
2025-05-23 12:16:50 - schema................................none
2025-05-23 12:16:50 - threadFactory................................internal
2025-05-23 12:16:50 - transactionIsolation................................default
2025-05-23 12:16:50 - username................................"taxgenie"
2025-05-23 12:16:50 - validationTimeout................................5000
2025-05-23 12:16:50 - HikariPool-1 - Starting...
2025-05-23 12:16:51 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7831d1aa
2025-05-23 12:16:51 - HikariPool-1 - Start completed.
2025-05-23 12:16:51 - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-05-23 12:16:51 - HikariPool-1 - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:16:51 - HikariPool-1 - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:16:51 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1ac2854e
2025-05-23 12:16:52 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3b5c1fc6
2025-05-23 12:16:52 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@709d6cc2
2025-05-23 12:16:54 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3bd2edec
2025-05-23 12:16:54 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@54992a52
2025-05-23 12:16:54 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@491fa43e
2025-05-23 12:16:54 - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-23 12:16:54 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:16:55 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e843fc4
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - The default project ID is noble-stratum-393405
2025-05-23 12:16:55 - Default credentials provider for <NAME_EMAIL>
2025-05-23 12:16:55 - Scopes in use by default credentials: [https://www.googleapis.com/auth/pubsub, https://www.googleapis.com/auth/spanner.admin, https://www.googleapis.com/auth/spanner.data, https://www.googleapis.com/auth/datastore, https://www.googleapis.com/auth/sqlservice.admin, https://www.googleapis.com/auth/devstorage.read_only, https://www.googleapis.com/auth/devstorage.read_write, https://www.googleapis.com/auth/cloudruntimeconfig, https://www.googleapis.com/auth/trace.append, https://www.googleapis.com/auth/cloud-platform, https://www.googleapis.com/auth/cloud-vision, https://www.googleapis.com/auth/bigquery, https://www.googleapis.com/auth/monitoring.write]
2025-05-23 12:16:55 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@66cee606
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:16:55 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@a4212ef
2025-05-23 12:16:55 - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-23 12:16:56 - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@22d72c99, org.springframework.security.web.context.SecurityContextPersistenceFilter@15800a35, org.springframework.security.web.header.HeaderWriterFilter@56ca8151, org.springframework.web.filter.CorsFilter@2cc75b25, org.springframework.security.web.authentication.logout.LogoutFilter@2e4c4373, in.taxgenie.pay_expense_pvv.auth.AppJwtAuthenticationFilter@b8a144e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@f8d6d7d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4a501cab, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7d3f7e4b, org.springframework.security.web.session.SessionManagementFilter@3f7ff364, org.springframework.security.web.access.ExceptionTranslationFilter@5de6a9b9]
2025-05-23 12:16:56 - Starting ProtocolHandler ["http-nio-8080"]
2025-05-23 12:16:56 - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-23 12:16:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@24d4999f
2025-05-23 12:16:56 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@68e2dd13
2025-05-23 12:16:56 - Started PayExpensePvvApplication in 7.391 seconds (JVM running for 7.665)
2025-05-23 12:16:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4be1f366
2025-05-23 12:16:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7822639a
2025-05-23 12:16:58 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 12:16:58 - Initializing Servlet 'dispatcherServlet'
2025-05-23 12:16:58 - Completed initialization in 1 ms
2025-05-23 12:16:58 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:16:58 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/200197 with path /api/v1/expense/get/200197 and host 0:0:0:0:0:0:0:1
2025-05-23 12:16:58 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:16:58 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:16:58 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/document/1/200197/invoice with path /api/v1/expense/document/1/200197/invoice and host 0:0:0:0:0:0:0:1
2025-05-23 12:16:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2ab7e4cb
2025-05-23 12:16:58 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:16:58 - getFile: Checking if the expense exists
2025-05-23 12:16:58 - getFile: Checking if the document upload url exists
2025-05-23 12:16:58 - getFile: Reading the file
2025-05-23 12:16:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@29fe4173
2025-05-23 12:16:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@656457ec
2025-05-23 12:17:01 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@66547059
2025-05-23 12:17:01 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2d68db3b
2025-05-23 12:17:01 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@48424f66
2025-05-23 12:17:01 - HikariPool-1 - After adding stats (total=20, active=1, idle=19, waiting=0)
2025-05-23 12:17:02 - getFile: Preparing the response
2025-05-23 12:17:11 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:17:11 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/save with path /api/v1/expense/save and host 0:0:0:0:0:0:0:1
2025-05-23 12:17:11 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:17:11 - save: Fetching the applicable rule
2025-05-23 12:17:11 - upload: Starting upload for expenseId=200197 index=1
2025-05-23 12:17:11 - 
WARNING: THERE WERE EMBEDDED SCRIPTS IN PDF for expenseId=200197!!!

2025-05-23 12:17:11 - upload: Uploading payexpense/13000/2237/200197/67515fa2-87ac-423f-9c5c-d639c5f00a44 to bucket payexpense
2025-05-23 12:17:11 - upload: Saved expenseId=200197 with new document
2025-05-23 12:17:11 - save: Getting the validation status of the expense
2025-05-23 12:17:11 - getValidationStatus: Running validations on the expense
2025-05-23 12:17:11 - getValidationStatus: Validating travel descriptor
2025-05-23 12:17:11 - ExpenseValidationStatusViewModel: Travel descriptor is not applicable to this expense
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating mobility descriptor
2025-05-23 12:17:11 - ExpenseValidationStatusViewModel: Mobility descriptor is not applicable to this expense
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating transport descriptor
2025-05-23 12:17:11 - ExpenseValidationStatusViewModel: Transport descriptor is not applicable to this expense
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating claim and applicable amounts
2025-05-23 12:17:11 - validateClaimAndApplicableAmounts: Found valid
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating invoice and applicable amounts
2025-05-23 12:17:11 - validateInvoiceAndApplicableAmounts: Found valid
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating standard deduction computation
2025-05-23 12:17:11 - validateStandardDeduction: Standard deduction is not applicable, clearing related fields if any
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating per diem computation
2025-05-23 12:17:11 - validatePerDiem: Per diem doesn't apply to this expense
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating merchant details
2025-05-23 12:17:11 - validateMerchantDetails: Merchant details doesn't apply to this expense; clearing related fields if any
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating source location
2025-05-23 12:17:11 - validateSourceLocation: Source location doesn't apply to this expense; clearing related fields if any
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating destination location
2025-05-23 12:17:11 - validateDestinationLocation: Destination location doesn't apply to this expense; clearing related fields if any
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating date range
2025-05-23 12:17:11 - validateDateRange: Date range doesn't apply to this expense; clearing the related fields if any
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating expense identifier
2025-05-23 12:17:11 - validateExpenseIdentifier: Found valid
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating unit rate computation
2025-05-23 12:17:11 - validateUnitRateComputation: Unit rate doesn't apply to this expense; clearing related fields if any
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating bill amount
2025-05-23 12:17:11 - validateBillAmount: Claim amount is within the limit; found valid
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating date range overlap
2025-05-23 12:17:11 - validateDateRangeOverlap: Checking if the date range overlaps with earlier expense
2025-05-23 12:17:11 - validateDateRangeOverlap: Date range doesn't apply; found valid
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Validating Invoice attachment
2025-05-23 12:17:11 - validateAttachments: Found valid
2025-05-23 12:17:11 - getValidationStatus: Valid: true
2025-05-23 12:17:11 - getValidationStatus: Returning; Final validity: true
2025-05-23 12:17:11 - save: Checking if all validations are passing
2025-05-23 12:17:11 - save: Saving the updated expense
2025-05-23 12:17:11 - save: Save successful; exiting
2025-05-23 12:17:12 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:17:12 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense/get/by-report-id/2237 with path /api/v1/expense/get/by-report-id/2237 and host 0:0:0:0:0:0:0:1
2025-05-23 12:17:12 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:17:12 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:17:12 - doFilterInternal: Request came from http://localhost:8080/api/v1/expense-report/save with path /api/v1/expense-report/save and host 0:0:0:0:0:0:0:1
2025-05-23 12:17:12 - Traffic from: 0:0:0:0:0:0:0:1
2025-05-23 12:17:12 - Save: Performing validation for Expense Report id: 2237 created by user: <EMAIL>
2025-05-23 12:17:12 - validateMetadataLimits: getApplicable Rules For Expense Report id: 2237
2025-05-23 12:17:12 - getApplicableRule: Finding the Metadata Limit Rules for Report with id: 2237
2025-05-23 12:17:12 - validateMetadataLimits: Rules were found for for Expense Report id: 2237
2025-05-23 12:17:12 - validateMonthlyLimit: Started monthly limit checks for Expense Report id 2237
2025-05-23 12:17:12 - validateMonthlyLimit: Performing the filling of the hashmap for expensesSumByMonth for Expense Report id 2237
2025-05-23 12:17:12 - validateMonthlyLimit: The created hashmap is for Expense Report id 2237 
	{2025-05-01=IExpenseValidationAggregates{count=1, sum=0.0, expenseDate=2025-05-01}}
2025-05-23 12:17:12 - validateMonthlyLimit: Checking if location category rule is applicable for Expense Report id 2237
2025-05-23 12:17:12 - Validating Expense report id 2237 created by user : <EMAIL> took 0.052000000000000005 seconds
2025-05-23 12:17:12 - Save : Saving Expense report id: 2237 created by user : <EMAIL>
2025-05-23 12:17:12 - Saving Expense report id: 2237 created by user : <EMAIL> took 0.08 seconds
2025-05-23 12:17:21 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:17:21 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:17:21 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:17:41 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:17:41 - HikariPool-1 - Shutdown initiated...
2025-05-23 12:17:41 - HikariPool-1 - Before shutdown stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7831d1aa: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1ac2854e: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3b5c1fc6: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@709d6cc2: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3bd2edec: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@54992a52: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@491fa43e: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4e843fc4: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@66cee606: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@a4212ef: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@24d4999f: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@68e2dd13: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4be1f366: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7822639a: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2ab7e4cb: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@29fe4173: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@656457ec: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@66547059: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2d68db3b: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@48424f66: (connection evicted)
2025-05-23 12:17:41 - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-05-23 12:17:41 - HikariPool-1 - Shutdown completed.
2025-05-23 12:17:42 - HV000001: Hibernate Validator 6.2.0.Final
2025-05-23 12:17:42 - Starting PayExpensePvvApplication using Java 17.0.10 on Vedants-Mac-Studio.local with PID 11693 (/Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix/target/classes started by vedantdube in /Users/<USER>/Workspace/Taxgenie/Development/PayExpense/pay_expense_phoenix)
2025-05-23 12:17:42 - Running with Spring Boot v2.5.9, Spring v5.3.15
2025-05-23 12:17:42 - The following profiles are active: tg-internal-gcp
2025-05-23 12:17:43 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-23 12:17:43 - Finished Spring Data repository scanning in 56 ms. Found 18 JPA repository interfaces.
2025-05-23 12:17:43 - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@9b47400' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:17:43 - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-23 12:17:43 - Tomcat initialized with port(s): 8080 (http)
2025-05-23 12:17:43 - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-23 12:17:43 - Starting service [Tomcat]
2025-05-23 12:17:43 - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-05-23 12:17:43 - Initializing Spring embedded WebApplicationContext
2025-05-23 12:17:43 - Root WebApplicationContext: initialization completed in 806 ms
2025-05-23 12:17:43 - Filter 'appJwtAuthenticationFilter' configured for use
2025-05-23 12:17:43 - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-05-23 12:17:43 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-23 12:17:43 - HHH000412: Hibernate ORM core version 5.4.33
2025-05-23 12:17:43 - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-23 12:17:43 - HikariPool-1 - configuration:
2025-05-23 12:17:43 - allowPoolSuspension................................false
2025-05-23 12:17:43 - autoCommit................................true
2025-05-23 12:17:43 - catalog................................none
2025-05-23 12:17:43 - connectionInitSql................................none
2025-05-23 12:17:43 - connectionTestQuery................................none
2025-05-23 12:17:43 - connectionTimeout................................30000
2025-05-23 12:17:43 - dataSource................................none
2025-05-23 12:17:43 - dataSourceClassName................................none
2025-05-23 12:17:43 - dataSourceJNDI................................none
2025-05-23 12:17:43 - dataSourceProperties................................{password=<masked>}
2025-05-23 12:17:43 - driverClassName................................"com.mysql.cj.jdbc.Driver"
2025-05-23 12:17:43 - exceptionOverrideClassName................................none
2025-05-23 12:17:43 - healthCheckProperties................................{}
2025-05-23 12:17:43 - healthCheckRegistry................................none
2025-05-23 12:17:43 - idleTimeout................................***********-05-23 12:17:43 - initializationFailTimeout................................1
2025-05-23 12:17:43 - isolateInternalQueries................................false
2025-05-23 12:17:43 - jdbcUrl................................***************************************************
2025-05-23 12:17:43 - keepaliveTime................................0
2025-05-23 12:17:43 - leakDetectionThreshold................................0
2025-05-23 12:17:43 - maxLifetime................................1800000
2025-05-23 12:17:43 - maximumPoolSize................................150
2025-05-23 12:17:43 - metricRegistry................................none
2025-05-23 12:17:43 - metricsTrackerFactory................................none
2025-05-23 12:17:43 - minimumIdle................................20
2025-05-23 12:17:43 - password................................<masked>
2025-05-23 12:17:43 - poolName................................"HikariPool-1"
2025-05-23 12:17:43 - readOnly................................false
2025-05-23 12:17:43 - registerMbeans................................false
2025-05-23 12:17:43 - scheduledExecutor................................none
2025-05-23 12:17:43 - schema................................none
2025-05-23 12:17:43 - threadFactory................................internal
2025-05-23 12:17:43 - transactionIsolation................................default
2025-05-23 12:17:43 - username................................"taxgenie"
2025-05-23 12:17:43 - validationTimeout................................5000
2025-05-23 12:17:43 - HikariPool-1 - Starting...
2025-05-23 12:17:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@b56d8a7
2025-05-23 12:17:44 - HikariPool-1 - Start completed.
2025-05-23 12:17:44 - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-05-23 12:17:44 - HikariPool-1 - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:17:44 - HikariPool-1 - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
2025-05-23 12:17:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@63c0813c
2025-05-23 12:17:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@50221aca
2025-05-23 12:17:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5574c7ce
2025-05-23 12:17:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e3a19fe
2025-05-23 12:17:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5eeee074
2025-05-23 12:17:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@26da8519
2025-05-23 12:17:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@715a3e58
2025-05-23 12:17:45 - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-23 12:17:45 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 12:17:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@771392b4
2025-05-23 12:17:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5b2ef5bb
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@a2ffd0f
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - The default project ID is noble-stratum-393405
2025-05-23 12:17:45 - Default credentials provider for <NAME_EMAIL>
2025-05-23 12:17:45 - Scopes in use by default credentials: [https://www.googleapis.com/auth/pubsub, https://www.googleapis.com/auth/spanner.admin, https://www.googleapis.com/auth/spanner.data, https://www.googleapis.com/auth/datastore, https://www.googleapis.com/auth/sqlservice.admin, https://www.googleapis.com/auth/devstorage.read_only, https://www.googleapis.com/auth/devstorage.read_write, https://www.googleapis.com/auth/cloudruntimeconfig, https://www.googleapis.com/auth/trace.append, https://www.googleapis.com/auth/cloud-platform, https://www.googleapis.com/auth/cloud-vision, https://www.googleapis.com/auth/bigquery, https://www.googleapis.com/auth/monitoring.write]
2025-05-23 12:17:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f24ebc8
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:45 - HHH000174: Function template anticipated 4 arguments, but 1 arguments encountered
2025-05-23 12:17:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@334c2648
2025-05-23 12:17:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@59d90126
2025-05-23 12:17:46 - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-23 12:17:46 - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6626ae40, org.springframework.security.web.context.SecurityContextPersistenceFilter@5de6a9b9, org.springframework.security.web.header.HeaderWriterFilter@4667fd40, org.springframework.web.filter.CorsFilter@33de7f3d, org.springframework.security.web.authentication.logout.LogoutFilter@12387c79, in.taxgenie.pay_expense_pvv.auth.AppJwtAuthenticationFilter@714e861f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@56ca8151, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@56b972a8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@684f20ea, org.springframework.security.web.session.SessionManagementFilter@8cad98b, org.springframework.security.web.access.ExceptionTranslationFilter@5163bb14]
2025-05-23 12:17:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3e5a6b50
2025-05-23 12:17:46 - Starting ProtocolHandler ["http-nio-8080"]
2025-05-23 12:17:46 - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-23 12:17:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@35189276
2025-05-23 12:17:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@14db517d
2025-05-23 12:17:46 - Started PayExpensePvvApplication in 4.349 seconds (JVM running for 4.615)
2025-05-23 12:17:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3479575d
2025-05-23 12:17:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@efab476
2025-05-23 12:17:47 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@57b18396
2025-05-23 12:17:47 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:18:14 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:18:14 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:18:14 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:18:44 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:18:44 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:18:44 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:19:14 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:19:14 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:19:14 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:19:44 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:19:44 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:19:44 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:20:14 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:20:14 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:20:14 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:20:44 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:20:44 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:20:44 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:21:14 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:21:14 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:21:14 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:21:45 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:21:45 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:21:45 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:22:15 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:22:15 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:22:15 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:23:41 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m25s559ms).
2025-05-23 12:23:41 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:23:41 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:23:41 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:25:04 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m23s741ms).
2025-05-23 12:25:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:25:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:25:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:25:34 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:25:34 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:25:34 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:26:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:26:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:26:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:26:34 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:26:34 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:26:34 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:27:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:27:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:27:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:27:34 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:27:34 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:27:34 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:28:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:28:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:28:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:28:34 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:28:34 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:28:34 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:29:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:29:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:29:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:29:34 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:29:34 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:29:34 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:30:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:30:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:30:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:30:34 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:30:34 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:30:34 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:31:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:31:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:31:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:31:34 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:31:34 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:31:34 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:32:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:32:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:32:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:32:34 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:32:34 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:32:34 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:33:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:33:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:33:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:33:34 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:33:34 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:33:34 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:34:04 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:34:04 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:34:04 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:34:36 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:34:36 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:34:36 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:35:06 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:35:06 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:35:06 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:35:43 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:35:43 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:35:43 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:36:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:36:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:36:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:36:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:36:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:36:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:37:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:37:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:37:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:37:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:37:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:37:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:38:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:38:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:38:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:38:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:38:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:38:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:39:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:39:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:39:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:39:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:39:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:39:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:40:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:40:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:40:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:40:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:40:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:40:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:41:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:41:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:41:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:41:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:41:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:41:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:42:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:42:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:42:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:42:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:42:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:42:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:43:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:43:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:43:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:43:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:43:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:43:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:44:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:44:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:44:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:44:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:44:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:44:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:45:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:45:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:45:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:45:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:45:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:45:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:46:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:46:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:46:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:46:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:46:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:46:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:47:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:47:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:47:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:47:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:47:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:47:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:48:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:48:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:48:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:48:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:48:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:48:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:49:11 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5574c7ce: (connection has passed maxLifetime)
2025-05-23 12:49:11 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@550cd6d4
2025-05-23 12:49:16 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@771392b4: (connection has passed maxLifetime)
2025-05-23 12:49:16 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@61b80d94
2025-05-23 12:49:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:49:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:49:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:49:25 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@35189276: (connection has passed maxLifetime)
2025-05-23 12:49:25 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 12:49:25 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5eeee074: (connection has passed maxLifetime)
2025-05-23 12:49:25 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@732226a9
2025-05-23 12:49:26 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7e7fb871
2025-05-23 12:49:26 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:49:26 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@a2ffd0f: (connection has passed maxLifetime)
2025-05-23 12:49:26 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7593d15
2025-05-23 12:49:31 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@b56d8a7: (connection has passed maxLifetime)
2025-05-23 12:49:31 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6f45388d
2025-05-23 12:49:33 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2e3a19fe: (connection has passed maxLifetime)
2025-05-23 12:49:33 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@252eb0f8
2025-05-23 12:49:35 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@26da8519: (connection has passed maxLifetime)
2025-05-23 12:49:35 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@30218b77
2025-05-23 12:49:45 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@14db517d: (connection has passed maxLifetime)
2025-05-23 12:49:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@39811073
2025-05-23 12:49:45 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4f24ebc8: (connection has passed maxLifetime)
2025-05-23 12:49:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@a6bfd84
2025-05-23 12:49:46 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3e5a6b50: (connection has passed maxLifetime)
2025-05-23 12:49:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@129067b0
2025-05-23 12:49:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@efab476: (connection has passed maxLifetime)
2025-05-23 12:49:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@22efcf07
2025-05-23 12:49:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@59d90126: (connection has passed maxLifetime)
2025-05-23 12:49:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3479575d: (connection has passed maxLifetime)
2025-05-23 12:49:48 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 12:49:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@51934bb4
2025-05-23 12:49:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@417f8cf6
2025-05-23 12:49:48 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:49:49 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@57b18396: (connection has passed maxLifetime)
2025-05-23 12:49:49 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@36864863
2025-05-23 12:49:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5b2ef5bb: (connection has passed maxLifetime)
2025-05-23 12:49:50 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@34c63830
2025-05-23 12:49:51 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@50221aca: (connection has passed maxLifetime)
2025-05-23 12:49:51 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2ea280b8
2025-05-23 12:49:52 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@334c2648: (connection has passed maxLifetime)
2025-05-23 12:49:52 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@63c0813c: (connection has passed maxLifetime)
2025-05-23 12:49:52 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 12:49:52 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@132c105e
2025-05-23 12:49:52 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@730e776e
2025-05-23 12:49:52 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:49:53 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@715a3e58: (connection has passed maxLifetime)
2025-05-23 12:49:53 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7fb13c7d
2025-05-23 12:49:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:49:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:49:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:50:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:50:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:50:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:50:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:50:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:50:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:51:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:51:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:51:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:51:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:51:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:51:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:52:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:52:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:52:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:52:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:52:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:52:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:53:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:53:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:53:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:53:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:53:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:53:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:54:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:54:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:54:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:54:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:54:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:54:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:55:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:55:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:55:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:55:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:55:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:55:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:56:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:56:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:56:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:56:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:56:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:56:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:57:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:57:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:57:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:57:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:57:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:57:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:58:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:58:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:58:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:58:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:58:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:58:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:59:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:59:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:59:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 12:59:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:59:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 12:59:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:00:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:00:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:00:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:00:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:00:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:00:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:01:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:01:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:01:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:01:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:01:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:01:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:02:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:02:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:02:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:02:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:02:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:02:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:03:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:03:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:03:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:03:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:03:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:03:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:04:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:04:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:04:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:04:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:04:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:04:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:08:11 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m17s641ms).
2025-05-23 13:08:11 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:08:11 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:08:11 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:14:09 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m57s583ms).
2025-05-23 13:14:09 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:14:09 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:14:09 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:14:39 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:14:39 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:14:39 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:15:09 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:15:09 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:15:09 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:15:40 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:15:40 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:15:40 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:19:44 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=4m4s227ms).
2025-05-23 13:19:44 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:19:44 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:19:44 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:20:14 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:20:14 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:20:14 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:20:44 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:20:44 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:20:44 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:21:14 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:21:14 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:21:14 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:21:44 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:21:44 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:21:44 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:22:15 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:22:15 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:22:15 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:22:45 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:22:45 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:22:45 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:23:15 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:23:15 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:23:15 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:23:45 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:23:45 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:23:45 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:24:15 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:24:15 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:24:15 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:24:45 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:24:45 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:24:45 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:25:15 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:25:15 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:25:15 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:25:45 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:25:45 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:25:45 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:26:15 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:26:15 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:26:15 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:26:45 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:26:45 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:26:45 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:27:15 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:27:15 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:27:15 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:27:45 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:27:45 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:27:45 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:41:49 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=14m3s908ms).
2025-05-23 13:41:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:41:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:41:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:42:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:42:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:42:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:49:41 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=7m21s826ms).
2025-05-23 13:49:41 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:49:41 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:49:41 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:50:11 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:50:11 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:50:11 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:50:41 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:50:41 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:50:41 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:50:49 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@61b80d94: (connection has passed maxLifetime)
2025-05-23 13:50:49 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@218c6efb
2025-05-23 13:51:07 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6f45388d: (connection has passed maxLifetime)
2025-05-23 13:51:07 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1acc7c6d
2025-05-23 13:51:09 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@732226a9: (connection has passed maxLifetime)
2025-05-23 13:51:09 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@542a8169
2025-05-23 13:51:09 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7593d15: (connection has passed maxLifetime)
2025-05-23 13:51:09 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4deb930f
2025-05-23 13:51:11 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:51:11 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:51:11 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:51:23 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@550cd6d4: (connection has passed maxLifetime)
2025-05-23 13:51:24 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@f69c93a
2025-05-23 13:51:24 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@252eb0f8: (connection has passed maxLifetime)
2025-05-23 13:51:24 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4a60a10b
2025-05-23 13:51:26 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@34c63830: (connection has passed maxLifetime)
2025-05-23 13:51:26 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@af80ce7
2025-05-23 13:51:30 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@51934bb4: (connection has passed maxLifetime)
2025-05-23 13:51:30 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7e7fb871: (connection has passed maxLifetime)
2025-05-23 13:51:30 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 13:51:30 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@730e776e: (connection has passed maxLifetime)
2025-05-23 13:51:30 - HikariPool-1 - Add connection elided, waiting 0, queue 2
2025-05-23 13:51:30 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6621c3f6
2025-05-23 13:51:30 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@434f12b
2025-05-23 13:51:30 - HikariPool-1 - After adding stats (total=19, active=0, idle=19, waiting=0)
2025-05-23 13:51:30 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@759cd9d3
2025-05-23 13:51:30 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:51:39 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@39811073: (connection has passed maxLifetime)
2025-05-23 13:51:39 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@22e2b77a
2025-05-23 13:51:39 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@132c105e: (connection has passed maxLifetime)
2025-05-23 13:51:39 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@774a3955
2025-05-23 13:51:41 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:51:41 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:51:41 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:51:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@22efcf07: (connection has passed maxLifetime)
2025-05-23 13:51:41 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@34686715
2025-05-23 13:51:43 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@30218b77: (connection has passed maxLifetime)
2025-05-23 13:51:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3252b5b0
2025-05-23 13:51:45 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2ea280b8: (connection has passed maxLifetime)
2025-05-23 13:51:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4c44a1bb
2025-05-23 13:51:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@a6bfd84: (connection has passed maxLifetime)
2025-05-23 13:51:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5e20337c
2025-05-23 13:51:52 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@129067b0: (connection has passed maxLifetime)
2025-05-23 13:51:52 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@17aa480f
2025-05-23 13:51:57 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@36864863: (connection has passed maxLifetime)
2025-05-23 13:51:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@32201096
2025-05-23 13:51:59 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@417f8cf6: (connection has passed maxLifetime)
2025-05-23 13:51:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6415a555
2025-05-23 13:52:00 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7fb13c7d: (connection has passed maxLifetime)
2025-05-23 13:52:00 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@75699cdf
2025-05-23 13:52:11 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:52:11 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:52:11 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:52:41 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:52:41 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:52:41 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:53:11 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:53:11 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:53:11 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:53:41 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:53:41 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:53:41 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:54:11 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:54:11 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:54:11 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 13:54:41 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:54:41 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 13:54:41 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:11:32 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m50s916ms).
2025-05-23 14:11:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:11:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:11:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:12:02 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:12:02 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:12:02 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:12:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:12:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:12:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:13:02 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:13:02 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:13:02 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:13:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:13:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:13:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:14:02 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:14:02 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:14:02 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:14:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:14:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:14:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:15:02 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:15:02 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:15:02 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:15:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:15:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:15:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:16:02 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:16:02 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:16:02 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:16:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:16:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:16:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:17:02 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:17:02 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:17:02 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:17:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:17:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:17:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:18:02 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:18:02 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:18:02 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:18:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:18:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:18:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:19:02 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:19:02 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:19:02 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:19:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:19:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:19:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:20:02 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:20:02 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:20:02 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:20:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:20:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:20:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:36:50 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m18s523ms).
2025-05-23 14:36:50 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:36:50 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:36:50 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:37:20 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:37:20 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:37:20 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:42:59 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m38s785ms).
2025-05-23 14:42:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:42:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:42:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:46:29 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m30s273ms).
2025-05-23 14:46:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:46:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:46:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:46:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:46:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:46:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:47:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:47:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:47:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:57:51 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=10m21s332ms).
2025-05-23 14:57:51 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:57:51 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:57:51 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 14:58:21 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:58:21 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 14:58:21 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:13:49 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m28s29ms).
2025-05-23 15:13:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:13:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:13:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:14:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:14:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:14:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:19:25 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m5s846ms).
2025-05-23 15:19:25 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:19:25 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:19:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:19:55 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:19:55 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:19:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:20:59 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m4s272ms).
2025-05-23 15:20:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:20:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:20:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:21:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:21:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:21:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:21:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:21:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:21:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:22:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:22:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:22:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:22:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:22:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:22:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:23:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:23:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:23:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:23:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:23:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:23:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:24:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:24:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:24:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:24:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:24:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:24:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:25:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:25:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:25:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:25:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:25:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:25:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:26:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:26:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:26:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:26:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:26:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:26:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:27:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:27:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:27:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:27:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:27:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:27:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:28:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:28:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:28:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:28:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:28:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:28:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:29:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:29:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:29:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:29:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:29:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:29:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:30:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:30:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:30:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:30:53 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@218c6efb: (connection has passed maxLifetime)
2025-05-23 15:30:59 - HikariPool-1 - Before cleanup stats (total=19, active=0, idle=19, waiting=0)
2025-05-23 15:30:59 - HikariPool-1 - After cleanup  stats (total=19, active=0, idle=19, waiting=0)
2025-05-23 15:30:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:31:01 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4ae9f6fe
2025-05-23 15:31:04 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@759cd9d3: (connection has passed maxLifetime)
2025-05-23 15:31:05 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@50eb0ccb
2025-05-23 15:31:12 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@f69c93a: (connection has passed maxLifetime)
2025-05-23 15:31:13 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1acc7c6d: (connection has passed maxLifetime)
2025-05-23 15:31:13 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 15:31:13 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@510f8c7d
2025-05-23 15:31:14 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2d05f5f
2025-05-23 15:31:14 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:31:16 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@542a8169: (connection has passed maxLifetime)
2025-05-23 15:31:16 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6621c3f6: (connection has passed maxLifetime)
2025-05-23 15:31:16 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 15:31:17 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@790ae59
2025-05-23 15:31:18 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6e7adb05
2025-05-23 15:31:18 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:31:19 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@af80ce7: (connection has passed maxLifetime)
2025-05-23 15:31:21 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@774a3955: (connection has passed maxLifetime)
2025-05-23 15:31:21 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 15:31:21 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1c727639
2025-05-23 15:31:22 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@24f8d25d
2025-05-23 15:31:22 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:31:27 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4deb930f: (connection has passed maxLifetime)
2025-05-23 15:31:28 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@569f2f62
2025-05-23 15:31:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:31:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:31:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:31:32 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5e20337c: (connection has passed maxLifetime)
2025-05-23 15:31:33 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@17aa480f: (connection has passed maxLifetime)
2025-05-23 15:31:33 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 15:31:33 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@20a74404
2025-05-23 15:31:34 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2af40db
2025-05-23 15:31:34 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:31:34 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4c44a1bb: (connection has passed maxLifetime)
2025-05-23 15:31:34 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@49df9324
2025-05-23 15:31:36 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4a60a10b: (connection has passed maxLifetime)
2025-05-23 15:31:37 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@150e9432
2025-05-23 15:31:43 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@22e2b77a: (connection has passed maxLifetime)
2025-05-23 15:31:43 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 15:31:43 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@434f12b: (connection has passed maxLifetime)
2025-05-23 15:31:43 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@539d097e
2025-05-23 15:31:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@47603f40
2025-05-23 15:31:44 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:31:45 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@32201096: (connection has passed maxLifetime)
2025-05-23 15:31:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@10190c9f
2025-05-23 15:31:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3252b5b0: (connection has passed maxLifetime)
2025-05-23 15:31:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3ac98f96
2025-05-23 15:31:52 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6415a555: (connection has passed maxLifetime)
2025-05-23 15:31:52 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@18f3ccd
2025-05-23 15:31:52 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@34686715: (connection has passed maxLifetime)
2025-05-23 15:31:53 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@351684c2
2025-05-23 15:31:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:31:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:31:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:32:04 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@75699cdf: (connection has passed maxLifetime)
2025-05-23 15:32:05 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@565df5e4
2025-05-23 15:32:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:32:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:32:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:32:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:32:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:32:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:33:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:33:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:33:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:33:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:33:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:33:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:34:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:34:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:34:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:34:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:34:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:34:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:35:29 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:35:29 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:35:29 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:36:00 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:36:00 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:36:00 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:36:30 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:36:30 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:36:30 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:38:32 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m2s501ms).
2025-05-23 15:38:32 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:38:32 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:38:32 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:46:11 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=7m38s248ms).
2025-05-23 15:46:11 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:46:11 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:46:11 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:46:41 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:46:41 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:46:41 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:47:12 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:47:12 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:47:12 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:48:10 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=57s696ms).
2025-05-23 15:48:10 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:48:10 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:48:10 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:48:40 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:48:40 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:48:40 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:49:30 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=50s399ms).
2025-05-23 15:49:30 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:49:30 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:49:30 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:50:00 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:50:00 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:50:00 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:50:56 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=56s276ms).
2025-05-23 15:50:56 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:50:56 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:50:56 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:52:11 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m14s858ms).
2025-05-23 15:52:11 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:52:11 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:52:11 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:52:41 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:52:41 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:52:41 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:53:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:53:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:53:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:53:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:53:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:53:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:54:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:54:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:54:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:54:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:54:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:54:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:55:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:55:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:55:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:55:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:55:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:55:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:56:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:56:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:56:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:56:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:56:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:56:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:57:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:57:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:57:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:57:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:57:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:57:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:58:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:58:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:58:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:58:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:58:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:58:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:59:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:59:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:59:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 15:59:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:59:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 15:59:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:00:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:00:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:00:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:00:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:00:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:00:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:01:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:01:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:01:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:01:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:01:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:01:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:02:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:02:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:02:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:02:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:02:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:02:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:03:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:03:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:03:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:03:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:03:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:03:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:04:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:04:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:04:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:04:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:04:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:04:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:05:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:05:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:05:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:05:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:05:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:05:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:06:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:06:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:06:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:06:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:06:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:06:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:07:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:07:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:07:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:07:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:07:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:07:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:08:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:08:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:08:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:08:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:08:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:08:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:09:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:09:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:09:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:09:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:09:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:09:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:10:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:10:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:10:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:10:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:10:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:10:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:11:05 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4ae9f6fe: (connection has passed maxLifetime)
2025-05-23 16:11:05 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@44d81a81
2025-05-23 16:11:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:11:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:11:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:11:33 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@569f2f62: (connection has passed maxLifetime)
2025-05-23 16:11:33 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3f428962
2025-05-23 16:11:34 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6e7adb05: (connection has passed maxLifetime)
2025-05-23 16:11:34 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@53f25f66
2025-05-23 16:11:36 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@20a74404: (connection has passed maxLifetime)
2025-05-23 16:11:37 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3e3c02d1
2025-05-23 16:11:43 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@50eb0ccb: (connection has passed maxLifetime)
2025-05-23 16:11:43 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4b9e7cf9
2025-05-23 16:11:45 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2d05f5f: (connection has passed maxLifetime)
2025-05-23 16:11:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1f6ab353
2025-05-23 16:11:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:11:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:11:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:11:53 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@510f8c7d: (connection has passed maxLifetime)
2025-05-23 16:11:53 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@64e4c804
2025-05-23 16:12:00 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@24f8d25d: (connection has passed maxLifetime)
2025-05-23 16:12:01 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3f87715d
2025-05-23 16:12:01 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@47603f40: (connection has passed maxLifetime)
2025-05-23 16:12:01 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d4e7fdc
2025-05-23 16:12:04 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@790ae59: (connection has passed maxLifetime)
2025-05-23 16:12:05 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@19e666bc
2025-05-23 16:12:06 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@49df9324: (connection has passed maxLifetime)
2025-05-23 16:12:06 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@55d83b15
2025-05-23 16:12:07 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2af40db: (connection has passed maxLifetime)
2025-05-23 16:12:07 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7978d1ca
2025-05-23 16:12:08 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@10190c9f: (connection has passed maxLifetime)
2025-05-23 16:12:08 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1c727639: (connection has passed maxLifetime)
2025-05-23 16:12:08 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 16:12:08 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1ab56e33
2025-05-23 16:12:09 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@ec72144
2025-05-23 16:12:09 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:12:10 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@18f3ccd: (connection has passed maxLifetime)
2025-05-23 16:12:10 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2900cf5a
2025-05-23 16:12:13 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@150e9432: (connection has passed maxLifetime)
2025-05-23 16:12:13 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@696e95d3
2025-05-23 16:12:14 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@565df5e4: (connection has passed maxLifetime)
2025-05-23 16:12:14 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7a831d69
2025-05-23 16:12:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:12:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:12:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:12:21 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@539d097e: (connection has passed maxLifetime)
2025-05-23 16:12:21 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@72fd3793
2025-05-23 16:12:27 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3ac98f96: (connection has passed maxLifetime)
2025-05-23 16:12:27 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@77a440d3
2025-05-23 16:12:36 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@351684c2: (connection has passed maxLifetime)
2025-05-23 16:12:36 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@517c4c5f
2025-05-23 16:12:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:12:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:12:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:13:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:13:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:13:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:13:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:13:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:13:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:14:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:14:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:14:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:14:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:14:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:14:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:15:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:15:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:15:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:15:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:15:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:15:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:16:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:16:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:16:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:16:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:16:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:16:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:17:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:17:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:17:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:17:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:17:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:17:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:18:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:18:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:18:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:18:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:18:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:18:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:19:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:19:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:19:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:19:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:19:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:19:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:20:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:20:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:20:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:20:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:20:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:20:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:21:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:21:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:21:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:21:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:21:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:21:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:22:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:22:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:22:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:22:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:22:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:22:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:23:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:23:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:23:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:23:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:23:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:23:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:24:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:24:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:24:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:24:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:24:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:24:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:25:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:25:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:25:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:25:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:25:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:25:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:26:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:26:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:26:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:26:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:26:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:26:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:27:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:27:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:27:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:27:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:27:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:27:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:28:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:28:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:28:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:28:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:28:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:28:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:29:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:29:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:29:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:29:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:29:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:29:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:30:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:30:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:30:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:30:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:30:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:30:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:31:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:31:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:31:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:31:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:31:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:31:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:32:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:32:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:32:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:32:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:32:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:32:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:33:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:33:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:33:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:33:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:33:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:33:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:34:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:34:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:34:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:34:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:34:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:34:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:35:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:35:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:35:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:35:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:35:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:35:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:36:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:36:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:36:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:36:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:36:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:36:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:37:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:37:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:37:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:37:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:37:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:37:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:38:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:38:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:38:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:38:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:38:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:38:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:39:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:39:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:39:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:39:48 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:39:48 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:39:48 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:40:18 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:40:18 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:40:18 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:41:03 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@44d81a81: (connection has passed maxLifetime)
2025-05-23 16:41:03 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=45s251ms).
2025-05-23 16:41:03 - HikariPool-1 - Before cleanup stats (total=19, active=0, idle=19, waiting=0)
2025-05-23 16:41:03 - HikariPool-1 - After cleanup  stats (total=19, active=0, idle=19, waiting=0)
2025-05-23 16:41:03 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:41:04 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2be4baeb
2025-05-23 16:41:04 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3f428962: (connection has passed maxLifetime)
2025-05-23 16:41:05 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@60e86cad
2025-05-23 16:41:21 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4b9e7cf9: (connection has passed maxLifetime)
2025-05-23 16:41:21 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@387d661f
2025-05-23 16:46:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3e3c02d1: (connection has passed maxLifetime)
2025-05-23 16:46:40 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m37s290ms).
2025-05-23 16:46:40 - HikariPool-1 - Before cleanup stats (total=19, active=0, idle=19, waiting=0)
2025-05-23 16:46:40 - HikariPool-1 - After cleanup  stats (total=19, active=0, idle=19, waiting=0)
2025-05-23 16:46:40 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:46:40 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@55fe553e
2025-05-23 16:46:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1f6ab353: (connection has passed maxLifetime)
2025-05-23 16:46:42 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@40b76b9b
2025-05-23 16:46:44 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@64e4c804: (connection has passed maxLifetime)
2025-05-23 16:46:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6c0f6e37
2025-05-23 16:46:46 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3f87715d: (connection has passed maxLifetime)
2025-05-23 16:46:46 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@793ea190
2025-05-23 16:46:56 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@53f25f66: (connection has passed maxLifetime)
2025-05-23 16:46:57 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@24d14700
2025-05-23 16:46:59 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@19e666bc: (connection has passed maxLifetime)
2025-05-23 16:46:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@dd7f6b5
2025-05-23 16:47:08 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@696e95d3: (connection has passed maxLifetime)
2025-05-23 16:47:08 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1ac72736
2025-05-23 16:47:09 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2900cf5a: (connection has passed maxLifetime)
2025-05-23 16:47:09 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@15c26278
2025-05-23 16:47:10 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7a831d69: (connection has passed maxLifetime)
2025-05-23 16:47:10 - HikariPool-1 - Before cleanup stats (total=19, active=0, idle=19, waiting=0)
2025-05-23 16:47:10 - HikariPool-1 - After cleanup  stats (total=19, active=0, idle=19, waiting=0)
2025-05-23 16:47:10 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:47:10 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@188f91d0
2025-05-23 16:47:12 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@55d83b15: (connection has passed maxLifetime)
2025-05-23 16:47:12 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2877ccf7
2025-05-23 16:47:13 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7978d1ca: (connection has passed maxLifetime)
2025-05-23 16:47:13 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@33181d6
2025-05-23 16:47:18 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@ec72144: (connection has passed maxLifetime)
2025-05-23 16:47:19 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@38c62d70
2025-05-23 16:48:12 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6d4e7fdc: (connection has passed maxLifetime)
2025-05-23 16:48:13 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@22d9d4a0
2025-05-23 16:48:15 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@517c4c5f: (connection has passed maxLifetime)
2025-05-23 16:48:15 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3c54550e
2025-05-23 16:48:17 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1ab56e33: (connection has passed maxLifetime)
2025-05-23 16:48:17 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@23bc1fb0
2025-05-23 16:48:17 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@77a440d3: (connection has passed maxLifetime)
2025-05-23 16:48:17 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1369dbc4
2025-05-23 16:48:27 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@72fd3793: (connection has passed maxLifetime)
2025-05-23 16:48:28 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1472284e
2025-05-23 16:48:30 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m20s134ms).
2025-05-23 16:48:30 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:48:30 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:48:30 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:49:53 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m22s611ms).
2025-05-23 16:49:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:49:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:49:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:50:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:50:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:50:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:50:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:50:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:50:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:51:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:51:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:51:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:51:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:51:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:51:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:52:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:52:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:52:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:52:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:52:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:52:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:53:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:53:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:53:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:53:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:53:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:53:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:54:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:54:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:54:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:54:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:54:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:54:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:55:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:55:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:55:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:55:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:55:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:55:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:56:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:56:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:56:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:56:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:56:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:56:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:57:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:57:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:57:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:57:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:57:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:57:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:58:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:58:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:58:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:58:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:58:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:58:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:59:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:59:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:59:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 16:59:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:59:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 16:59:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:00:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:00:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:00:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:00:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:00:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:00:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:01:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:01:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:01:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:01:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:01:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:01:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:02:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:02:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:02:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:02:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:02:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:02:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:03:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:03:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:03:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:03:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:03:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:03:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:04:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:04:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:04:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:04:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:04:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:04:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:05:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:05:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:05:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:05:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:05:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:05:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:06:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:06:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:06:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:06:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:06:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:06:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:07:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:07:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:07:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:07:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:07:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:07:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:08:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:08:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:08:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:08:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:08:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:08:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:09:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:09:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:09:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:09:53 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:09:53 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:09:53 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:10:23 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:10:23 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:10:23 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:11:22 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=58s560ms).
2025-05-23 17:11:22 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:11:22 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:11:22 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:11:52 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:11:52 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:11:52 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:13:03 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m10s991ms).
2025-05-23 17:13:03 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:13:03 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:13:03 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:13:33 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:13:33 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:13:33 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:14:13 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:14:13 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:14:13 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:14:44 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:14:44 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:14:44 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:17:17 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m33s667ms).
2025-05-23 17:17:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:17:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:17:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:17:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:17:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:17:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:18:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:18:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:18:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:18:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:18:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:18:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:19:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:19:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:19:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:19:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:19:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:19:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:20:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:20:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:20:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:20:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:20:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:20:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:20:55 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@387d661f: (connection has passed maxLifetime)
2025-05-23 17:20:55 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@f3d9294
2025-05-23 17:21:07 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2be4baeb: (connection has passed maxLifetime)
2025-05-23 17:21:07 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@b7d0366
2025-05-23 17:21:09 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@40b76b9b: (connection has passed maxLifetime)
2025-05-23 17:21:09 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@31ebacf4
2025-05-23 17:21:14 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6c0f6e37: (connection has passed maxLifetime)
2025-05-23 17:21:14 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@72e42dc3
2025-05-23 17:21:15 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@60e86cad: (connection has passed maxLifetime)
2025-05-23 17:21:15 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5b0fbe2f
2025-05-23 17:21:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:21:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:21:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:21:21 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@24d14700: (connection has passed maxLifetime)
2025-05-23 17:21:21 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@17812cc1
2025-05-23 17:21:30 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@793ea190: (connection has passed maxLifetime)
2025-05-23 17:21:30 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bec0d8f
2025-05-23 17:21:33 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@55fe553e: (connection has passed maxLifetime)
2025-05-23 17:21:33 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e4f52df
2025-05-23 17:21:35 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@33181d6: (connection has passed maxLifetime)
2025-05-23 17:21:36 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@484e1f8
2025-05-23 17:21:39 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@dd7f6b5: (connection has passed maxLifetime)
2025-05-23 17:21:39 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@112c2f26
2025-05-23 17:21:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1ac72736: (connection has passed maxLifetime)
2025-05-23 17:21:41 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@25bdaf3e
2025-05-23 17:21:42 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@38c62d70: (connection has passed maxLifetime)
2025-05-23 17:21:43 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@73275a2c
2025-05-23 17:21:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:21:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:21:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:21:51 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2877ccf7: (connection has passed maxLifetime)
2025-05-23 17:21:51 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6f82c50d
2025-05-23 17:21:55 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3c54550e: (connection has passed maxLifetime)
2025-05-23 17:21:55 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@373aec85
2025-05-23 17:21:58 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@15c26278: (connection has passed maxLifetime)
2025-05-23 17:21:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@10465f4c
2025-05-23 17:21:59 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@23bc1fb0: (connection has passed maxLifetime)
2025-05-23 17:22:00 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@69006e6f
2025-05-23 17:22:03 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1369dbc4: (connection has passed maxLifetime)
2025-05-23 17:22:03 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@34cad41d
2025-05-23 17:22:06 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@188f91d0: (connection has passed maxLifetime)
2025-05-23 17:22:06 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@32aa745a
2025-05-23 17:22:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:22:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:22:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:22:18 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@22d9d4a0: (connection has passed maxLifetime)
2025-05-23 17:22:18 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2077fffa
2025-05-23 17:22:33 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1472284e: (connection has passed maxLifetime)
2025-05-23 17:22:34 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6ce66954
2025-05-23 17:22:47 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:22:47 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:22:47 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:23:17 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:23:17 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:23:17 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:23:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:23:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:23:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:24:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:24:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:24:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:24:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:24:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:24:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:25:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:25:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:25:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:25:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:25:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:25:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:26:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:26:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:26:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:26:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:26:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:26:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:27:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:27:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:27:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:27:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:27:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:27:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:28:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:28:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:28:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:28:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:28:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:28:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:29:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:29:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:29:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:29:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:29:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:29:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:30:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:30:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:30:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:30:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:30:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:30:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:31:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:31:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:31:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:31:49 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:31:49 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:31:49 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:32:19 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:32:19 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:32:19 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:39:59 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=7m40s335ms).
2025-05-23 17:39:59 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:39:59 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:39:59 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:56:51 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m52s59ms).
2025-05-23 17:56:51 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:56:51 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:56:51 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:57:21 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:57:21 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:57:21 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 17:57:51 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:57:51 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 17:57:51 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:01:28 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m36s819ms).
2025-05-23 18:01:28 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:01:28 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:01:28 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:01:58 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:01:58 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:01:58 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:09:35 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=7m36s832ms).
2025-05-23 18:09:35 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:09:35 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:09:35 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:10:05 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:10:05 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:10:05 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:18:21 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=8m16s9ms).
2025-05-23 18:18:21 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:18:21 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:18:21 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:18:51 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:18:51 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:18:51 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:19:21 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:19:21 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:19:21 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:19:51 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:19:51 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:19:51 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:20:21 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:20:21 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:20:21 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:20:55 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:20:55 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:20:55 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:21:25 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:21:25 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:21:25 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:22:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:22:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:22:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:22:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:22:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:22:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:23:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:23:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:23:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:23:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:23:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:23:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:24:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:24:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:24:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:24:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:24:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:24:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:25:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:25:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:25:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:25:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:25:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:25:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:26:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:26:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:26:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:26:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:26:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:26:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:27:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:27:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:27:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:27:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:27:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:27:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:28:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:28:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:28:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:28:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:28:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:28:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:29:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:29:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:29:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:29:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:29:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:29:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:30:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:30:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:30:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:30:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:30:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:30:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:31:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:31:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:31:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:31:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:31:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:31:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:32:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:32:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:32:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:32:13 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@f3d9294: (connection has passed maxLifetime)
2025-05-23 18:32:13 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@43276570
2025-05-23 18:32:19 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@31ebacf4: (connection has passed maxLifetime)
2025-05-23 18:32:19 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7233dd71
2025-05-23 18:32:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:32:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:32:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:32:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@25bdaf3e: (connection has passed maxLifetime)
2025-05-23 18:32:48 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@32e5b214
2025-05-23 18:32:51 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@484e1f8: (connection has passed maxLifetime)
2025-05-23 18:32:51 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@35a11334
2025-05-23 18:32:52 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@17812cc1: (connection has passed maxLifetime)
2025-05-23 18:32:52 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5cc60148
2025-05-23 18:32:55 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@b7d0366: (connection has passed maxLifetime)
2025-05-23 18:32:55 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@ff1428e
2025-05-23 18:32:58 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5b0fbe2f: (connection has passed maxLifetime)
2025-05-23 18:32:59 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3699d1ca
2025-05-23 18:33:04 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@72e42dc3: (connection has passed maxLifetime)
2025-05-23 18:33:04 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@23d6319d
2025-05-23 18:33:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:33:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:33:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:33:10 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4e4f52df: (connection has passed maxLifetime)
2025-05-23 18:33:10 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@54fc461a
2025-05-23 18:33:12 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@69006e6f: (connection has passed maxLifetime)
2025-05-23 18:33:12 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7706a544
2025-05-23 18:33:13 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@373aec85: (connection has passed maxLifetime)
2025-05-23 18:33:13 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@75bafd1b
2025-05-23 18:33:14 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@10465f4c: (connection has passed maxLifetime)
2025-05-23 18:33:14 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7eb8624
2025-05-23 18:33:15 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4bec0d8f: (connection has passed maxLifetime)
2025-05-23 18:33:15 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7106c4a0
2025-05-23 18:33:19 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@73275a2c: (connection has passed maxLifetime)
2025-05-23 18:33:19 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@56206adf
2025-05-23 18:33:20 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@32aa745a: (connection has passed maxLifetime)
2025-05-23 18:33:20 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@30efe9e1
2025-05-23 18:33:23 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@112c2f26: (connection has passed maxLifetime)
2025-05-23 18:33:23 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e4088db
2025-05-23 18:33:33 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@34cad41d: (connection has passed maxLifetime)
2025-05-23 18:33:33 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3ddd8d57
2025-05-23 18:33:33 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6f82c50d: (connection has passed maxLifetime)
2025-05-23 18:33:34 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@17512241
2025-05-23 18:33:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:33:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:33:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:34:03 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2077fffa: (connection has passed maxLifetime)
2025-05-23 18:34:03 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@39f114d8
2025-05-23 18:34:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:34:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:34:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:34:17 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6ce66954: (connection has passed maxLifetime)
2025-05-23 18:34:18 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4aef31f4
2025-05-23 18:34:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:34:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:34:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:35:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:35:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:35:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:35:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:35:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:35:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:36:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:36:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:36:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:36:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:36:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:36:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:37:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:37:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:37:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:37:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:37:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:37:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:38:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:38:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:38:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:38:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:38:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:38:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:39:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:39:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:39:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:39:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:39:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:39:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:40:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:40:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:40:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:40:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:40:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:40:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:41:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:41:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:41:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:41:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:41:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:41:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:42:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:42:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:42:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:42:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:42:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:42:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:43:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:43:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:43:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:43:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:43:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:43:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:44:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:44:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:44:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:44:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:44:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:44:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:45:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:45:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:45:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:45:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:45:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:45:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:46:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:46:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:46:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:46:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:46:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:46:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:47:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:47:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:47:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:47:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:47:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:47:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:48:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:48:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:48:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:48:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:48:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:48:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:49:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:49:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:49:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:49:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:49:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:49:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:50:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:50:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:50:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:50:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:50:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:50:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:51:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:51:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:51:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:51:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:51:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:51:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:52:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:52:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:52:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:52:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:52:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:52:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:53:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:53:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:53:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:53:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:53:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:53:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:54:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:54:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:54:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:54:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:54:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:54:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:55:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:55:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:55:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:55:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:55:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:55:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:56:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:56:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:56:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:56:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:56:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:56:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:57:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:57:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:57:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:57:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:57:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:57:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:58:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:58:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:58:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:58:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:58:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:58:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:59:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:59:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:59:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 18:59:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:59:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 18:59:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:00:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:00:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:00:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:00:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:00:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:00:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:01:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:01:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:01:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:01:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:01:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:01:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:01:45 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7233dd71: (connection has passed maxLifetime)
2025-05-23 19:01:45 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@60e8314c
2025-05-23 19:01:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@43276570: (connection has passed maxLifetime)
2025-05-23 19:01:50 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4c7d637d
2025-05-23 19:02:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:02:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:02:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:02:13 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@ff1428e: (connection has passed maxLifetime)
2025-05-23 19:02:13 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5b304127
2025-05-23 19:02:22 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3699d1ca: (connection has passed maxLifetime)
2025-05-23 19:02:22 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3b1f3ee0
2025-05-23 19:02:36 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@54fc461a: (connection has passed maxLifetime)
2025-05-23 19:02:36 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4d3022d9
2025-05-23 19:02:37 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7706a544: (connection has passed maxLifetime)
2025-05-23 19:02:37 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5cc60148: (connection has passed maxLifetime)
2025-05-23 19:02:37 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 19:02:37 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5abaad61
2025-05-23 19:02:37 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6329e1f5
2025-05-23 19:02:37 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:02:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:02:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:02:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:02:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@32e5b214: (connection has passed maxLifetime)
2025-05-23 19:02:41 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@eb61c93
2025-05-23 19:02:43 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@56206adf: (connection has passed maxLifetime)
2025-05-23 19:02:43 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@22ae77bf
2025-05-23 19:02:43 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@35a11334: (connection has passed maxLifetime)
2025-05-23 19:02:43 - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-05-23 19:02:43 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7eb8624: (connection has passed maxLifetime)
2025-05-23 19:02:43 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@c4003c9
2025-05-23 19:02:44 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@41bb69b8
2025-05-23 19:02:44 - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:02:48 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3ddd8d57: (connection has passed maxLifetime)
2025-05-23 19:02:49 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@829ea8b
2025-05-23 19:02:50 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2e4088db: (connection has passed maxLifetime)
2025-05-23 19:02:50 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@433e6216
2025-05-23 19:02:58 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@23d6319d: (connection has passed maxLifetime)
2025-05-23 19:02:58 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7b6a4a72
2025-05-23 19:02:59 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7106c4a0: (connection has passed maxLifetime)
2025-05-23 19:03:00 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3ce313f
2025-05-23 19:03:02 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@30efe9e1: (connection has passed maxLifetime)
2025-05-23 19:03:02 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2f66c87a
2025-05-23 19:03:03 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@75bafd1b: (connection has passed maxLifetime)
2025-05-23 19:03:03 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@19c25b5b
2025-05-23 19:03:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:03:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:03:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:03:25 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@17512241: (connection has passed maxLifetime)
2025-05-23 19:03:25 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@30485367
2025-05-23 19:03:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:03:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:03:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:03:41 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@39f114d8: (connection has passed maxLifetime)
2025-05-23 19:03:41 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@705c2b94
2025-05-23 19:04:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:04:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:04:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:04:16 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4aef31f4: (connection has passed maxLifetime)
2025-05-23 19:04:16 - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@d20f1ce
2025-05-23 19:04:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:04:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:04:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:05:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:05:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:05:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:05:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:05:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:05:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:06:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:06:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:06:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:06:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:06:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:06:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:07:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:07:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:07:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:07:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:07:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:07:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:08:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:08:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:08:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:08:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:08:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:08:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:09:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:09:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:09:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:09:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:09:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:09:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:10:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:10:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:10:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:10:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:10:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:10:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:11:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:11:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:11:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:11:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:11:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:11:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:12:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:12:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:12:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:12:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:12:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:12:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:13:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:13:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:13:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:13:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:13:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:13:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:14:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:14:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:14:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:14:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:14:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:14:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:15:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:15:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:15:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:15:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:15:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:15:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:16:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:16:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:16:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:16:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:16:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:16:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:17:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:17:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:17:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:17:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:17:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:17:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:18:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:18:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:18:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:18:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:18:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:18:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:19:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:19:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:19:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:19:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:19:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:19:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:20:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:20:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:20:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:20:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:20:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:20:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:21:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:21:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:21:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:21:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:21:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:21:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:22:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:22:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:22:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:22:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:22:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:22:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:23:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:23:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:23:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:23:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:23:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:23:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:24:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:24:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:24:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:24:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:24:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:24:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:25:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:25:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:25:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:25:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:25:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:25:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:26:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:26:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:26:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:26:38 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:26:38 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:26:38 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:27:08 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:27:08 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:27:08 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:27:39 - HikariPool-1 - Before cleanup stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:27:39 - HikariPool-1 - After cleanup  stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:27:39 - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-05-23 19:27:40 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-23 19:27:40 - HikariPool-1 - Shutdown initiated...
2025-05-23 19:27:40 - HikariPool-1 - Before shutdown stats (total=20, active=0, idle=20, waiting=0)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@60e8314c: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4c7d637d: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5b304127: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3b1f3ee0: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4d3022d9: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5abaad61: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6329e1f5: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@eb61c93: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@22ae77bf: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@c4003c9: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@41bb69b8: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@829ea8b: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@433e6216: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7b6a4a72: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@3ce313f: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2f66c87a: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@19c25b5b: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@30485367: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@705c2b94: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@d20f1ce: (connection evicted)
2025-05-23 19:27:40 - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-05-23 19:27:40 - HikariPool-1 - Shutdown completed.
