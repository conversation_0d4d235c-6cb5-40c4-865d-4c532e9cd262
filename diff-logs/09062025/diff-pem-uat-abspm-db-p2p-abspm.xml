<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1749466085756-52">
        <createTable tableName="update_email_audit">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="changed_by_email" type="VARCHAR(255)"/>
            <column name="changed_by_id" type="VARCHAR(255)"/>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="current_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="from_email" type="VARCHAR(255)"/>
            <column name="to_email" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-53">
        <createTable tableName="user_metadata_limit_ledger">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="company_code" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="expense_metadata_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="month" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="expense_count" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="sum_value" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="BIGINT"/>
            <column name="count_value" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-54">
        <addColumn tableName="approver_audit">
            <column name="approver_id" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-55">
        <createIndex associatedWith="" indexName="Status_indx" tableName="report_state">
            <column name="status"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-56">
        <createIndex associatedWith="" indexName="idx_cr_usr_id" tableName="expense">
            <column name="creating_user_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-57">
        <dropTable tableName="sp_execution_details"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-58">
        <dropColumn columnName="locked" tableName="expense_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-1">
        <addNotNullConstraint columnDataType="boolean" columnName="assign_unapproved" tableName="approval_delegation" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-2">
        <addNotNullConstraint columnDataType="boolean" columnName="can_exceed_limit" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-3">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="approval_delegation" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-4">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="approver" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-5">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="document_identifier" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-6">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-7">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-8">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="location" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-9">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-10">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="payment" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-11">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="approval_delegation" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-12">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="approver" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-13">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-14">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-15">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="location" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-16">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-17">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="current_index" tableName="document_identifier" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-18">
        <addNotNullConstraint columnDataType="int(10)" columnName="expense_count_limit" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-19">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="expense_metadata_id" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-20">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="expense_metadata_id" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-21">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="expense_report_id" tableName="payment" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-22">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="expense_subgroup_id" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-23">
        <modifyDataType columnName="first_name" newDataType="varchar(30)" tableName="expense_vouchers_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-24">
        <addNotNullConstraint columnDataType="boolean" columnName="is_date_range_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-25">
        <addNotNullConstraint columnDataType="boolean" columnName="is_destination_location_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-26">
        <addNotNullConstraint columnDataType="boolean" columnName="is_expense_identifier_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-27">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="approval_delegation" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-28">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="approver" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-29">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-30">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-31">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="location" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-32">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-33">
        <addNotNullConstraint columnDataType="boolean" columnName="is_gst_entry_allowed" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-34">
        <addNotNullConstraint columnDataType="boolean" columnName="is_invoice_required" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-35">
        <addNotNullConstraint columnDataType="boolean" columnName="is_location_required" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-36">
        <addNotNullConstraint columnDataType="boolean" columnName="is_mobility_descriptor_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-37">
        <addNotNullConstraint columnDataType="boolean" columnName="is_per_diem_allowed" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-38">
        <addNotNullConstraint columnDataType="boolean" columnName="is_source_location_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-39">
        <addNotNullConstraint columnDataType="boolean" columnName="is_standard_deduction_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-40">
        <addNotNullConstraint columnDataType="boolean" columnName="is_transport_descriptor_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-41">
        <addNotNullConstraint columnDataType="boolean" columnName="is_travel_descriptor_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-42">
        <addNotNullConstraint columnDataType="boolean" columnName="is_unit_rate_applicable" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-43">
        <modifyDataType columnName="last_name" newDataType="varchar(30)" tableName="expense_vouchers_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-44">
        <addNotNullConstraint columnDataType="double(22,2)" columnName="limit_amount" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-45">
        <addNotNullConstraint columnDataType="boolean" columnName="merchant_required" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-46">
        <modifyDataType columnName="middle_name" newDataType="varchar(30)" tableName="expense_vouchers_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-47">
        <addNotNullConstraint columnDataType="int(10)" columnName="month" tableName="document_identifier" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-48">
        <addNotNullConstraint columnDataType="double(22,2)" columnName="paid_amount" tableName="payment" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-49">
        <addNotNullConstraint columnDataType="int(10)" columnName="rules_count" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-50">
        <addNotNullConstraint columnDataType="double(22,2)" columnName="tds_amount" tableName="payment" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466085756-51">
        <addNotNullConstraint columnDataType="int(10)" columnName="year" tableName="document_identifier" validate="true"/>
    </changeSet>
</databaseChangeLog>
