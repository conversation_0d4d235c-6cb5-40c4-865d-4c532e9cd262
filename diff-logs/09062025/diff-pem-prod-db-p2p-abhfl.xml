<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1749466038369-53">
        <dropForeignKeyConstraint baseTableName="approval_delegation_history" constraintName="fk_approval_delegation_id"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-54">
        <dropForeignKeyConstraint baseTableName="approver_history" constraintName="fk_approver_id"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-55">
        <dropForeignKeyConstraint baseTableName="location_history" constraintName="fk_location_id"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-49">
        <createTable tableName="user_metadata_limit_ledger">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="company_code" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="expense_metadata_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="month" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="expense_count" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="sum_value" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="BIGINT"/>
            <column name="count_value" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-50">
        <addColumn tableName="approver_audit">
            <column name="approver_id" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-51">
        <createIndex associatedWith="" indexName="Status_indx" tableName="report_state">
            <column name="status"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-52">
        <createIndex associatedWith="" indexName="idx_cr_usr_id" tableName="expense">
            <column name="creating_user_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-56">
        <dropTable tableName="approval_delegation_history"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-57">
        <dropTable tableName="approver_history"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-58">
        <dropTable tableName="location_history"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-59">
        <dropTable tableName="sp_execution_details"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-60">
        <dropColumn columnName="expense_month" tableName="expense"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-1">
        <addNotNullConstraint columnDataType="boolean" columnName="assign_unapproved" tableName="approval_delegation" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-2">
        <addNotNullConstraint columnDataType="boolean" columnName="can_exceed_limit" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-3">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="approval_delegation" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-4">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="approver" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-5">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="document_identifier" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-6">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-7">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-8">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="location" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-9">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-10">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="company_code" tableName="payment" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-11">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="approval_delegation" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-12">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="approver" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-13">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-14">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-15">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="location" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-16">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="creating_user_id" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-17">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="current_index" tableName="document_identifier" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-18">
        <addNotNullConstraint columnDataType="int(10)" columnName="expense_count_limit" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-19">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="expense_metadata_id" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-20">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="expense_metadata_id" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-21">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="expense_report_id" tableName="payment" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-22">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="expense_subgroup_id" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-23">
        <addNotNullConstraint columnDataType="boolean" columnName="is_date_range_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-24">
        <addNotNullConstraint columnDataType="boolean" columnName="is_destination_location_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-25">
        <addNotNullConstraint columnDataType="boolean" columnName="is_expense_identifier_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-26">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="approval_delegation" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-27">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="approver" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-28">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-29">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-30">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="location" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-31">
        <addNotNullConstraint columnDataType="boolean" columnName="is_frozen" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-32">
        <addNotNullConstraint columnDataType="boolean" columnName="is_gst_entry_allowed" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-33">
        <addNotNullConstraint columnDataType="boolean" columnName="is_invoice_required" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-34">
        <addNotNullConstraint columnDataType="boolean" columnName="is_location_required" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-35">
        <addNotNullConstraint columnDataType="boolean" columnName="is_mobility_descriptor_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-36">
        <addNotNullConstraint columnDataType="boolean" columnName="is_per_diem_allowed" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-37">
        <addNotNullConstraint columnDataType="boolean" columnName="is_source_location_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-38">
        <addNotNullConstraint columnDataType="boolean" columnName="is_standard_deduction_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-39">
        <addNotNullConstraint columnDataType="boolean" columnName="is_transport_descriptor_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-40">
        <addNotNullConstraint columnDataType="boolean" columnName="is_travel_descriptor_applicable" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-41">
        <addNotNullConstraint columnDataType="boolean" columnName="is_unit_rate_applicable" tableName="expense_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-42">
        <addNotNullConstraint columnDataType="double(22,2)" columnName="limit_amount" tableName="metadata_limit_rule" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-43">
        <addNotNullConstraint columnDataType="boolean" columnName="merchant_required" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-44">
        <addNotNullConstraint columnDataType="int(10)" columnName="month" tableName="document_identifier" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-45">
        <addNotNullConstraint columnDataType="double(22,2)" columnName="paid_amount" tableName="payment" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-46">
        <addNotNullConstraint columnDataType="int(10)" columnName="rules_count" tableName="expense_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-47">
        <addNotNullConstraint columnDataType="double(22,2)" columnName="tds_amount" tableName="payment" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466038369-48">
        <addNotNullConstraint columnDataType="int(10)" columnName="year" tableName="document_identifier" validate="true"/>
    </changeSet>
</databaseChangeLog>
