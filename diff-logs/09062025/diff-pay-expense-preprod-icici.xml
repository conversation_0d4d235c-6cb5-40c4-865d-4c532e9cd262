<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1749466141532-6">
        <createTable tableName="update_email_audit">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="changed_by_email" type="VARCHAR(255)"/>
            <column name="changed_by_id" type="VARCHAR(255)"/>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="current_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="from_email" type="VARCHAR(255)"/>
            <column name="to_email" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-7">
        <createTable tableName="user_metadata_limit_ledger">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="company_code" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="expense_metadata_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="month" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="expense_count" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="sum_value" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="BIGINT"/>
            <column name="count_value" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-8">
        <addColumn tableName="approver_audit">
            <column name="approver_id" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-9">
        <addColumn tableName="expense">
            <column name="buyer_gstin" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-12">
        <addUniqueConstraint columnNames="company_code, expense_report_id, payment_reference" constraintName="unique_idx" tableName="payment"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-13">
        <createIndex associatedWith="" indexName="Status_indx" tableName="report_state">
            <column name="status"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-14">
        <createIndex associatedWith="" indexName="idx_cr_usr_id" tableName="expense">
            <column name="creating_user_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-15">
        <createIndex associatedWith="" indexName="idx_creating_user_id" tableName="expense_report">
            <column name="creating_user_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-16">
        <createIndex associatedWith="" indexName="idx_report_status" tableName="expense_report">
            <column name="report_status"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-17">
        <dropTable tableName="expense_report_backup_23022022"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-1">
        <dropNotNullConstraint columnDataType="bigint(19)" columnName="erp_comp_code" tableName="payment"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-2">
        <modifyDataType columnName="expense_type" newDataType="varchar(255)" tableName="expense_vouchers_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-3">
        <dropDefaultValue columnDataType="boolean" columnName="has_been_validated" tableName="expense"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-4">
        <modifyDataType columnName="job_band" newDataType="bigint(19)" tableName="entitlement_details"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749466141532-5">
        <dropNotNullConstraint columnDataType="bigint(19)" columnName="save_time" tableName="report_state"/>
    </changeSet>
</databaseChangeLog>
