<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1749471342016-2">
        <addUniqueConstraint columnNames="company_code, document_identifier" constraintName="idx_doc_identifier" tableName="expense_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471342016-3">
        <addUniqueConstraint columnNames="expense_report_id, level, save_time" constraintName="idx_rep_state" tableName="report_state"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471342016-1">
        <addNotNullConstraint columnDataType="bigint(19)" columnName="job_band" tableName="entitlement_details" validate="true"/>
    </changeSet>
</databaseChangeLog>
