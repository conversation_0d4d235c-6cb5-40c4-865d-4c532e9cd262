<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1749471281766-1">
        <modifyDataType columnName="assign_unapproved" newDataType="boolean" tableName="approval_delegation"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-2">
        <modifyDataType columnName="can_exceed_limit" newDataType="boolean" tableName="expense_rule"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-3">
        <modifyDataType columnName="first_name" newDataType="varchar(30)" tableName="expense_vouchers_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-4">
        <modifyDataType columnName="is_date_range_applicable" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-5">
        <modifyDataType columnName="is_destination_location_applicable" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-6">
        <modifyDataType columnName="is_expense_identifier_applicable" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-7">
        <modifyDataType columnName="is_frozen" newDataType="boolean" tableName="approval_delegation"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-8">
        <modifyDataType columnName="is_frozen" newDataType="boolean" tableName="approver"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-9">
        <modifyDataType columnName="is_frozen" newDataType="boolean" tableName="expense_rule"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-10">
        <modifyDataType columnName="is_frozen" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-11">
        <modifyDataType columnName="is_frozen" newDataType="boolean" tableName="location"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-12">
        <modifyDataType columnName="is_frozen" newDataType="boolean" tableName="metadata_limit_rule"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-13">
        <modifyDataType columnName="is_gst_entry_allowed" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-14">
        <modifyDataType columnName="is_invoice_required" newDataType="boolean" tableName="expense_rule"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-15">
        <modifyDataType columnName="is_location_required" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-16">
        <modifyDataType columnName="is_mobility_descriptor_applicable" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-17">
        <modifyDataType columnName="is_per_diem_allowed" newDataType="boolean" tableName="expense_rule"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-18">
        <modifyDataType columnName="is_source_location_applicable" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-19">
        <modifyDataType columnName="is_standard_deduction_applicable" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-20">
        <modifyDataType columnName="is_transport_descriptor_applicable" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-21">
        <modifyDataType columnName="is_travel_descriptor_applicable" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-22">
        <modifyDataType columnName="is_unit_rate_applicable" newDataType="boolean" tableName="expense_rule"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-23">
        <modifyDataType columnName="last_name" newDataType="varchar(30)" tableName="expense_vouchers_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-24">
        <modifyDataType columnName="merchant_required" newDataType="boolean" tableName="expense_subgroup"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749471281766-25">
        <modifyDataType columnName="middle_name" newDataType="varchar(30)" tableName="expense_vouchers_report"/>
    </changeSet>
</databaseChangeLog>
