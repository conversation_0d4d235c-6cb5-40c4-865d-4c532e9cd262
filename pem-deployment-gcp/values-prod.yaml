# Default values for pem-deployment-gcp.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

namepsace: "default"

image:
  repository: asia-south1-docker.pkg.dev/master-projects-399408/pay-expense/backend
  #repository: asia-south1-docker.pkg.dev/$GCP_PROJECT_ID/$IMAGE_REPO/pay-expense-backend
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: image_tag

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "pem-prod-app-sa"

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8080

livenessProbe:
   httpGet:
    path: /health
    port: http

readinessProbe:
  httpGet:
   path: /health
   port: http

configMap:
  enabled: false
  data: {}
    # property-like keys; each key maps to a simple value
    # player_initial_lives: "3"
    # ui_properties_file_name: "user-interface.properties"
    # # file-like keys
    # game.properties: |
    #   enemy.types=aliens,monsters
    #   player.maximum-lives=5
    # user-interface.properties: |
    #   color.good=purple
    #   color.bad=yellow
    #   allow.textmode=true

secret:
  enabled: enable
  stringData: 
    db-password: "bitbucket-db-password-prod"
    aws_access_key: "bitbucket_aws_access_key"
    aws_secret_key: "bitbucket_aws_secret_key"
    # auth-extra-groups: "system:bootstrappers:kubeadm:default-node-token"
    # expiration: "2020-09-13T04:39:10Z"
    # usage-bootstrap-authentication: "true"
    # usage-bootstrap-signing: "true"

# healthCheck:
#   checkIntervalSec: 15
#   port: 8080
#   type: HTTP
#   requestPath: /healthz

startupProbe: {}
  # failureThreshold: 30
  # httpGet:
  #   path: /actuator/health
  #   port: http
  #   scheme: HTTP
  # periodSeconds: 10
  # successThreshold: 1
  # timeoutSeconds: 1

containerPorts:
  - name: http
    containerPort: 8080
    protocol: TCP 

env:
  - name: DB_URL
    value: "****************************************"
  - name: DB_USERNAME
    value: "icici_pem_adm"
  - name: DB_PASSWORD
    valueFrom:
      secretKeyRef:
        name: pembackend-pem-deployment-gcp #secret name is same as chart name 
        key: db-password
  - name: FILE_UPLOAD_LIMIT
    value: "10MB"
  - name: JWT_SECRET
    value: "1E223539-0D5B-4279-8F9A-B3B13BDC7A29-DA01E40D-6806-40FA-B280-44ACB4902845"
  - name: PAY_EXPENSE_PRODUCT_ID
    value: '4'
  - name: DB_DIALECT
    value: "org.hibernate.dialect.MySQL5InnoDBDialect"
  - name: DB_SHOW_SQL
    value: 'false'
  - name: DB_DDL_AUTO
    value: "none"
  - name: DB_ENGINE
    value: "innodb"
  - name: CEM_URL
    value: "http://cem.payinvoice.in/api/v1"
  - name: AWS_ENDPOINT_URL
    value: "https://payexpense.s3.ap-south-1.amazonaws.com"
  - name: AWS_REGION
    value: "ap-south-1"
  - name: AWS_ACCESS_KEY
    valueFrom:
      secretKeyRef:
        name: pembackend-pem-deployment-gcp #secret name is same as chart name 
        key: aws_access_key
  - name: AWS_SECRET_KEY
    valueFrom:
      secretKeyRef:
        name: pembackend-pem-deployment-gcp #secret name is same as chart name 
        key: aws_secret_key
  - name: AWS_BUCKET_NAME
    value: "payexpense"
  - name: ACTIVE_PROFILE
    value: "ses-gcp"
  - name: BUCKET_NAME
    value: 'payexpense'
  - name: AWS_ROOT_DIRECTORY
    value: "payexpense"
  - name: ALLOWED_ORIGINS
    value: "http://localhost:4200,http://icici.payexpense.online,https://icici.payexpense.online"
  - name: PAY_EXPENSE_CUSTOMER_ID
    value: "4423"
  - name: COMMUNICATION_ENGINE_URL
    value: "https://gst.taxgenie.online/communication-engine/"
  - name: APPROVAL_EMAIL_TEMPLATE_ID
    value: "pe-approval-format"
  - name: ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID
    value: "pe-acknowledgement-format"
  - name: PAY_EXPENSE_SENDER_EMAIL
    value: "<EMAIL>"
  - name: SENDBACK_EMAIL_TEMPLATE_ID
    value: "pe-sentback-for-creator"
  - name: SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID
    value: "pe-submit-for-creator"
  - name: SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID
    value: "pe-submit-for-approver"
  - name: APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID
    value: "pe-approved-for-creator"
  - name: APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID
    value: "pe-approved-for-approver"
  - name: POSTED_EMAIL_TEMPLATE_ID
    value: "pe-posted-for-creator"
  - name: PAID_EMAIL_TEMPLATE_ID
    value: "pe-paid-for-creator"
  - name: NEW_USER_WELCOME_EMAIL_TEMPLATE_ID
    value: "pe-new-user-welcome"
  - name: SENDBACK_REMINDER_EMAIL_TEMPLATE_ID
    value: "pe-sent-back-reminder"
  - name: APPROVAL_REMINDER_EMAIL_TEMPLATE_ID
    value: "pe-approval-reminder"
  - name: GCP_BUCKET
    value: "pay-expense"

ingress:
  enabled: false
  className: "gce"
  annotations:
    kubernetes.io/ingress.class: gce
    kubernetes.io/ingress.global-static-ip-name: payinvoice-static-ip
    networking.gke.io/managed-certificates: managed-cert
    kubernetes.io/ingress.allow-http: "true"
  hosts:
    - host: pem.payinvoice.in
      paths:
        - path: /
          pathType: Prefix
          backend:
             service:
               name: pembackend-pem-deployment-gcp
               port:
                 number: 8080
    - host: cem.payinvoice.in
      paths:
        - path: /
          pathType: Prefix
          backend:
           service:
             name: cembackend-cem-deployment-gcp
             port:
               number: 8080
  domainNames: 
    - domainName: pem.payinvoice.in
    - domainName: cem.payinvoice.in     
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

resourceQuota: {}
#Use this configuration to specify Resource Limit on a specific Namespace
#Add the limits on CPU and Memory usage for the desired Namesapce.
#If any resource try to utilize more resources speciffied here in that namespace, it will not allow that pod to run in the ns
#To add quota on ns remove curly braces and set the value for enbaled to true
#By default it is false, which means it will not create any quota Limit at ns level.
#uncomment the lines below enabled to configure the desired quota limits.
  #  enabled: false
  #  namespace: customer1
  #  cpuLimit: "1"
  #  memLimit: "1Gi"
  #  cpuReq: "0.5"
  #  memRequest: "500Mi"


#specify network policy for the desired namespace.
networkPolicy:
#To enable network policy set enable configuration below to true.
  # enabled: false
  # accessLabels: 
  #   - app: data  # Pod Label to which you want to apply the network policy
  # ingressRules:  #Add the ingress network policy rules to be applied on Namespace
    # - from:
    #     - ipBlock:
    #         cidr: **********/16
    #         except:
    #           - **********/24
    #     - namespaceSelector:
    #         matchLabels:
    #           project: myproject
    #     - podSelector:
    #         matchLabels:
    #           role: frontend
    #   ports:
    #     - protocol: TCP
    #       port: 6379
  egressRules: {}  #Add egress network policy rules to be applied on Namespace.
    # - to:
    #     - ipBlock:
    #         cidr: 10.0.0.0/24
    #   ports:
    #     - protocol: TCP
    #       port: 5978


#Create a role into specified Namespace RBAC control for specified namespace
#Note give minimal permission to the user so that you can extend them as per your requirment
role:
  enabled: false # To enable the roles uncomment the rules block and set value of enabled to true
  # rules: 
    # - apiGroups: [""]
    #   resources: ["pods", "services", "serviceaccounts"]
    #   verbs: ["update", "create", "delete", "get", "watch", "list"]
    # - apiGroups: ["apps"]
    #   resources: ["deployments"]
    #   verbs: ["update", "create", "delete", "get", "watch", "list"]

RoleBinding:
  enabled: false # Enable the rolebinding by setting this value to true or false.
  # subjects:
  # # You can specify more than one "subject"
  # - kind: User
  #   name: <EMAIL> # "name" is case sensitive and should be IAM user or SA name
  #   apiGroup: rbac.authorization.k8s.io
  # roleRef:
  # # "roleRef" specifies the binding to a Role 
  #   kind: Role #this must be Role
  #   name: developer # this must match the name of the Role you wish to bind to
  #   apiGroup: rbac.authorization.k8s.io

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
