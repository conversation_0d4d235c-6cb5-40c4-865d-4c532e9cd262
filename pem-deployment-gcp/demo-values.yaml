# Default values for pem-deployment-gcp.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: asia-south1-docker.pkg.dev/noble-stratum-393405/pay-expense/pay-expense-backend
  #repository: asia-south1-docker.pkg.dev/$GCP_PROJECT_ID/$IMAGE_REPO/pay-expense-backend
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "1.320.8"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8080

livenessProbe: {}
  #  httpGet:
  #  path: /
  #  port: http
readinessProbe: {}
  # httpGet:
  #  path: /
  #  port: http

startupProbe: {}
  # failureThreshold: 30
  # httpGet:
  #   path: /actuator/health
  #   port: http
  #   scheme: HTTP
  # periodSeconds: 10
  # successThreshold: 1
  # timeoutSeconds: 1

containerPorts:
  - name: http
    containerPort: 8080
    protocol: TCP 

env:
  - name: DB_URL
    value: "*************************************************"
  - name: DB_USERNAME
    value: "pe_backend"
  - name: DB_PASSWORD
    value: "TaxGenie@2022!@#"
  - name: FILE_UPLOAD_LIMIT
    value: "10MB"
  - name: JWT_SECRET
    value: "1E223539-0D5B-4279-8F9A-B3B13BDC7A29-DA01E40D-6806-40FA-B280-44ACB4902845"
  - name: PAY_EXPENSE_PRODUCT_ID
    value: '4'
  - name: DB_DIALECT
    value: "org.hibernate.dialect.MySQL5InnoDBDialect"
  - name: DB_SHOW_SQL
    value: 'false'
  - name: DB_DDL_AUTO
    value: "update"
  - name: DB_ENGINE
    value: "innodb"
  - name: CEM_URL
    value: "http://*************:9005/api/v1"
  - name: AWS_ENDPOINT_URL
    value: "https://payexpense.s3.ap-south-1.amazonaws.com"
  - name: AWS_REGION
    value: "ap-south-1"
  - name: AWS_ACCESS_KEY
    value: bitbucket_AWS_ACCESS_KEY
  - name: AWS_SECRET_KEY
    value: bitbucket_AWS_SECRET_KEY
  - name: AWS_BUCKET_NAME
    value: "payexpense"
  - name: ACTIVE_PROFILE
    value: "tg-internal-gcp"
  - name: AWS_ROOT_DIRECTORY
    value: "payexpense"
  - name: ALLOWED_ORIGINS
    value: "http://localhost:4200,http://*************:9002,http://*************:10002,https://payexpense-uat.abfldirect.com,http://procurement.dev.payinvoice.in"
  - name: PAY_EXPENSE_CUSTOMER_ID
    value: "12741"
  - name: COMMUNICATION_ENGINE_URL
    value: "https://dev.taxgenie.online/communication-engine"
  - name: SENDBACK_EMAIL_TEMPLATE_ID
    value: "pe-sentback-format"
  - name: APPROVAL_EMAIL_TEMPLATE_ID
    value: "pe-approval-format"
  - name: ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID
    value: "pe-acknowledgement-format"
  - name: PAY_EXPENSE_SENDER_EMAIL
    value: "<EMAIL>"
  - name: SENDBACK_EMAIL_TEMPLATE_ID
    value: "pe-sentback-for-creator"
  - name: SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID
    value: "pe-submit-for-creator"
  - name: SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID
    value: "pe-submit-for-approver"
  - name: APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID
    value: "pe-approved-for-creator"
  - name: APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID
    value: "pe-approved-for-approver"
  - name: POSTED_EMAIL_TEMPLATE_ID
    value: "pe-posted-for-creator"
  - name: PAID_EMAIL_TEMPLATE_ID
    value: "pe-paid-for-creator"
  - name: NEW_USER_WELCOME_EMAIL_TEMPLATE_ID
    value: "pe-new-user-welcome"
  - name: SENDBACK_REMINDER_EMAIL_TEMPLATE_ID
    value: "pe-sent-back-reminder"
  - name: APPROVAL_REMINDER_EMAIL_TEMPLATE_ID
    value: "pe-approval-reminder"

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
