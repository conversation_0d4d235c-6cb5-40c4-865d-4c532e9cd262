{{- if .Values.resourceQuota.enabled -}}
apiVersion: v1
kind: ResourceQuota
metadata:
  name: {{ include "pem-deployment-gcp.fullname" . }}
  namespace: {{ .Values.resourceQuota.namespace }}
spec:
  hard:
    limits.cpu: {{ .Values.resourceQuota.cpuLimit }}
    limits.memory: {{ .Values.resourceQuota.memLimit }}
    requests.cpu: {{ .Values.resourceQuota.cpuReq }}
    requests.memory: {{ .Values.resourceQuota.memRequest }}
{{- end }}    