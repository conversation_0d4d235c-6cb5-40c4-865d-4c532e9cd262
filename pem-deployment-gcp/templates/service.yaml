apiVersion: v1
kind: Service
metadata:
  name: {{ include "pem-deployment-gcp.fullname" . }}
  annotations: 
    cloud.google.com/neg: '{"ingress": true}'
    kubernetes.io/ingress.class: "gce"
  labels:
    {{- include "pem-deployment-gcp.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "pem-deployment-gcp.selectorLabels" . | nindent 4 }}
