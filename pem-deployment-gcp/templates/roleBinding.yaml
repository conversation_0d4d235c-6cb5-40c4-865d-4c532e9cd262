{{- if .Values.RoleBinding.enabled -}}
apiVersion: rbac.authorization.k8s.io/v1
# This role binding allows "jane" to read pods in the "specified" namespace.
# You need to already have a desire Role  in that namespace.
kind: RoleBinding
metadata:
  name: {{ include "pem-deployment-gcp.fullname" . }}-rolebinding
  namespace: {{ .Values.namepsace }}
roleRef:
 {{- toYaml $.Values.RoleBinding.roleRef | nindent 2 }}
subjects:
 {{- toYaml $.Values.RoleBinding.subjects | nindent 2 }}
{{- end }}