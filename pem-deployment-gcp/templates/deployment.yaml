apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "pem-deployment-gcp.fullname" . }}
  labels:
    {{- include "pem-deployment-gcp.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "pem-deployment-gcp.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "pem-deployment-gcp.selectorLabels" . | nindent 8 }}
    spec:
      # revisionHistoryLimit: 2
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "pem-deployment-gcp.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      nodeSelector:
        iam.gke.io/gke-metadata-server-enabled: "true"
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.env }}
          env:
            {{- toYaml . | nindent 10}}
          {{- end }}      
          {{- with .Values.containerPorts }}    
          ports:
            {{- toYaml . | nindent 10 }}  
          {{- end }}   
          {{- with .Values.livenessProbe }} 
          livenessProbe:
            {{- toYaml . | nindent 12  }}
          {{- end }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
          {{- toYaml . | nindent 10 }}
          {{- end }}  
          {{- with .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml . | nindent 12  }}
          {{- end }}
          {{- with .Values.startupProbe }}    
          startupProbe:
            {{- toYaml . | nindent 12  }}
          {{- end }} 
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      
