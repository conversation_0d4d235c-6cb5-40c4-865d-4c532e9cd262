apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "pem-deployment-gcp.fullname" . }}-test-connection"
  labels:
    {{- include "pem-deployment-gcp.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "pem-deployment-gcp.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
