#!/usr/bin/env bash

# Create output folder if it doesn't exist
mkdir -p diff-logs

# Local reference DB
REFERENCE_URL="**************************************"
REFERENCE_USERNAME="root"
REFERENCE_PASSWORD="root"

# JDBC driver
DRIVER="com.mysql.cj.jdbc.Driver"

# Define parallel arrays for compatibility
ENTITIES=("p2p-abhfl" "p2p-abcd" "p2p-abml" "p2p-abspm" "icici")
HOSTS=(
  "p2p-abhfl-uat.cs6hneehdqgn.ap-south-1.rds.amazonaws.com"
  "p2p-abcd-uat.cs6hneehdqgn.ap-south-1.rds.amazonaws.com"
  "p2p-abml-uat.cs6hneehdqgn.ap-south-1.rds.amazonaws.com"
  "p2p-abspm-uat.cs6hneehdqgn.ap-south-1.rds.amazonaws.com"
  "*************"
)
DBS=(
  "pem_prod_db"
  "pem_uat_abcd_db"
  "pem_uat_abml_db"
  "pem_uat_abspm_db"
  "pay_expense_preprod"
)

# Credentials
default_username="admin"
default_password="Admin123"
icici_username="taxgenie"
icici_password='taxgenie*#8102*$'

# Loop through all targets by index
for i in "${!ENTITIES[@]}"; do
  key="${ENTITIES[i]}"
  host="${HOSTS[i]}"
  db="${DBS[i]}"
  file_name="diff-${db//_/-}-$key.xml"
  file_path="diff-logs/$file_name"

  # Choose credentials based on entity
  if [[ "$key" == "icici" ]]; then
    username="$icici_username"
    password="$icici_password"
  else
    username="$default_username"
    password="$default_password"
  fi

  # Informative messages
  echo
  echo "🔁 [Entity: $key] Running diff: local:pem_uat_db ↔ $db@$host"
  echo "🔧 Output file: $file_path"

  # Run Liquibase diffChangeLog
  liquibase \
    --referenceUrl="$REFERENCE_URL" \
    --referenceUsername="$REFERENCE_USERNAME" \
    --referencePassword="$REFERENCE_PASSWORD" \
    --url="***************************" \
    --username="$username" \
    --password="$password" \
    --driver="$DRIVER" \
    --changeLogFile="$file_path" \
    diffChangeLog

  echo "✅ [Entity: $key] Completed diff"
done

echo -e "\n🎉 All diffs complete. Check the 'diff-logs/' directory."
